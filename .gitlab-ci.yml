image:
  name: docker/compose:1.25.4
  entrypoint: [""]

services:
  - docker:24.0.5-dind

stages:
  - build
  - deploy

variables:
  DEV_IMAGE_TAG: ${DOCKER_REGISTRY_HOST}/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/dev:$CI_COMMIT_SHORT_SHA
  PROD_IMAGE_TAG: ${DOCKER_REGISTRY_HOST}/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/prod:$CI_COMMIT_SHORT_SHA
  
build-dev:
  stage: build
  before_script:
    - apk add --no-cache openssh-client bash
  script:
    - ls -a
    - export NGINX_IMAGE=${DEV_IMAGE_TAG}_nginx
    - chmod +x ./ci/dev/setup_env.sh
    - bash ./ci/dev/setup_env.sh
    - echo "------------------Setup done---------------------"
    - docker-compose -f ./docker-compose.ci.yml build
    - docker push ${DEV_IMAGE_TAG}_nginx
  when: manual
  only:
    - dev
  tags:
    - runner-01

deploy-dev:
  stage: deploy
  image:
    name: bitnami/kubectl:1.29
    entrypoint: [""]
  script:
    - export NGINX_IMAGE=$DEV_IMAGE_TAG
    - echo "------------------Setup done---------------------"
    - sed -i "s#<NGINX_IMAGE>#${NGINX_IMAGE}_nginx#" ./ci/dev/app.yml
    - cat ./ci/dev/app.yml
    - kubectl apply -f ./ci/dev/
    - echo "------------------DEPLOY DONE---------------------"
  only:
    - dev
  needs:
    - build-dev
  tags:
    - skylink-runner-cluster-dev-v2
  when: manual

build-prod:
  stage: build
  before_script:
    - apk add --no-cache openssh-client bash
  script:
    - ls -a
    - export NGINX_IMAGE=${PROD_IMAGE_TAG}_nginx
    - chmod +x ./ci/prod/setup_env.sh
    - bash ./ci/prod/setup_env.sh
    - echo "------------------Setup done---------------------"
    - docker-compose -f ./docker-compose.ci.yml build
    - docker push ${PROD_IMAGE_TAG}_nginx
  when: manual
  only:
    - main
  tags:
    - runner-01

deploy-prod:
  stage: deploy
  image:
    name: bitnami/kubectl:1.29
    entrypoint: [""]
  script:
    - export NGINX_IMAGE=$PROD_IMAGE_TAG
    - echo "------------------Setup done---------------------"
    - sed -i "s#<NGINX_IMAGE>#${NGINX_IMAGE}_nginx#" ./ci/prod/app.yml
    - cat ./ci/prod/app.yml
    - kubectl apply -f ./ci/prod/
    - echo "------------------DEPLOY DONE---------------------"
  only:
    - main
  needs:
    - build-prod
  tags:
    - ci-dev
  when: manual
