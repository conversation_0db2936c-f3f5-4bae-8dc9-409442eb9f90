# Set the base image to node:20-alpine
FROM node:20-alpine as build

# Specify where our app will live in the container
WORKDIR /app

RUN ls -a

COPY package.json ./
COPY yarn.lock ./
COPY . /app/

RUN yarn install
RUN yarn build


# Prepare nginx
FROM nginx:stable-alpine
COPY --from=build /app/dist /usr/share/nginx/html
RUN rm /etc/nginx/conf.d/default.conf
COPY --from=build /app/ci/dev/nginx.conf /etc/nginx/conf.d

# Fire up nginx
EXPOSE 80
# EXPOSE 443
CMD ["nginx", "-g", "daemon off;"]

