/** @type {import('tailwindcss').Config} */

const big360Color = {
  neutral: {
    '00': '#FDFDFD',
    '50': '#F5F5F5',
    '100': '#E6E7E9',
    '200': '#D9DBDE',
    '300': '#C5C8CB',
    '400': '#ADB0B4',
    '500': '#909498',
    '600': '#6F7377',
    '700': '#4E5255',
    '800': '#2E3133',
    '900': '#1E1F21',
    '950': '#141416',
  },
  neutralDark: {
    '00': '#141416',
    '50': '#1A1B1D',
    '100': '#212224',
    '200': '#2A2C2F',
    '300': '#36383C',
    '400': '#46484D',
    '500': '#5A5D63',
    '600': '#707479',
    '700': '#8A8E93',
    '800': '#AEB2B6',
    '900': '#D3D6D9',
    '950': '#FDFDFD',
  },
  brand: {
    '00': '#F4F0FF',
    '50': '#E3DAFF',
    '100': '#CFBEFF',
    '200': '#B89EFF',
    '300': '#A37EFF',
    '400': '#8F5CFF',
    '500': '#7C47E6',
    '600': '#6A3CC2',
    '700': '#582FA0',
    '800': '#45257F',
    '900': '#331B5F',
    '950': '#241344',
  },
  brandAlt: {
    '00': '#341273',
    '50': '#42139D',
    '100': '#4F12C9',
    '200': '#5D14F2',
    '300': '#7537F9',
    '400': '#8F5CFF',
    '500': '#9F75FF',
    '600': '#AF8DFF',
    '700': '#C0A6FF',
    '800': '#D1BFFF',
    '900': '#E2D7FF',
    '950': '#F4F0FF',
  },
  success: {
    '00': '#F3FDF7',
    '50': '#E3F8EF',
    '100': '#C4F1DD',
    '200': '#97E6C4',
    '300': '#6CDCAD',
    '400': '#45D29C',
    '500': '#2BB684',
    '600': '#22996F',
    '700': '#197A59',
    '800': '#135D45',
    '900': '#0E4634',
    '950': '#0A3326',
  },
  successAlt: {
    '00': '#0A3326',
    '50': '#0E4634',
    '100': '#135D45',
    '200': '#197A59',
    '300': '#22996F',
    '400': '#2BB684',
    '500': '#45D29C',
    '600': '#6CDCAD',
    '700': '#97E6C4',
    '800': '#C4F1DD',
    '900': '#E3F8EF',
    '950': '#F3FDF7',
  },
  info: {
    '00': '#F2F8FF',
    '50': '#E4F0FE',
    '100': '#CBE3FD',
    '200': '#A6D3FC',
    '300': '#84C3FB',
    '400': '#69B4FA',
    '500': '#4F9EF0',
    '600': '#4284D0',
    '700': '#366BB0',
    '800': '#2A5490',
    '900': '#1E4071',
    '950': '#152E55',
  },
  infoAlt: {
    '00': '#152E55',
    '50': '#1E4071',
    '100': '#2A5490',
    '200': '#366BB0',
    '300': '#4284D0',
    '400': '#4F9EF0',
    '500': '#69B4FA',
    '600': '#84C3FB',
    '700': '#A6D3FC',
    '800': '#CBE3FD',
    '900': '#E4F0FE',
    '950': '#F2F8FF',
  },
  warning: {
    '00': '#FFFBE8',
    '50': '#FFF5D6',
    '100': '#FFE89E',
    '200': '#FFD75A',
    '300': '#FFC930',
    '400': '#FFBC00',
    '500': '#F5B200',
    '600': '#D89D00',
    '700': '#BB8700',
    '800': '#9E7200',
    '900': '#835D00',
    '950': '#684A00',
  },
  warningAlt: {
    '00': '#684A00',
    '50': '#835D00',
    '100': '#9E7200',
    '200': '#BB8700',
    '300': '#D89D00',
    '400': '#F5B200',
    '500': '#FFBC00',
    '600': '#FFC930',
    '700': '#FFD75A',
    '800': '#FFE89E',
    '900': '#FFF5D6',
    '950': '#FFFBE8',
  },
  danger: {
    '00': '#FFF4F4',
    '50': '#FFE7E7',
    '100': '#FFCECE',
    '200': '#FFA3A3',
    '300': '#FF7A7A',
    '400': '#FF5C5C',
    '500': '#F84242',
    '600': '#DB3838',
    '700': '#BF2D2D',
    '800': '#A32121',
    '900': '#891717',
    '950': '#6E0F0F',
  },
  dangerAlt: {
    '00': '#6E0F0F',
    '50': '#891717',
    '100': '#A32121',
    '200': '#BF2D2D',
    '300': '#DB3838',
    '400': '#F84242',
    '500': '#FF5C5C',
    '600': '#FF7A7A',
    '700': '#FFA3A3',
    '800': '#FFCECE',
    '900': '#FFE7E7',
    '950': '#FFF4F4',
  },
  alphaWhite: {
    '00': '#FDFDFD00',
    '08': '#FDFDFD14',
    '12': '#FDFDFD1F',
    '16': '#FDFDFD29',
    '24': '#FDFDFD3D',
    '32': '#FDFDFD52',
    '48': '#FDFDFD7A',
    '56': '#FDFDFD8F',
    '64': '#FDFDFDA3',
    '72': '#FDFDFDB8',
    '80': '#FDFDFDCC',
    '96': '#FDFDF8F5',
  },
  alphaBlack: {
    '00': '#1E1F2100',
    '08': '#1E1F2114',
    '12': '#1E1F211F',
    '16': '#1E1F2129',
    '24': '#1E1F213D',
    '32': '#1E1F2152',
    '48': '#1E1F217A',
    '56': '#1E1F218F',
    '64': '#1E1F21A3',
    '72': '#1E1F21B8',
    '80': '#1E1F21CC',
    '88': '#1E1F21E0',
  },
  alphaBrand: {
    '08': '#8F5CFF14',
    '12': '#8F5CFF1F',
    '16': '#8F5CFF29',
    '24': '#8F5CFF3D',
    '32': '#8F5CFF52',
    '48': '#8F5CFF7A',
    '56': '#8F5CFF8F',
    '64': '#8F5CFFA3',
    '72': '#8F5CFFB8',
    '80': '#8F5CFFCC',
    '96': '#8F5CFFF5',
  },
};


export default {
  darkMode: ['class'],
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      animation: {
        loadingDotOne: 'loadingDotOne 1s infinite',
        loadingDotTwo: 'loadingDotTwo 1.1s infinite',
        loadingDotThree: 'loadingDotThree 1.2s infinite',
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
      keyframes: {
        loadingDotOne: {
          '0%, 100%': {
            transform: 'translateY(-25%)',
            'animation-timing-function': 'cubic-bezier(0.8, 0, 1, 1)',
          },
          '50%': {
            transform: 'translateY(0)',
            'animation-timing-function': 'cubic-bezier(0, 0, 0.2, 1)',
          },
        },
        loadingDotTwo: {
          '0%, 100%': {
            transform: 'translateY(-25%)',
            'animation-timing-function': 'cubic-bezier(0.8, 0, 1, 1)',
          },
          '50%': {
            transform: 'translateY(0)',
            'animation-timing-function': 'cubic-bezier(0, 0, 0.2, 1)',
          },
        },
        loadingDotThree: {
          '0%, 100%': {
            transform: 'translateY(-25%)',
            'animation-timing-function': 'cubic-bezier(0.8, 0, 1, 1)',
          },
          '50%': {
            transform: 'translateY(0)',
            'animation-timing-function': 'cubic-bezier(0, 0, 0.2, 1)',
          },
        },
        'accordion-down': {
          from: {
            height: '0',
          },
          to: {
            height: 'var(--radix-accordion-content-height)',
          },
        },
        'accordion-up': {
          from: {
            height: 'var(--radix-accordion-content-height)',
          },
          to: {
            height: '0',
          },
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      boxShadow: {
        filter: 'var(--shadow-filter)',
        medium: '0px 4px 20px -8px hsla(225, 18%, 4%, 0.1)',
        light: '0px 0px 32px 0px hsla(225, 18%, 4%, 0.02)',
      },
      colors: {
        brand: {
          DEFAULT: 'hsl(var(--brand))',
          foreground: 'hsl(var(--brand-foreground))',
          secondary: 'hsl(var(--brand-secondary))',
        },
        background: {
          DEFAULT: 'hsl(var(--background))',
          foreground: 'hsl(var(--background-foreground))',
        },
        foreground: 'hsl(var(--foreground))',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        tertiary: {
          DEFAULT: 'hsl(var(--tertiary))',
          foreground: 'hsl(var(--tertiary-foreground))',
        },
        primary: {
          crm: 'hsl(var(--primary-crm))',
          foreground: 'hsl(var(--primary-foreground-crm))',
        },
        text: {
          primary: 'hsl(var(--text-primary-crm))',
          foreground: 'hsl(var(--text-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground_crm: 'hsl(var(--secondary-foreground-crm))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        create: {
          DEFAULT: 'hsl(var(--create))',
          foreground: 'hsl(var(--create-foreground))',
        },
        error: {
          DEFAULT: 'hsl(var(--error))',
          text: 'hsl(var(--error-text))',
        },
        success: {
          DEFAULT: 'hsl(var(--success))',
          hover: 'hsl(var(--success-hover))',
          text: 'hsl(var(--success-text))',
        },
        standard: {
          DEFAULT: 'hsl(var(--standard))',
          text: 'hsl(var(--standard-text))',
        },
        disabled: {
          DEFAULT: 'hsl(var(--disabled))',
        },
        filter: {
          DEFAULT: 'hsl(var(--filter-text))',
        },
        delete: {
          DEFAULT: 'hsl(var(--delete))',
          foreground: 'hsl(var(--delete-foreground))',
        },
        warning: {
          DEFAULT: 'hsl(var(--warning))',
          background: 'hsl(var(--warning-bg))',
          text: 'hsl(var(--warning-text))',
        },
        info: {
          DEFAULT: 'hsl(var(--info))',
          background: 'hsl(var(--info-bg))',
          text: 'hsl(var(--info-text))',
        },
        avatar: {
          DEFAULT: 'hsl(var(--avatar))',
        },
        hover: {
          filter: 'hsl(var(--hover-filter))',
          foreground: 'hsl(var(--hover-filter-foreground))',
          table: 'hsl(var(--cell-hover))',
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        chart: {
          1: 'hsl(var(--chart-1))',
          2: 'hsl(var(--chart-2))',
          3: 'hsl(var(--chart-3))',
          4: 'hsl(var(--chart-4))',
          5: 'hsl(var(--chart-5))',
        },
        overlay: {
          DEFAULT: 'var(--overlay)',
          box: {
            icon: 'var(--box-icon-overlay)',
          },
        },
        big360Color
      },
      plugins: ['tailwindcssAnimate'],
    },
  },
};
