import { clsx, type ClassValue } from 'clsx';
import camelCase from 'lodash/camelCase';
import isArray from 'lodash/isArray';
import isObject from 'lodash/isObject';
import transform from 'lodash/transform';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const rowPage = [10, 20, 30, 40, 50];
export const rowPageDefault = 10;

export function removeVietnameseTones(str: string) {
  // Lowercase Vietnamese characters
  str = str
    .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, 'a')
    .replace(/[èéẹẻẽêềếệểễ]/g, 'e')
    .replace(/[ìíịỉĩ]/g, 'i')
    .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, 'o')
    .replace(/[ùúụủũưừứựửữ]/g, 'u')
    .replace(/[ỳýỵỷỹ]/g, 'y')
    .replace(/đ/g, 'd');

  // Uppercase Vietnamese characters
  str = str
    .replace(/[ÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴ]/g, 'A')
    .replace(/[ÈÉẸẺẼÊỀẾỆỂỄ]/g, 'E')
    .replace(/[ÌÍỊỈĨ]/g, 'I')
    .replace(/[ÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠ]/g, 'O')
    .replace(/[ÙÚỤỦŨƯỪỨỰỬỮ]/g, 'U')
    .replace(/[ỲÝỴỶỸ]/g, 'Y')
    .replace(/Đ/g, 'D');

  // Remove diacritical marks
  str = str.replace(/[\u0300\u0301\u0303\u0309\u0323]/g, '').replace(/[\u02C6\u0306\u031B]/g, '');

  // Normalize spaces
  str = str.replace(/\s+/g, ' ').trim();

  // Remove special characters
  str = str.replace(/[!@%^*()+=<>?/,.,:;'"&#\[\]~$_`\-{}|\\]/g, ' ');

  return str;
}
export const camelize = (obj: Record<string, unknown>) =>
  transform(
    obj,
    (
      acc: Record<string, unknown>,
      value: unknown,
      key: string,
      target: Record<string, unknown>,
    ) => {
      const camelKey = isArray(target) ? key : camelCase(key);
      acc[camelKey] = isObject(value) ? camelize(value as Record<string, unknown>) : value;
    },
    {},
  );
