import React, { useEffect, useMemo, useRef, useState } from 'react';
import TableContactList from '@/components/ContactList/TableContactList';
import { ColumnDef, SortingState } from '@tanstack/react-table';
import {
  TContactItems,
  TContactListResponse,
  TContactRestore,
  TSegments,
} from '@/types/contactList';
import { useTranslation } from 'react-i18next';
import { Checkbox } from '@/components/ui/checkbox';
import { RiMailLine, RiPhoneLine } from '@remixicon/react';
import {
  arrayToUrlParams,
  formatDate,
  handleCheckEmptyValue,
  handleFormatPhone,
  toCamelCase,
  verifyParamSearch,
} from '@/utils/helper';
import { LABEL, PAGE_SIZE, QUERY_KEY } from '@/utils/constants';
import { useMutation, useQuery } from '@tanstack/react-query';
import { get, update } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { TBaseResponse, TErrorResponse } from '@/types/ResponseApi';
import { TPagination } from '@/types/table';
import { Button } from '@/components/ui/button';
import { useSearchParams } from 'react-router-dom';
import { toast, useToast } from '@/hooks/use-toast';
import CustomToolTips from '@/components/CustomToolTips';
import ModalEmptyTrash from '@/pages/TrashContact/ModalEmptyTrash';
import { Avatar } from '@/components/Avatar/Avatar';
import { RenderGender } from '@/components/RenderGender';
import SegmentCol from '@/pages/ContactList/components/columns/SegmentCol';
import { handleGetContactLimit } from '@/store/ContactLimit/action';
import { useAppDispatch } from '@/hooks/reduxHooks';

const n = (key: keyof TContactItems) => key;
const TYPE = 'CRM';

const TrashContact: React.FC = () => {
  const { t } = useTranslation();
  const refClickCloseModal = useRef<HTMLButtonElement>(null);
  const dispatch = useAppDispatch();
  const { dismiss } = useToast();
  const [listRestore, setListRestore] = useState<TContactItems[]>([]);
  const [searchParams] = useSearchParams();
  const [contactListData, setContactListData] = useState<TContactListResponse>({
    code: 0,
    message: '',
    data: {
      items: [],
      count: 0,
    },
  });
  const [pagination, setPagination] = useState<TPagination>({
    currentPage: 1,
    pageSize: PAGE_SIZE,
  });
  const [sortContactList, setSortContactList] = useState<SortingState>([]);
  const resetFilterRef = useRef<(() => void) | null>(null);

  const allQueryParams = Object.fromEntries(searchParams.entries());
  const handleMergeArray = (arrSrc: TSegments[]) => [
    ...arrSrc.map((item) => ({
      name: item.segment.name,
      type: TYPE,
      color: item.segment.color,
    })),
  ];
  const column: ColumnDef<TContactItems>[] = useMemo<ColumnDef<TContactItems>[]>(() => {
    return [
      {
        id: n('full_name'),
        accessorKey: n('full_name'),
        size: 240,
        minSize: 180,
        cell: ({ row, getValue }) => {
          const full_name = getValue<string>();
          const fb_uid = row.original.fb_uid;
          return (
            <div className="flex justify-between">
              <div className="flex gap-3 items-center">
                <Checkbox
                  onCheckedChange={(value) => row.toggleSelected(!!value)}
                  checked={row.getIsSelected() || (row.getIsSomeSelected() && 'indeterminate')}
                  aria-label="Select all"
                />
                <Avatar uid={fb_uid} name={full_name} />
                {full_name ? (
                  <CustomToolTips
                    element={<p className="truncate">{full_name}</p>}
                    content={full_name}
                  />
                ) : (
                  <>-</>
                )}
              </div>
            </div>
          );
        },
        header: ({ table }) => {
          return (
            <div className="flex gap-3">
              <Checkbox
                checked={
                  table.getIsAllPageRowsSelected() ||
                  (table.getIsSomePageRowsSelected() && 'indeterminate')
                }
                className="bg-white"
                onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                aria-label="Select all"
              />
              {t('common.name')}
            </div>
          );
        },
      },
      {
        id: n('phone_number'),
        accessorKey: n('phone_number'),
        size: 230,
        minSize: 150,
        header: () => <p className="w-full">{t('common.phoneNumber')}</p>,
        cell: ({ getValue, row }) => {
          const phoneNumber = getValue<string>();
          // const status = row.original.status;
          const datatype = row.original.datatype;
          if (!phoneNumber || datatype === 'DATASET') {
            return <>-</>;
          }
          return (
            <div className="flex items-center gap-4">
              <div className="flex items-center rounded-lg  w-fit min-w-[134px] h-[32px] p-1 gap-1 [&_span]:hover:text-base hover:text-success-hover hover:bg-success hover:cursor-pointer [&_svg]:hover:scale-[120%] [&_svg]:duration-300 [&_span]:duration-300">
                <RiPhoneLine size={20} color={'#27923A'} />
                <p className="text-sm w-max">{handleFormatPhone(phoneNumber)}</p>
              </div>
              {/*<RenderStatus status={status} />*/}
            </div>
          );
        },
      },
      {
        id: n('email'),
        accessorKey: n('email'),
        size: 210,
        minSize: 150,
        header: () => <p className="w-full">{t('common.email')}</p>,
        cell: ({ getValue }) => {
          const email = getValue<string>();
          if (!email) {
            return handleCheckEmptyValue(getValue<string>());
          }
          return (
            <CustomToolTips
              element={
                <div className="flex items-center gap-1 px-2 py-0 cursor-pointer rounded-lg [&_p]:duration-300 [&_p]:hover:text-base hover:bg-secondary-foreground_crm hover:border hover:border-border ">
                  <span>
                    <RiMailLine size={20} color={'#515667'} />
                  </span>
                  <p className="truncate text-sm">{email}</p>
                </div>
              }
              content={email}
            />
          );
        },
      },
      {
        id: toCamelCase(LABEL.segment),
        accessorKey: toCamelCase(LABEL.segment),
        size: 210,
        minSize: 150,
        header: () => <p className="w-full">{t('common.segment.title')}</p>,
        cell: ({ row }) => {
          const segment = handleMergeArray(row.original.segments);
          if (segment.length === 0) {
            return '-';
          }
          return <SegmentCol segments={segment} />;
        },
      },
      {
        id: n('dob'),
        accessorKey: n('dob'),
        size: 100,
        minSize: 100,
        enableSorting: true,
        sortUndefined: 'last',
        sortDescFirst: false,
        header: () => <p>{t('common.dob')}</p>,
        cell: ({ getValue }) => {
          const dob = getValue<string>();
          return <p className="truncate">{dob ? formatDate(dob, '/') : '-'}</p>;
        },
      },
      {
        id: n('gender'),
        accessorKey: n('gender'),
        size: 100,
        minSize: 100,
        header: () => <p className="w-full">{t('common.gender')}</p>,
        enableSorting: false,
        cell: ({ getValue }) => {
          const genderValue = getValue<string>();
          if (!genderValue) {
            return <>-</>;
          }
          return (
            <div className="w-full flex items-center">
              <RenderGender gender={genderValue} />
            </div>
          );
        },
      },
    ];
  }, [t]);

  const {
    data: contactListDataResponse,
    isLoading: loadingContactList,
    refetch: refetchContact,
    isRefetching: isRefetchingContact,
  } = useQuery({
    queryKey: [QUERY_KEY.CONTACT_LIST_TRASH, allQueryParams, pagination, sortContactList],
    refetchOnMount: true,
    queryFn: () =>
      get({
        endpoint: ENDPOINTS.contact_list,
        params: {
          page: pagination.currentPage,
          limit: pagination.pageSize,
          order_by: `${
            sortContactList.length > 0 ? `${arrayToUrlParams(sortContactList)},` : ''
          }-date_created,full_name`,
          is_deleted: true,
          ...verifyParamSearch(allQueryParams),
        },
      }),
  });

  useEffect(() => {
    if (contactListDataResponse) {
      setContactListData(
        (contactListDataResponse as unknown as TBaseResponse<TContactListResponse>)?.data,
      );
    }
  }, [contactListDataResponse, pagination.currentPage]);

  const restoreContactMutation = useMutation({
    mutationFn: async (payload: TContactRestore): Promise<TBaseResponse<TContactListResponse>> => {
      return update({
        endpoint: ENDPOINTS.contact_list_restore,
        params: payload,
      }).then((res) => res.data as TBaseResponse<TContactListResponse>);
    },
    onSuccess: () => {
      handleSuccessDelete();
      setTimeout(() => {
        setListRestore([]);
        //   time toast undo button is available
      }, 1500);
      toast({
        status: 'success',
        description: t('contactList.contactsRestored'),
        action: (
          <Button
            className="bg-transparent shadow-none rounded-none hover:bg-transparent"
            onClick={handleUndoContact}
          >
            <span className="text-brand font-medium text-sm">{t('common.button.undo')}</span>
          </Button>
        ),
      });
    },
    onError: (error: TErrorResponse) => {
      console.log('error', { error });
    },
  });

  const handleSuccessDelete = () => {
    handleResetFilter();
    refetchContact();
    setTimeout(() => {
      onCloseModal();
      dispatch(handleGetContactLimit());
    }, 100);
  };

  const undoContactMutation = useMutation({
    mutationFn: async (payload: TContactRestore): Promise<TBaseResponse<TContactListResponse>> => {
      return update({
        endpoint: ENDPOINTS.bulk_delete_contact,
        params: payload,
      }).then((res) => res.data as TBaseResponse<TContactListResponse>);
    },
    onSuccess: handleSuccessDelete,
    onError: (error: TErrorResponse) => {
      console.log('error', { error });
    },
  });

  const emptyTrashContactMutation = useMutation({
    mutationFn: async () => {
      return update({
        endpoint: ENDPOINTS.empty_trash_contact,
      }).then((res) => res.data);
    },
    onSuccess: handleSuccessDelete,
    onError: () => {},
  });

  const handleSubmitRestore = () => {
    restoreContactMutation.mutate({
      contact_ids: listRestore.map((item) => item.id),
    });
  };

  const handleUndoContact = () => {
    undoContactMutation.mutate({
      contact_ids: listRestore.map((item) => item.id),
    });
    dismiss();
  };

  const onCloseModal = () => {
    if (refClickCloseModal.current) {
      refClickCloseModal.current.click();
    }
  };

  const handleResetFilter = () => {
    if (resetFilterRef.current) {
      resetFilterRef.current();
    }
  };

  const loadingTrashContact = loadingContactList || isRefetchingContact;

  const dataTable: TContactItems[] = useMemo(
    () => (loadingTrashContact ? [] : (contactListData?.data?.items ?? [])),
    [loadingTrashContact, contactListData],
  );

  const countListContact = contactListData?.data?.count ?? 0;

  return (
    <>
      <div className="py-2 px-3 mt-4 flex justify-between items-center w-full rounded-2xl border-border bg-secondary-foreground_crm mb-6">
        <p className="font-normal text-sm text-secondary leading-5">
          {t('contactList.trashDescriptionPage')}
        </p>
        <ModalEmptyTrash
          loading={emptyTrashContactMutation.isPending}
          handleEmpty={() => emptyTrashContactMutation.mutate()}
          ref={refClickCloseModal}
          isDisableButton={dataTable.length === 0 || countListContact === 0}
        />
      </div>
      <TableContactList
        data={dataTable}
        count={countListContact}
        columns={column as ColumnDef<TContactItems>[]}
        isShowHistory={false}
        pagination={pagination}
        setPagination={setPagination}
        isLoading={loadingTrashContact}
        getRowSelected={setListRestore}
        setSortContactList={setSortContactList}
        onResetFilter={(ref) => (resetFilterRef.current = ref)}
        filter={{
          isSearch: true,
          isFilter: false,
          rightNodeFilter: (
            <>
              <Button
                size={'lg'}
                disabled={listRestore.length === 0}
                onClick={handleSubmitRestore}
                className="bg-create font-medium p-3 text-sm rounded-xl hover:text-primary-foreground hover:bg-create-foreground disabled:bg-tertiary disabled:text-disabled"
              >
                {t('common.button.restore')}
              </Button>
            </>
          ),
        }}
      />
    </>
  );
};
export default TrashContact;
