import { RiErrorWarningLine, RiLoader2Line } from '@remixicon/react';
import { DialogClose } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import Modal from '@/components/Modal';
import React from 'react';
import { useTranslation } from 'react-i18next';

type TModalDeleteContact = {
  loading: boolean;
  handleEmpty: () => void;
  isDisableButton: boolean;
};

const ModalEmptyTrash = React.forwardRef<HTMLButtonElement, TModalDeleteContact>(
  (props, refClickCloseModal) => {
    const { loading, handleEmpty, isDisableButton } = props;
    const { t } = useTranslation();

    return (
      <Modal
        isCloseIcon={false}
        className="w-fit max-w-none"
        titleAlign={'center'}
        trigger={
          <Button
            variant={'secondary'}
            size={'sm'}
            className="text-sm text-primary-crm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={isDisableButton}
          >
            {t('common.button.emptyTrashNow')}
          </Button>
        }
        title={
          <>
            <div className="m-auto w-fit">
              <RiErrorWarningLine size={80} color={'#F53E3E'} />
            </div>
            <p className="text-xl text-primary font-semibold text-center">
              {t('contactList.emptyTrash')}
            </p>
          </>
        }
      >
        <div className="flex flex-col gap-4 w-[529px] justify-center items-center">
          <p className="text-sm text-center text-secondary">
            {t('contactList.emptyTrashDescription')}
          </p>
          <div className="flex gap-4 w-full">
            <DialogClose className="flex-1" disabled={loading} ref={refClickCloseModal}>
              <div className="flex items-center justify-center h-[40px] font-medium w-full bg-secondary rounded-lg text-sm text-primary hover:bg-secondary-foreground_crm hover:text-filter">
                {t('common.button.cancel')}
              </div>
            </DialogClose>

            <Button
              disabled={loading}
              onClick={handleEmpty}
              className="flex-1 h-[40px] font-medium border border-border w-full bg-delete text-background-foreground text-base rounded-xl hover:bg-delete-foreground hover:text-background-foreground"
            >
              {loading ? (
                <RiLoader2Line className="mx-auto animate-spin" />
              ) : (
                t('common.button.emptyTrashNow')
              )}
            </Button>
          </div>
        </div>
      </Modal>
    );
  },
);

ModalEmptyTrash.displayName = 'ModalEmptyTrash';

export default ModalEmptyTrash;
