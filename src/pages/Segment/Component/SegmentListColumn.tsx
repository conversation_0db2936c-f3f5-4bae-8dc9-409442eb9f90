import { ColumnDef } from '@tanstack/react-table';
import { formatDateTime, handleCheckEmptyValue } from '@/utils/helper';
import { useTranslation } from 'react-i18next';
import { TSegment } from '@/types/segment';
import { TagFillIcon } from '@/assets/TagFillIcon';
import CustomToolTips from '@/components/CustomToolTips';
// import { RiEdit2Line, RiShieldCheckLine } from '@remixicon/react';
import { RiEdit2Line } from '@remixicon/react';
// import { ModalRemoveSegment } from '@/components/Segment/ModalRemoveSegment';
import { ModalSubmitSegment } from '@/components/Segment/ModalSubmitSegment';
import Modal from '@/components/Modal';
import { Link } from 'react-router-dom';
// import { CleanUpContainer } from '@/components/CleanUpContainer';
// import { update } from '@/apis/apiHelper';
// import { ENDPOINTS } from '@/apis/endpoints';
// import { getSegment } from '@/store/segment/action';
// import { useAppDispatch } from '@/hooks/reduxHooks';
import { ROOT_PATH, ROOT_ROUTE } from '@/constants/router';

const n = (key: keyof TSegment) => key;

type TSegmentListColumn = {
  loadingSegment: boolean;
  deleteSegment: (id: string) => void;
};

export const SegmentListColumn = ({}: TSegmentListColumn): ColumnDef<TSegment>[] => {
  // const { loadingSegment, deleteSegment } = props;
  const { t } = useTranslation();
  // const dispatch = useAppDispatch();

  // const handleCleanUpSegment = async (data: TSegment, quantity: number) => {
  //   await update({
  //     endpoint: ENDPOINTS.segments.cleanUp(data.id),
  //     params: {
  //       contact_quantity: quantity,
  //     },
  //   }).finally(() => {
  //     setTimeout(() => {
  //       dispatch(getSegment({ pageSize: 10, currentPage: 1 }));
  //     }, 3000);
  //   });
  // };
  return [
    {
      id: n('name'),
      accessorKey: n('name'),
      size: 540,
      minSize: 330,
      header: () => <p className="w-full">{t('segment.segmentName')}</p>,
      cell: ({ getValue, row }) => {
        const name = getValue<string>();
        if (!name) {
          return <></>;
        }
        return (
          <div className="flex items-center gap-4">
            {row.original?.color && <TagFillIcon color={row.original?.color ?? ''} />}
            <CustomToolTips
              element={
                <Link
                  to={`${ROOT_PATH}/${ROOT_ROUTE.contact.segmentDetail}/${row.original.id}`}
                  className="truncate hover:font-medium hover:text-primary-crm"
                >
                  {name}
                </Link>
              }
              content={name}
            />
          </div>
        );
      },
    },
    {
      id: n('contact_quantity'),
      accessorKey: n('contact_quantity'),
      size: 210,
      minSize: 150,
      header: () => <p className="w-full">{t('segment.totalContact')}</p>,
      cell: ({ getValue }) => {
        const contact_quantity = getValue<string>();
        if (!contact_quantity) {
          return handleCheckEmptyValue(getValue<string>());
        }
        return (
          <div className="flex gap-1 justify-between">
            <p className="truncate hover:cursor-pointer hover:border-b">{Number(contact_quantity).toLocaleString()}</p>
          </div>
        );
      },
    },

    {
      id: n('date_created'),
      accessorKey: n('date_created'),
      size: 240,
      minSize: 180,
      header: () => <p className="w-full">{t('segment.createDate')}</p>,
      cell: ({ getValue }) => {
        const dateCreated = getValue<string>();
        if (!dateCreated) {
          return handleCheckEmptyValue(dateCreated);
        }
        return (
          <div className="flex gap-1 justify-between">
            <p className="truncate">{formatDateTime(dateCreated, '/', ':')}</p>
          </div>
        );
      },
    },
    {
      id: 'action',
      pin: 'right',
      // size: 113,
      // minSize: 113,
      // maxSize: 113,
      size: 70,
      minSize: 70,
      maxSize: 70,
      meta: {
        sticky: 'right',
      },
      header: () => <></>,
      cell: ({ row }) => {
        return (
          <div className="w-full flex gap-3 px-1 justify-center">
            {/*<CustomToolTips*/}
            {/*  element={*/}
            {/*    <CleanUpContainer*/}
            {/*      trigger={(setOpen: () => void) => (*/}
            {/*        <button className="hover:text-primary" onClick={setOpen}>*/}
            {/*          <RiShieldCheckLine size={20} color={'#20232C'} />*/}
            {/*        </button>*/}
            {/*      )}*/}
            {/*      removePopup={false}*/}
            {/*      setRemovePopup={() => {}}*/}
            {/*      countQuantity={row.original.contact_quantity}*/}
            {/*      handleCleanUp={(data) => {*/}
            {/*        data.handleCleanupSuccess(() =>*/}
            {/*          handleCleanUpSegment(row.original, data.quantity),*/}
            {/*        );*/}
            {/*      }}*/}
            {/*    />*/}
            {/*  }*/}
            {/*  content={t('common.button.verify')}*/}
            {/*/>*/}
            <CustomToolTips
              element={
                <Modal
                  className="w-[850px] max-w-none"
                  titleAlign={'center'}
                  title={t('segment.updateSegment')}
                  trigger={
                    <button className="hover:text-primary">
                      <RiEdit2Line size={20} color={'#20232C'} />
                    </button>
                  }
                >
                  <ModalSubmitSegment segmentDetail={row.original} />
                </Modal>
              }
              content={t('common.button.edit')}
            />

            {/*<CustomToolTips*/}
            {/*  element={*/}
            {/*    <ModalRemoveSegment*/}
            {/*      segmentDetail={row.original}*/}
            {/*      deleteSegment={deleteSegment}*/}
            {/*      loadingSegment={loadingSegment}*/}
            {/*    />*/}
            {/*  }*/}
            {/*  content={t('common.button.moveToTrash')}*/}
            {/*/>*/}
          </div>
        );
      },
    },
  ] as ColumnDef<TSegment>[];
};
