import React, { useEffect, useMemo, useRef, useState } from 'react';
import {
  RiAddCircleLine,
  RiCakeLine,
  RiContactsBook3Line,
  RiIdCardLine,
  RiLoader2Line,
  RiMapPinLine,
  RiNotificationBadgeLine,
} from '@remixicon/react';
import Modal from '@/components/Modal';
import { useTranslation } from 'react-i18next';
import { useMutation, useQuery } from '@tanstack/react-query';
import {
  ageSelectOptions,
  genderSelectOptions,
  monthSelectOptions,
  OptionFilterDemoGraphic,
  PAGE_SIZE,
  QUERY_KEY,
  RangeOptions,
  statusSelectOptions,
} from '@/utils/constants';
import { get, update } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { Button } from '@/components/ui/button';
import { useParams } from 'react-router-dom';
import { TPagination } from '@/types/table';
import {
  TContactItems,
  TContactListResponse,
  TContactRestore,
  TSearchFilter,
} from '@/types/contactList';
import TableContactList from '@/components/ContactList/TableContactList';
import { ContactListSegmentColumn } from '@/pages/ContactList/components/columns/ContactListSegmentColumn';
import { TBaseResponse, TErrorResponse } from '@/types/ResponseApi';
import useDebounce from '@/hooks/useDebounce';
import MultipleSelectColumn from '@/components/MultipleSelectColumn';
import { MultipleSelect } from '@/components/MultipleSelect';
import SelectRange from '@/components/SelectRange';
import { DialogClose } from '@/components/ui/dialog';
import { arrayToUrlParams, verifyParamSearch } from '@/utils/helper';
import { FilterBar } from '@/components/FilterBar';
import ResultFound from '@/components/ResultFound';
import ListResultTableContact from '@/components/ContactList/ListResultTableContact';
import { getSelector, useAppSelector } from '@/hooks/reduxHooks';
import { SortingState } from '@tanstack/react-table';

const defaultFilter = {
  gender__in: '',
  age__in: '',
  status__in: '',
  dob_month__in: '',
  person_province__in: '',
  search: '',
  frequency_min: '',
  frequency_max: '',
};

type TModalAddMoreContact = {
  refetchContact: () => void;
};

const ModalAddMoreContact: React.FC<TModalAddMoreContact> = ({
  ...props
}: TModalAddMoreContact) => {
  const { refetchContact } = props;
  const { t } = useTranslation();
  const { id } = useParams();

  const [pagination, setPagination] = useState<TPagination>({
    currentPage: 1,
    pageSize: PAGE_SIZE,
  });
  const [sortContactList, setSortContactList] = useState<SortingState>([]);
  const resetFilterRef = useRef<(() => void) | null>(null);
  const [listSelectContact, setListSelectContact] = useState<TContactItems[]>([]);
  const { isDataType } = useAppSelector(getSelector('contactList'));
  const refClickCloseModal = useRef<HTMLButtonElement>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [searchFilter, setSearchFilter] =
    useState<Omit<TSearchFilter, 'segment__in'>>(defaultFilter);
  const [contactListData, setContactListData] = useState<TContactListResponse>({
    code: 0,
    message: '',
    data: {
      items: [],
      count: 0,
    },
  });

  const { locationData } = useAppSelector(getSelector('location_province'));

  const {
    data: contactListDataResponse,
    isLoading: loadingContactSegment,
    isRefetching: refetchingContactSegment,
    refetch: refetchContactSegment,
  } = useQuery({
    queryKey: [QUERY_KEY.CONTACT_LIST_SEGMENT, searchFilter, sortContactList, pagination],
    queryFn: () =>
      get({
        endpoint: ENDPOINTS.contact_list,
        params: {
          page: pagination.currentPage,
          limit: pagination.pageSize,
          datatype: isDataType,
          order_by: `${
            sortContactList.length > 0 ? `${arrayToUrlParams(sortContactList)},` : ''
          }-date_created,full_name`,
          segment__not_in: id?.toString() ?? '',
          ...verifyParamSearch(searchFilter),
        },
      }),
  });

  const handleResetFilter = () => {
    if (resetFilterRef.current && refClickCloseModal.current) {
      resetFilterRef.current();
      refClickCloseModal.current.click();
      refetchContact();
      refetchContactSegment();
    }
  };

  const addListContactSegmentMutation = useMutation({
    mutationFn: async (payload: TContactRestore): Promise<TBaseResponse<TContactListResponse>> => {
      return update({
        endpoint: ENDPOINTS.segments.create + id + '/add-contact/',
        params: payload,
      }).then((res) => res.data as TBaseResponse<TContactListResponse>);
    },
    onSuccess: handleResetFilter,
    onError: (error: TErrorResponse) => {
      console.log('error', { error });
    },
  });

  const handleSelectDemographic = (value: string[]) => {
    setSearchFilter({
      ...searchFilter,
      gender__in: value
        .filter((val) => genderSelectOptions.some((option) => option.value === val))
        .join(','),
      age__in: value
        .filter((val) => ageSelectOptions.some((option) => option.value === val))
        .join(','),
    });
  };

  const handleAddListContact = () => {
    if (listSelectContact && listSelectContact.length > 0) {
      addListContactSegmentMutation.mutate({
        contact_ids: listSelectContact.map((item) => item.id),
      });
    }
  };

  useEffect(() => {
    if (contactListDataResponse) {
      setContactListData(
        (contactListDataResponse as unknown as TBaseResponse<TContactListResponse>)?.data,
      );
    }
  }, [contactListDataResponse, pagination.currentPage]);

  const dataTable: TContactItems[] = useMemo(
    () => (loadingContactSegment ? [] : (contactListData?.data?.items ?? [])),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [contactListData.data.items],
  );

  const countItems = contactListData?.data?.count ?? 0;
  const debouncedSearchQuery: string = useDebounce(searchQuery, 300) ?? '';

  useEffect(() => {
    setSearchFilter({
      ...searchFilter,
      search: debouncedSearchQuery ?? '',
    });
    //   eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debouncedSearchQuery]);

  const hasFilters = useMemo(
    () => Object.values(searchFilter).some((value) => value !== ''),
    [searchFilter],
  );
  return (
    <Modal
      isCloseIcon={false}
      className="w-fit max-w-none min-h-[750px]"
      titleAlign={'center'}
      trigger={
        <Button className="h-[40px] bg-transparent text-primary font-base font-medium border-[1px] border-border rounded-xl hover:bg-white hover:text-filter">
          <RiAddCircleLine />
          {t('common.button.addContact')}
        </Button>
      }
      title={
        <div className="h-[40px]">
          <p className="text-xl text-primary font-semibold text-center">
            {t('segment.addMoreContact')}
          </p>
        </div>
      }
    >
      <div className="w-[1152px]">
        <FilterBar searchQuery={searchQuery} setSearchQuery={setSearchQuery} isRevert={false}>
          <div className="flex gap-2 mt-6 mb-1.5">
            <MultipleSelectColumn
              key={'demographic'}
              data={OptionFilterDemoGraphic}
              icon={<RiIdCardLine size={16} />}
              title={t('common.demographic')}
              selected={searchFilter.gender__in
                .split(',')
                .filter(Boolean)
                .concat(searchFilter.age__in.split(',').filter(Boolean))}
              onChange={(e) => {
                handleSelectDemographic(e);
              }}
              className="min-w-[185px]"
            />
            <MultipleSelect
              key={'dob'}
              icon={<RiCakeLine size={16} />}
              title={t('common.dob')}
              isHideSearch={true}
              options={monthSelectOptions}
              selected={searchFilter.dob_month__in.split(',').filter(Boolean)}
              onChange={(e) => {
                setSearchFilter({
                  ...searchFilter,
                  dob_month__in: e.join(','),
                });
              }}
              placeholder={t('common.dob')}
              className="min-w-[115px]"
            />
            <MultipleSelect
              key={'location'}
              icon={<RiMapPinLine size={16} />}
              title={t('common.location')}
              options={locationData}
              selected={searchFilter.person_province__in.split(',').filter(Boolean)}
              onChange={(e) => {
                setSearchFilter({
                  ...searchFilter,
                  person_province__in: e.join(','),
                });
              }}
              placeholder={t('common.location')}
            />
            <MultipleSelect
              key={'status'}
              icon={<RiNotificationBadgeLine size={16} />}
              title={t('common.status')}
              isHideSearch={true}
              options={statusSelectOptions}
              selected={searchFilter.status__in.split(',').filter(Boolean)}
              onChange={(e) => {
                setSearchFilter({
                  ...searchFilter,
                  status__in: e.join(','),
                });
              }}
              placeholder={t('common.status')}
              className="min-w-[130px]"
            />
            <SelectRange
              key={'contactFrequency'}
              data={RangeOptions}
              icon={<RiContactsBook3Line size={16} />}
              title={t('common.contactFrequency')}
              selected={
                searchFilter.frequency_min || searchFilter.frequency_max
                  ? [searchFilter.frequency_min, searchFilter.frequency_max]
                  : []
              }
              onChange={(e) => {
                setSearchFilter({ ...searchFilter, frequency_min: e[0], frequency_max: e[1] });
              }}
            />
          </div>
        </FilterBar>
        {hasFilters && searchFilter ? (
          <ResultFound
            countItems={countItems}
            handleRemoveAllFilter={() => setSearchFilter(defaultFilter)}
          >
            <ListResultTableContact
              listResult={Object.keys(searchFilter).filter((item) => item !== 'search')}
              selectFilter={searchFilter as TSearchFilter}
              setSelectFilter={setSearchFilter}
              isAction={true}
            />
          </ResultFound>
        ) : (
          <></>
        )}

        <TableContactList
          data={dataTable ?? []}
          count={countItems}
          isShowHistory={true}
          columns={ContactListSegmentColumn()}
          className="h-[370px] max-h-[370px]"
          pagination={pagination}
          setPagination={setPagination}
          isLoading={loadingContactSegment || refetchingContactSegment}
          getRowSelected={setListSelectContact}
          onResetFilter={(ref) => (resetFilterRef.current = ref)}
          filter={{
            isFilter: false,
            isSearch: false,
          }}
          rightNodeFilterContact={<></>}
          setSortContactList={setSortContactList}
        />
        <div className="flex gap-4 w-full mt-6">
          <DialogClose className="flex-1" ref={refClickCloseModal}>
            <div className="flex items-center justify-center h-[40px] font-medium w-full bg-secondary rounded-lg text-sm text-primary hover:bg-secondary-foreground_crm hover:text-filter">
              {t('common.button.cancel')}
            </div>
          </DialogClose>

          <Button
            disabled={addListContactSegmentMutation.isPending}
            onClick={handleAddListContact}
            variant={'primary'}
            className="flex-1 h-[40px] w-full"
          >
            {addListContactSegmentMutation.isPending ? (
              <RiLoader2Line className="mx-auto animate-spin" />
            ) : (
              t('common.button.add')
            )}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default ModalAddMoreContact;
