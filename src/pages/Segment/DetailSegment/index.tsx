import React, { useEffect, useMemo, useRef, useState } from 'react';
import TableContactList from '@/components/ContactList/TableContactList';
import { ContactListColumn } from '@/pages/ContactList/components/columns/ContactListColumn';
import { ColumnDef, SortingState } from '@tanstack/react-table';
import { TContactItems, TContactListResponse, TTimeReminder } from '@/types/contactList';
import { useParams, useSearchParams } from 'react-router-dom';
import { TPagination } from '@/types/table';
import { PAGE_SIZE, paramsSearch, QUERY_KEY } from '@/utils/constants';
import { useMutation, useQuery } from '@tanstack/react-query';
import { get } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { TBaseResponse, TErrorResponse } from '@/types/ResponseApi';
import { TSegment, TSegmentDetailResponse } from '@/types/segment';
import { arrayToUrlParams, filterEmptyParams } from '@/utils/helper';
import ModalAddMoreContact from '@/pages/Segment/DetailSegment/ModalAddMoreContact';
import ResultFilter from '@/components/ResultFilter';
import Breadcrumb from '@/components/Breadcrumb';
import { getSelector, useAppSelector } from '@/hooks/reduxHooks';

const DetailSegment: React.FC = () => {
  // const { t } = useTranslation();
  const [listRemoveContact, setListRemoveContact] = useState<TContactItems[]>([]);
  const { isDataType } = useAppSelector(getSelector('contactList'));
  const resetFilterRef = useRef<(() => void) | null>(null);
  const [searchParams] = useSearchParams();
  const { id } = useParams();
  const allQueryParams = Object.fromEntries(searchParams.entries());
  const querySearch = allQueryParams['search'] ?? '';
  const [segmentDetail, setSegmentDetail] = useState<TSegment>();
  // const [removePopup, setRemovePopup] = useState<boolean>(false);
  const [sortContactList, setSortContactList] = useState<SortingState>([]);
  const [contactListData, setContactListData] = useState<TContactListResponse>({
    code: 0,
    message: '',
    data: {
      items: [],
      count: 0,
    },
  });

  const [pagination, setPagination] = useState<TPagination>({
    currentPage: 1,
    pageSize: PAGE_SIZE,
  });

  const {
    data: contactListDataResponse,
    isLoading: loadingContactList,
    refetch: refetchContact,
    isRefetching: isRefetchingContact,
  } = useQuery({
    queryKey: [QUERY_KEY.CONTACT_LIST_DETAIL_SEGMENT, querySearch, pagination, sortContactList],
    queryFn: () =>
      get({
        endpoint: ENDPOINTS.contact_list,
        params: filterEmptyParams({
          page: pagination.currentPage.toString(),
          limit: pagination.pageSize.toString(),
          datatype: isDataType,
          order_by: `${
            sortContactList.length > 0 ? `${arrayToUrlParams(sortContactList)},` : ''
          }-date_created,full_name`,
          search: querySearch,
          segment__in: id?.toString() ?? '',
        }),
      }),
  });

  const fetchSegmentDetailMutation = useMutation({
    mutationFn: async () => {
      return await get({
        endpoint: ENDPOINTS.segments.detail + id?.toString() + '/',
      }).then((res) => res.data);
    },
    onSuccess: (res) => {
      setSegmentDetail((res as unknown as TSegmentDetailResponse).data as TSegment);
    },
    onError: (error: TErrorResponse) => {
      console.log('error', { error });
    },
  });

  useEffect(() => {
    if (contactListDataResponse) {
      setContactListData(
        (contactListDataResponse as unknown as TBaseResponse<TContactListResponse>)?.data,
      );
    }
  }, [contactListDataResponse, pagination.currentPage]);

  const dataTable: TContactItems[] = useMemo(
    () => (loadingContactList ? [] : (contactListData?.data?.items ?? [])),
    [loadingContactList, contactListData],
  );

  const handleUpdateSegmentReminder = (id: number, payload: TTimeReminder | null) => {
    setContactListData((prev) => {
      const newItems = prev.data.items.map((item) =>
        item.id === id ? { ...item, reminder: payload } : item,
      );
      return { ...prev, data: { ...prev.data, items: newItems } };
    });
  };

  const handleResetFilter = () => {
    if (resetFilterRef.current) {
      resetFilterRef.current();
    }
  };

  // const handleCleanUpSegment = async (quantity: number) => {
  //   await update({
  //     endpoint: ENDPOINTS.segments.cleanUp(id as string),
  //     params: {
  //       contact_quantity: quantity,
  //     },
  //   }).finally(() => setRemovePopup(true));
  // };

  useEffect(() => {
    if (!!id) {
      fetchSegmentDetailMutation.mutate();
    }
    //   eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  useEffect(() => {
    if (segmentDetail && segmentDetail.filter) {
      refetchContact();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [segmentDetail]);

  const countItems = contactListData?.data?.count ?? 0;
  return (
    <div>
      <Breadcrumb endUrl={segmentDetail?.id} title={segmentDetail?.name} />
      <div className="text-left mb-6">
        <h3 className="text-2xl font-medium mb-2">{segmentDetail?.name ?? ''}</h3>
        <p className="text-base font-normal">{segmentDetail?.content ?? ''}</p>
      </div>
      <div className="flex gap-2">
        {segmentDetail?.filter ? (
          Object.keys(segmentDetail?.filter).map((item: string) => {
            const value = item as keyof typeof paramsSearch;
            const result: string[] = (segmentDetail?.filter[value] as string).split(',') ?? [''];
            return (
              <ResultFilter
                key={item}
                keyInSearch={item}
                title={paramsSearch[value]}
                result={result}
                isShowMore={false}
                isAction={false}
              />
            );
          })
        ) : (
          <></>
        )}
      </div>
      <TableContactList
        data={loadingContactList || isRefetchingContact ? [] : (dataTable ?? [])}
        count={countItems}
        isShowHistory={true}
        columns={
          ContactListColumn({
            listRemove: listRemoveContact,
            pagination,
            countItems,
            setListRemove: setListRemoveContact,
            handleUpdateReminder: handleUpdateSegmentReminder,
            setPagination,
            handleResetFilter,
            refetchContact,
            isContactInSegment: true,
            isDataSet: segmentDetail?.datatype === 'DATASET',
          }) as ColumnDef<TContactItems>[]
        }
        pagination={pagination}
        setPagination={setPagination}
        isLoading={loadingContactList || isRefetchingContact}
        getRowSelected={setListRemoveContact}
        setSortContactList={setSortContactList}
        onResetFilter={(ref) => (resetFilterRef.current = ref)}
        filter={{
          isFilter: false,
          isSearch: true,
        }}
        rightNodeFilterContact={
          <>
            <ModalAddMoreContact refetchContact={refetchContact} />
            {/*<CleanUpContainer*/}
            {/*  trigger={(setOpen: () => void) => (*/}
            {/*    <Button*/}
            {/*      onClick={setOpen}*/}
            {/*      className="h-[40px] bg-transparent text-primary font-base font-medium border-[1px] border-border rounded-xl hover:bg-white hover:text-filter"*/}
            {/*    >*/}
            {/*      <RiShieldCheckLine />*/}
            {/*      {t('common.button.verify')}*/}
            {/*    </Button>*/}
            {/*  )}*/}
            {/*  removePopup={removePopup}*/}
            {/*  setRemovePopup={setRemovePopup}*/}
            {/*  countQuantity={segmentDetail?.contact_quantity ?? 0}*/}
            {/*  handleCleanUp={(data) => {*/}
            {/*    data.handleCleanupSuccess(() => handleCleanUpSegment(data.quantity));*/}
            {/*    // refetchContact*/}
            {/*  }}*/}
            {/*/>*/}
          </>
        }
      />
    </div>
  );
};
export default DetailSegment;
