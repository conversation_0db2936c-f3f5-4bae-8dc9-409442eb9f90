import { ColumnDef } from '@tanstack/react-table';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { SegmentListColumn } from '@/pages/Segment/Component/SegmentListColumn';
import { TSegment } from '@/types/segment';
import TableSegmentList from '@/pages/Segment/Component/TableSegmentList';
import { useSearchParams } from 'react-router-dom';
import Breadcrumb from '@/components/Breadcrumb';
import { getSelector, useAppDispatch, useAppSelector } from '@/hooks/reduxHooks';
import { toast } from '@/hooks/use-toast';
import { TPaginationSearch } from '@/types/table';
import { PAGE_SIZE } from '@/utils/constants';
import { deleteSegment, getDatasetSegment, getSegment } from '@/store/segment/action';
import { removeItem } from '@/store/segment/segmentSlice';
import { RiDatabase2Line, RiVipDiamondLine } from '@remixicon/react';
import { TabsContainer } from '@/components/TabsContainer';
import { setDataType } from '@/store/contactList/contactListSlice';

const SegmentList: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { segments, datasetSegments, loadingSegmentsDataset, loadingSegmentsAudience } =
    useAppSelector(getSelector('segment'));
  const [searchParams] = useSearchParams();
  const allQueryParams = Object.fromEntries(searchParams.entries());
  const querySelect = allQueryParams['search'] ?? '';
  const { isDataType } = useAppSelector(getSelector('contactList'));
  const isFirstRender = useRef(true);
  const [pagination, setPagination] = useState<TPaginationSearch>({
    currentPage: 1,
    pageSize: PAGE_SIZE,
  });

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }
    handleUpdateList({
      ...pagination,
      search: querySelect,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [querySelect]);

  const handleUpdateList = (value: TPaginationSearch) => {
    dispatch(getSegment(value));
    setPagination(value);
  };

  const handleUpdateListDataset = (value: TPaginationSearch) => {
    dispatch(getDatasetSegment(value));
    setPagination(value);
  };
  const handleDeleteSegment = async (id: string) => {
    const res = await dispatch(deleteSegment(id));

    /* eslint-disable @typescript-eslint/no-explicit-any */
    if ((res as any)?.payload?.data) {
      handleRemoveSuccess();
      dispatch(removeItem(id));
    }
  };

  const handleRemoveSuccess = () => {
    const countAfter = segments.count - 1;
    const lastPage = Math.ceil(countAfter / pagination.pageSize);
    if (lastPage >= pagination.currentPage || segments.items.length <= 1) {
      if (isDataType === 'AUDIENCE') {
        dispatch(getSegment(pagination));
      } else {
        dispatch(getDatasetSegment(pagination));
      }
    } else {
      setPagination({
        ...pagination,
        currentPage: pagination.currentPage > lastPage ? lastPage : pagination.currentPage,
      });
    }
    toast({
      status: 'success',
      description: t('segment.segmentMovedToTrash'),
    });
  };

  return (
    <div className="h-full">
      <Breadcrumb />
      <div className="text-left mb-6">
        <h3 className="text-2xl font-medium mb-2">{t('segment.title')}</h3>
        <p className="text-base font-normal">{t('segment.descriptionPage')}</p>
      </div>
      <TabsContainer
        isVertical={false}
        valueTab={isDataType}
        onChange={(value: string) => {
          dispatch(setDataType(value.toUpperCase()));
        }}
        listTabs={[
          {
            title: `${t('segment.audienceTitle')}`,
            value: 'AUDIENCE',
            icon: <RiVipDiamondLine size={20} />,
            child: (
              <TableSegmentList
                data={segments.items}
                count={segments.count}
                columns={
                  SegmentListColumn({
                    loadingSegment: loadingSegmentsAudience,
                    deleteSegment: handleDeleteSegment,
                  }) as ColumnDef<TSegment>[]
                }
                pagination={pagination}
                setPagination={handleUpdateList}
                isLoading={loadingSegmentsAudience}
              />
            ),
          },
          {
            title: `${t('segment.datasetTitle')}`,
            value: 'DATASET',
            icon: <RiDatabase2Line size={20} />,
            child: (
              <TableSegmentList
                data={datasetSegments.items}
                count={datasetSegments.count}
                columns={
                  SegmentListColumn({
                    loadingSegment: loadingSegmentsDataset,
                    deleteSegment: handleDeleteSegment,
                  }) as ColumnDef<TSegment>[]
                }
                pagination={pagination}
                setPagination={handleUpdateListDataset}
                isLoading={loadingSegmentsDataset}
              />
            ),
          },
        ]}
      />
    </div>
  );
};

export default SegmentList;
