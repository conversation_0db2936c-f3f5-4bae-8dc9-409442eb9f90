type Props = {
  name: string;
  email: string;
};

const PreviewSender = ({ name, email }: Props) => (
  <div className="max-w-[408px] w-full border rounded-2xl overflow-hidden shadow-sm">
    <div className="bg-secondary p-4 flex items-center gap-2">
      <div className="h-8 w-8 rounded-full bg-[#D9D9D9]" />
      <div className="flex flex-col text-sm text-left">
        <span className="font-medium">{name}</span>
        <span className="text-secondary">{email}</span>
      </div>
    </div>
    <div className="px-4 py-[47px] flex flex-col gap-6">
      {[69.91, 270.18, 221.17, 298.28].map((item) => (
        <div
          className="h-4 bg-secondary animate-pulse rounded-lg"
          key={item}
          style={{ width: `${item}px` }}
        />
      ))}
    </div>
  </div>
);
export default PreviewSender;
