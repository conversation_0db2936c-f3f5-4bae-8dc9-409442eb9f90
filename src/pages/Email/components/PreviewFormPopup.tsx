import { Dialog, DialogContent } from '@/components/ui/dialog';
import LabelValue from './PreviewForm/LabelValue';
import { t } from 'i18next';

type Props = {
  innerHtml: string;
  open: boolean;
  onClose: (input: boolean) => void;
};

const PreviewFormPopup = ({ innerHtml, open, onClose }: Props) => {
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-[920px] max-h-lvh overflow-x-hidden">
        <div className="p-6 bg-secondary rounded-2xl flex flex-col gap-6">
          <div className="flex flex-col gap-2">
            <LabelValue label={t('email.from')} value={''} />
            <LabelValue label={t('email.subject')} value={''} />
            <LabelValue label={t('email.preview')} value={''} />
          </div>
          <div className="shadow-md rounded-2xl" dangerouslySetInnerHTML={{ __html: innerHtml }} />
        </div>
      </DialogContent>
    </Dialog>
  );
};
export default PreviewFormPopup;
