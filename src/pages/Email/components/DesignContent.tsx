import { Button } from '@/components/ui/button';
import { EMAIL_ROUTER, ROOT_PATH } from '@/constants/router';
import { t } from 'i18next';
import { useNavigate } from 'react-router-dom';

const DesignContent = () => {
  const navigate = useNavigate();
  const router = `${ROOT_PATH}/${EMAIL_ROUTER.email}/${EMAIL_ROUTER.design}`;
  return (
    <div className="border w-full rounded-2xl p-4">
      <div className="flex items-center justify-between">
        {t('email.design.title')}
        <Button onClick={() => navigate(router)} type="button" variant={'outline'}>
          {t('email.startDesign')}
        </Button>
      </div>
      <p className="text-sm text-muted-foreground text-start">{t('email.desDesign')}</p>
    </div>
  );
};
export default DesignContent;
