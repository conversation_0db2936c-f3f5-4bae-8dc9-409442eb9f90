import { FormControl, FormField, FormItem } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { RiAttachmentLine } from '@remixicon/react';
import { Control, FieldPath, FieldValues } from 'react-hook-form';
import { useRef } from 'react';
import { t } from 'i18next';

type Props<T extends FieldValues> = {
  control: Control<T>;
  name: FieldPath<T>;
  accept?: string;
  onChange?: (file: File | null) => void;
};

const SelectFile = <T extends FieldValues>({
  name,
  control,
  accept = 'image/*,application/pdf',
  onChange,
}: Props<T>) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <FormField
      control={control}
      name={name}
      render={({ field: { onChange: fieldOnChange, ...fieldProps } }) => (
        <FormItem className="flex items-center gap-2 border max-w-[365px] rounded-2xl px-4 py-3 h-[97px]">
          <FormControl>
            <Input
              {...fieldProps}
              ref={fileInputRef}
              className="hidden"
              type="file"
              accept={accept}
              onChange={(e) => {
                const file = (e.target as HTMLInputElement).files?.[0] || null;
                fieldOnChange(file);
                onChange?.(file);
              }}
            />
          </FormControl>
          <div
            onClick={handleButtonClick}
            className="w-full h-full border-dashed border rounded-2xl flex items-center justify-center cursor-pointer"
          >
            <RiAttachmentLine className="w-5 h-5" />
            {t('email.selectFile')}
          </div>
        </FormItem>
      )}
    />
  );
};

export default SelectFile;
