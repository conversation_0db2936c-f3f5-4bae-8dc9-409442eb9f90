import CustomToolTips from '@/components/CustomToolTips';
import { Checkbox } from '@/components/ui/checkbox';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { RiInformation2Line } from '@remixicon/react';
import { ReactNode } from 'react';
import { Control, FieldPath, FieldValues } from 'react-hook-form';

type Props<T extends FieldValues> = {
  control: Control<T>;
  name: FieldPath<T>;
  title?: string;
  tooltipContent?: ReactNode;
};

const CheckboxField = <T extends FieldValues>({
  control,
  name,
  title,
  tooltipContent,
}: Props<T>) => (
  <FormField
    control={control}
    name={name}
    render={({ field }) => (
      <FormItem className="flex flex-col items-start gap-1 w-full p-2">
        <div className="flex items-center gap-2">
          <FormControl>
            <Checkbox
              className="w-4 h-4"
              name={name}
              checked={field.value}
              onCheckedChange={field.onChange}
            />
          </FormControl>
          <FormLabel className="flex items-center gap-2 cursor-pointer">
            {title}
            {tooltipContent && (
              <CustomToolTips
                className="bg-card border w-[346px] text-primary text-xs"
                content={tooltipContent}
                element={<RiInformation2Line className="ml-2" size={16} />}
              />
            )}
          </FormLabel>
        </div>

        <FormMessage />
      </FormItem>
    )}
  />
);
export default CheckboxField;
