import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { Control, FieldPath, FieldValues } from 'react-hook-form';

type Props<T extends FieldValues> = {
  control: Control<T>;
  name: FieldPath<T>;
  placeholder?: string;
  title?: string;
  isRequired?: boolean;
  className?: string;
  disabled?: boolean;
};

const InputField = <T extends FieldValues>({
  title,
  control,
  name,
  placeholder,
  isRequired = true,
  className,
  disabled = false,
}: Props<T>) => {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={cn('flex flex-col items-start gap-1 w-full', className)}>
          {title && (
            <FormLabel>
              {title} {isRequired && <span className="text-red-500">*</span>}
            </FormLabel>
          )}
          <FormControl>
            <Input
              disabled={disabled}
              className={cn(
                'text-sm h-10 p-3 rounded-xl',
                name in control._formState.errors
                  ? 'border-red-500'
                  : 'focus:shadow-medium focus:border-primary-crm',
              )}
              placeholder={placeholder}
              {...field}
              value={typeof field.value === 'string' ? field.value : ''}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default InputField;
