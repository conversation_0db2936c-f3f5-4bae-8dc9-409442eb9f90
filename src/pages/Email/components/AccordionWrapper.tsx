import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { ReactNode } from 'react';

type Props = {
  element: ReactNode;
  title: string;
  subTitle?: string;
};

const AccordionWrapper = ({ element, title, subTitle }: Props) => {
  return (
    <Accordion type="single" collapsible className="w-full p-4 border rounded-2xl">
      <AccordionItem className="border-none" value="item-1">
        <AccordionTrigger className="p-0">
          <div className="font-medium leading-[26px] text-base py-[3px]">{title}</div>
        </AccordionTrigger>
        {subTitle && (
          <div className="text-muted-foreground text-start text-sm mt-1">{subTitle}</div>
        )}
        <AccordionContent className="mt-3">{element}</AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};
export default AccordionWrapper;
