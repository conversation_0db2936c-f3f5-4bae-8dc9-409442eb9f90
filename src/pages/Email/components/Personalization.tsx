import { TEmailCampaign } from '@/constants/email/validate';
import { Control } from 'react-hook-form';
import CheckboxCollapsed from './CheckboxCollapsed';
import { t } from 'i18next';
import styled from 'styled-components';
import DropdownField from './form/DropdownField';

type Props = {
  control: Control<TEmailCampaign>;
};

const Personalization = ({ control }: Props) => {
  return (
    <>
      <CheckboxCollapsed<TEmailCampaign>
        control={control}
        name={'unsubscribe.isSelect'}
        title={t('email.subscription.customSubscription')}
        content={
          <Box className="border-l">
            <DropdownField<TEmailCampaign>
              control={control}
              name={'unsubscribe.unsubscribe_form_name'}
              option={[]}
              className="max-w-[247px]"
              placeholder={t('email.select')}
            />
          </Box>
        }
        tooltip={t('email.tooltip.customPage')}
      />
      <CheckboxCollapsed<TEmailCampaign>
        control={control}
        name={'profile_update_form.isSelect'}
        title={t('email.subscription.updateProfile')}
        content={
          <Box className="border-l">
            <DropdownField<TEmailCampaign>
              control={control}
              name={'profile_update_form.value'}
              option={[]}
              className="max-w-[247px]"
              placeholder={t('email.select')}
            />
          </Box>
        }
        tooltip={t('email.tooltip.updateProfile')}
      />
    </>
  );
};
export default Personalization;

const Box = styled.div`
  margin-left: 16px;
  padding-left: 8px;
`;
