import { TEmailCampaign } from '@/constants/email/validate';
import CheckboxCollapsed from './CheckboxCollapsed';
import { Control } from 'react-hook-form';
import { t } from 'i18next';
import InputField from './form/InputField';
import CheckboxField from './form/CheckboxField';
import styled from 'styled-components';
import SelectFile from './form/SelectFile';
import AttachmentTooltip from './tooltip/AttachmentTooltip';
import IgnoreList from './tooltip/IgnoreList';

type Props = {
  control: Control<TEmailCampaign>;
};

const SendingTracking = ({ control }: Props) => {
  return (
    <>
      <CheckboxCollapsed<TEmailCampaign>
        control={control}
        name={'reply_to.isSelect'}
        title={t('email.sendTracking.replyToAddress')}
        subName="reply_to.value"
        tooltip={t('email.sendTracking.replyToAddressTool')}
        placeholder={t('email.placeholder.replyAddress')}
      />
      <CheckboxCollapsed<TEmailCampaign>
        control={control}
        name={'utm_campaign.isSelect'}
        title={t('email.sendTracking.activeGoogle')}
        content={
          <Box className="border-l">
            <InputField
              className="max-w-[294px]"
              isRequired={false}
              title={t('email.sendTracking.customizeUtm')}
              control={control}
              name={'utm_campaign.value'}
              placeholder={t('email.placeholder.campaignValue')}
            />
            <CheckboxField<TEmailCampaign>
              control={control}
              name={'utm_campaign.ga_active'}
              title={t('email.sendTracking.activeUtm')}
              tooltipContent={t('email.sendTracking.activeUtmTooltip')}
            />
          </Box>
        }
      />
      <CheckboxField<TEmailCampaign>
        title={t('email.sendTracking.ignoreList')}
        control={control}
        name={'ignore_custom_list_sender'}
        tooltipContent={<IgnoreList />}
      />
      <CheckboxCollapsed<TEmailCampaign>
        tooltip={<AttachmentTooltip />}
        control={control}
        name={'attachment_url.isSelect'}
        title={t('email.sendTracking.addAttachment')}
        content={<SelectFile control={control} name={'attachment_url.value'} />}
      />
      <CheckboxCollapsed<TEmailCampaign>
        control={control}
        name={'tag.isSelect'}
        title={t('email.sendTracking.addTag')}
        subName="tag.value"
        tooltip={t('email.sendTracking.replyToAddressTool')}
        placeholder={t('email.placeholder.addTag')}
      />
      <CheckboxCollapsed<TEmailCampaign>
        control={control}
        name={'email_expiration_date.isSelect'}
        title={t('email.sendTracking.setExpiration')}
        tooltip={t('email.tooltip.expirationDate')}
        content={
          <Box>
            <div className="border-l overflow-hidden flex items-center border max-w-[318px] rounded-xl">
              <InputField
                className="[&_input]:border-none ring-0 [&_input]:rounded-none"
                control={control}
                name="email_expiration_date.duration"
              />
              <span className="text-center text-sm font-medium h-10 flex items-center w-[54px] p-1 pl-2 bg-secondary-foreground_crm">
                {t('email.days')}
              </span>
            </div>
          </Box>
        }
      />
    </>
  );
};
export default SendingTracking;

const Box = styled.div`
  margin-left: 16px;
  padding-left: 8px;
`;
