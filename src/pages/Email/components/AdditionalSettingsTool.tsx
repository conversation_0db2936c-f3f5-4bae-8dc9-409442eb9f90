import { Control, FieldPath, FieldValues } from 'react-hook-form';

type Props<T extends FieldValues> = {
  control: Control<T>;
  name: FieldPath<T>;
  placeholder?: string;
  title?: string;
};

const AdditionalSettingsTool = <T extends FieldValues>({ name }: Props<T>) => {
  switch (name) {
    case 'sender.email':
      return <div></div>;
    default:
      return <div></div>;
  }
};
export default AdditionalSettingsTool;
