import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { RiAddLine, RiSubtractLine } from '@remixicon/react';
import { ChangeEvent } from 'react';

type Props = {
  title?: string;
  value: number;
  onChange: (value: number) => void;
  className?: string;
  min?: number;
  max?: number;
};

const Counter = ({ title, value, onChange, className, min = 0, max }: Props) => {
  const handleIncrement = () => {
    if (max === undefined || value < max) {
      onChange(value + 1);
    }
  };

  const handleDecrement = () => {
    if (value > min) {
      onChange(value - 1);
    }
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newValue = parseInt(e.target.value, 10);
    if (!isNaN(newValue)) {
      // Ensure value is within bounds
      const boundedValue = Math.max(min, Math.min(max ?? Infinity, newValue));
      onChange(boundedValue);
    }
  };

  return (
    <div className={cn('flex flex-col gap-1 items-start', className)}>
      {title && <span className="text-sm text-secondary">{title}</span>}
      <div className="h-10 border rounded-xl flex items-center w-fit overflow-hidden">
        <Button
          disabled={value <= min}
          variant={'ghost'}
          className="w-10 h-full rounded-none"
          onClick={handleDecrement}
        >
          <RiSubtractLine size={20} />
        </Button>
        <input
          className="outline-none h-full flex-1 border-x max-w-[47.5px] text-center text-sm text-tertiary"
          type="number"
          value={value}
          onChange={handleInputChange}
          min={min}
          max={max}
        />
        <Button
          variant={'ghost'}
          className="w-10 h-full rounded-none"
          disabled={max !== undefined && value >= max}
          onClick={handleIncrement}
        >
          <RiAddLine size={20} />
        </Button>
      </div>
    </div>
  );
};

export default Counter;
