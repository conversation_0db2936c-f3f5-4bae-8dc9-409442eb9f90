import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { Option } from '@/types/contactList';
import { RiArrowDownSLine } from '@remixicon/react';

type Props = {
  options: Option[];
  selected: string;
  title: string;
  setSelected: (value: string) => void;
  className?: string;
  isShowLabel?: boolean;
};

const UnitSelect = ({
  options,
  selected,
  title,
  setSelected,
  className,
  isShowLabel = false,
}: Props) => (
  <div className={cn('flex items-center gap-4', className)}>
    <span className="text-secondary">{title}</span>
    <DropdownMenu>
      <DropdownMenuTrigger className="space-y-0 flex-1 max-w-[212px]">
        <div className="flex items-center border h-10 rounded-xl flex-1 w-full overflow-hidden">
          <div className="text-secondary px-3 py-2.5 text-sm flex-1 text-start capitalize">
            {isShowLabel
              ? options.find((item) => item.value === String(selected))?.label
              : selected}
          </div>
          <div className="p-2.5 border-l">
            <RiArrowDownSLine size={20} />
          </div>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="max-h-[300px] overflow-y-auto">
        {options.map((option) => (
          <DropdownMenuItem
            key={option.value}
            onClick={() => setSelected(option.value)}
            className={cn(
              'p-2.5 cursor-pointer',
              option.value === selected && 'bg-brand text-secondary-foreground_crm',
            )}
          >
            {option.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
);
export default UnitSelect;
