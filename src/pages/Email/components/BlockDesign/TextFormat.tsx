import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { RiBold, RiItalic, RiUnderline } from '@remixicon/react';
import { t } from 'i18next';
import { useState } from 'react';

type Props = {
  className?: string;
  selected?: string[];
  onChange?: (formats: string[]) => void;
};

const TextFormat = ({ className, selected = [], onChange }: Props) => {
  const [activeFormats, setActiveFormats] = useState<string[]>(selected);

  const toggleFormat = (format: string) => {
    const newFormats = activeFormats.includes(format)
      ? activeFormats.filter((f) => f !== format)
      : [...activeFormats, format];

    setActiveFormats(newFormats);
    onChange?.(newFormats);
  };

  return (
    <div className={cn('flex items-center gap-3', className)}>
      <span className="text-secondary">{t('email.textFormat')}</span>
      <div className="py-1 px-2 rounded-xl border flex items-center gap-3">
        <Button
          className={cn('p-1.5 rounded-xl w-8 h-8')}
          variant={selected.includes('bold') ? 'secondary' : 'ghost'}
          onClick={() => toggleFormat('bold')}
        >
          <RiBold size={20} />
        </Button>
        <Button
          className={cn('p-1.5 rounded-xl w-8 h-8')}
          variant={selected.includes('italic') ? 'secondary' : 'ghost'}
          onClick={() => toggleFormat('italic')}
        >
          <RiItalic size={20} />
        </Button>
        <Button
          className={cn('p-1.5 rounded-xl w-8 h-8')}
          variant={selected.includes('underline') ? 'secondary' : 'ghost'}
          onClick={() => toggleFormat('underline')}
        >
          <RiUnderline size={20} />
        </Button>
      </div>
    </div>
  );
};

export default TextFormat;
