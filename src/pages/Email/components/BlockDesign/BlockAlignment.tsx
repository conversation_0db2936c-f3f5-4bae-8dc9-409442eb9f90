import CustomToolTips from '@/components/CustomToolTips';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  RiAlignItemHorizontalCenterLine,
  RiAlignItemLeftLine,
  RiAlignItemRightLine,
  RiInformation2Line,
} from '@remixicon/react';
import { t } from 'i18next';
import { Fragment } from 'react/jsx-runtime';

type Props = {
  value: 'left' | 'center' | 'right';
  onChange: (value: 'left' | 'center' | 'right') => void;
};

const BlockAlignment = ({ value, onChange }: Props) => {
  const btns = [
    { value: 'left', icon: <RiAlignItemLeftLine size={20} /> },
    { value: 'center', icon: <RiAlignItemHorizontalCenterLine size={20} /> },
    { value: 'right', icon: <RiAlignItemRightLine size={20} /> },
  ];

  return (
    <div className="flex items-center gap-6">
      <div className="text-secondary flex gap-1 items-center">
        {t('email.blockAlignment')}
        <CustomToolTips
          className="z-50"
          content={t('email.blockAlignTooltip')}
          element={<RiInformation2Line size={20} />}
        />
      </div>
      <div className="h-10 flex items-center gap-3 border rounded-xl">
        {btns.map((btn, index) => (
          <Fragment key={btn.value}>
            <Button
              key={btn.value}
              onClick={() => onChange(btn.value as 'left' | 'center' | 'right')}
              className={cn(
                'w-8 h-8 p-1 rounded-xl',
                index === 0 && 'ml-2',
                index === 2 && 'mr-2',
                btn.value === value && 'bg-secondary',
              )}
              size={'icon'}
              variant={'ghost'}
            >
              {btn.icon}
            </Button>
            <div className={cn('h-[27.5px] bg-secondary w-[1px] my-auto last:hidden')} />
          </Fragment>
        ))}
      </div>
    </div>
  );
};
export default BlockAlignment;
