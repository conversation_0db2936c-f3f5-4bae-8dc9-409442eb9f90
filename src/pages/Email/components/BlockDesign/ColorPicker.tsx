import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { SOLIDS_COLOR } from '@/utils/colorPicker';
import { useTranslation } from 'react-i18next';

type Props = {
  color: string;
  setColor: (color: string) => void;
  title?: string;
};

const ColorPicker = ({ color, setColor, title }: Props) => {
  const { t } = useTranslation();
  return (
    <div className="flex items-center gap-4">
      <div className="flex gap-1 items-end mb-[4px] text-secondary">
        {title ?? t('email.color')}
      </div>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant={'outline'}
            className={cn('w-10 h-10 p-3 justify-start text-left font-normal hover:bg-transparent')}
            type={'button'}
            style={{ background: color }}
          ></Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-2">
          <div className="grid grid-cols-10 gap-1">
            {SOLIDS_COLOR.map((s) => (
              <div
                key={s}
                style={{ background: s }}
                className={cn(
                  'rounded-full h-6 w-6 cursor-pointer active:scale-105',
                  s === '#FFFFFF' && 'border border-gray-300',
                )}
                onClick={() => setColor(s)}
              />
            ))}
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};
export default ColorPicker;
