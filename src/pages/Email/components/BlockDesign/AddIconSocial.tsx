import { t } from 'i18next';
import { RiDeleteBin5Line, RiUploadCloud2Line } from '@remixicon/react';
import { useRef, useState } from 'react';
import { fSize } from '@/utils/number';
import { Button } from '@/components/ui/button';

const AddIconSocial = () => {
  const [file, setFile] = useState<File | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const droppedFile = event.dataTransfer.files[0];
    if (droppedFile) setFile(droppedFile);
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setFile(event.target.files[0]);
    }
  };

  const handleClick = () => {
    inputRef.current?.click();
  };
  const handleRemoveFile = () => {
    inputRef.current!.value = '';
    setFile(null);
  };
  return (
    <div className="bg-secondary p-4 rounded-2xl">
      {label(t('email.label'))}
      <div
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        className="bg-card p-6 flex items-center gap-6 mb-3 border rounded-2xl mt-2"
      >
        {file ? (
          <div className="flex items-center gap-4 w-full">
            <img
              className="w-[76px] h-[76px] object-contain"
              src={URL.createObjectURL(file)}
              alt=""
            />
            <div className="flex text-secondary text-sm flex-col">
              <span className="">{file.name}</span>
              <span>{fSize(file.size)}</span>
            </div>
            <Button
              onClick={handleRemoveFile}
              variant={'outline'}
              className="ml-auto"
              size={'icon'}
            >
              <RiDeleteBin5Line size={20} />
            </Button>
          </div>
        ) : (
          <>
            <div className="p-3 rounded-full bg-secondary">
              <RiUploadCloud2Line size={24} />
            </div>
            <div className="flex flex-col gap-2 text-sm text-secondary">
              <div className="flex items-center gap-1">
                <span>{t('email.drop')}</span>
                <button onClick={handleClick} className="p-0 lowercase text-brand">
                  {t('common.button.select')}
                </button>
                <span>{t('email.fromFile')}</span>
              </div>
              <div>{t('email.maximumFileSize')}</div>
            </div>
          </>
        )}
        <input
          ref={inputRef}
          onChange={(e) => handleFileSelect(e)}
          type="file"
          className="hidden"
        />
      </div>
      {label(t('email.link'))}
      <input
        placeholder={t('email.customLink')}
        className="mt-1 w-full px-3 py-2.5 outline-none rounded-xl text-sm"
      />
    </div>
  );
};
export default AddIconSocial;

const label = (value: string) => (
  <div className="flex font-medium items-center gap-1 text-sm">
    {value}
    <span className="text-red-500">*</span>
  </div>
);
