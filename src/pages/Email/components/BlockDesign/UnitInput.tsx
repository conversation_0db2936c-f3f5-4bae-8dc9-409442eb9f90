import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import { RiArrowDownSLine } from '@remixicon/react';
import { cn } from '@/lib/utils';
import { useEffect, useState } from 'react';
import { parseValueAndUnit } from '@/utils/string';

type Props = {
  value: string;
  setValue: (value: string) => void;
  title: string;
  className?: string;
  units?: string[] | string;
  defaultUnit?: string;
};

const UnitInput = ({ title, value, setValue, units, defaultUnit, className }: Props) => {
  const [selected, setSelected] = useState(parseValueAndUnit(value).unit || defaultUnit);
  const [input, setInput] = useState(parseValueAndUnit(value).value);

  useEffect(() => {
    if (!Array.isArray(units)) return;
    setValue(`${input}${selected}`);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [input, selected]);

  return (
    <div className={cn('flex items-center gap-4', className)}>
      <span className="text-secondary">{title}</span>
      <div className="flex items-center border max-w-[212px] rounded-xl overflow-hidden">
        <Input
          value={Array.isArray(units) ? input : value}
          onChange={(e) =>
            Array.isArray(units) ? setInput(e.currentTarget.value) : setValue(e.currentTarget.value)
          }
          className="rounded-none border-none"
        />
        {Array.isArray(units) ? (
          <DropdownMenu>
            <DropdownMenuTrigger className="border-l p-2.5">
              <div className="flex items-center text-sm font-medium">
                {selected}
                <RiArrowDownSLine size={20} />
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              {units.map((option) => (
                <DropdownMenuItem
                  key={option}
                  onClick={() => setSelected(option)}
                  className={cn(
                    'p-2.5 cursor-pointer',
                    option === selected && 'bg-brand text-secondary-foreground_crm',
                  )}
                >
                  {option}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        ) : (
          <div className="w-[50px] text-sm font-medium flex items-center justify-center bg-secondary h-10">
            {units}
          </div>
        )}
      </div>
    </div>
  );
};
export default UnitInput;
