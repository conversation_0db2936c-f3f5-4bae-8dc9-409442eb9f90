import EmojiPicker from '@/components/EmojiPicker';
import { cn } from '@/lib/utils';

type Props = {
  value: string;
  onChange: (value: string) => void;
  title?: string;
  className?: string;
  isEmoji?: boolean;
  placeholder?: string;
};

const ContentInput = ({
  placeholder,
  value = '',
  onChange,
  title,
  className,
  isEmoji = true,
}: Props) => {
  return (
    <div className={cn('flex items-center w-full gap-6', className)}>
      {title && <span className="text-secondary">{title}</span>}
      <div
        className={`border h-10 flex-1 max-w-[489px] rounded-2xl px-3 py-2.5 flex items-center gap-[5px]`}
      >
        <input
          placeholder={placeholder}
          className="flex-1 outline-none text-sm"
          type="text"
          value={value}
          onChange={(e) => onChange(e.currentTarget.value)}
        />
        {isEmoji && <EmojiPicker onEmojiSelect={(emoji) => onChange(value + emoji.native)} />}
      </div>
    </div>
  );
};

export default ContentInput;
