import { ItemDesignTypes } from '@/constants/email';
import { cn } from '@/lib/utils';
import { TTypeBlock } from '@/types/email';
import { RiDraggable } from '@remixicon/react';
import { useDrag } from 'react-dnd';

type Props = {
  icon: React.ReactNode;
  label: string;
  type: TTypeBlock;
};

const EditerBlock = ({ icon, label, type }: Props) => {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: ItemDesignTypes.TOOLBAR_ITEM,
    item: { type: type, label, icon },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  }));

  return (
    <div
      ref={drag}
      className={cn(
        'w-[102px] h-[102px] flex flex-col items-center cursor-grab justify-between border py-2 rounded-xl shadow-medium',
        isDragging && 'opacity-50',
      )}
    >
      <RiDraggable size={20} className="rotate-90" />
      {icon}
      <span className="text-xs text-gray-600">{label}</span>
    </div>
  );
};
export default EditerBlock;
