import { RiArrowDownCircleLine } from '@remixicon/react';
import { t } from 'i18next';

const EmptyBlock = () => {
  return (
    <div
      style={{
        background: `
      linear-gradient(to right, #d1d5db 50%, transparent 50%) top/20px 1px repeat-x,
      linear-gradient(to right, #d1d5db 50%, transparent 50%) bottom/20px 1px repeat-x,
      linear-gradient(to bottom, #d1d5db 50%, transparent 50%) left/1px 20px repeat-y,
      linear-gradient(to bottom, #d1d5db 50%, transparent 50%) right/1px 20px repeat-y
    `,
        backgroundOrigin: 'padding-box',
        padding: '1px',
      }}
      className="max-w-[390px] flex flex-col items-center justify-center gap-3 rounded-2xl w-full h-[118px]"
    >
      <RiArrowDownCircleLine size={40} />
      <span className="text-xs text-muted-foreground">{t('email.dropBlock')}</span>
    </div>
  );
};
export default EmptyBlock;
