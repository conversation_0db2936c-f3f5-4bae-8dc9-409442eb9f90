import { TTypeBlock } from '@/types/email';
import {
  RiAlignLeft,
  RiCodeView,
  RiImageLine,
  RiLinkM,
  RiRadioButtonLine,
  RiShareLine,
  RiText,
  RiVideoLine,
} from '@remixicon/react';
import { t } from 'i18next';

type Props = {
  icon: React.ReactNode;
  label: string;
  type: TTypeBlock;
};

export const toolbarConfig = (): Props[] => {
  return [
    { type: 'title', icon: <RiText size={32} />, label: t('email.blockDesign.title') },
    { type: 'text', icon: <RiAlignLeft size={32} />, label: t('email.blockDesign.text') },
    { type: 'image', icon: <RiImageLine size={32} />, label: t('email.blockDesign.image') },
    { type: 'video', icon: <RiVideoLine size={32} />, label: t('email.blockDesign.video') },
    { type: 'button', icon: <RiRadioButtonLine size={32} />, label: t('email.blockDesign.button') },
    { type: 'html', icon: <RiCodeView size={32} />, label: t('email.blockDesign.html') },
    { type: 'social', icon: <RiShareLine size={32} />, label: t('email.blockDesign.social') },
    { type: 'navigation', icon: <RiLinkM size={32} />, label: t('email.blockDesign.link') },
    {
      type: 'divider',
      icon: (
        <svg
          width="33"
          height="33"
          viewBox="0 0 33 33"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <line
            x1="0.888672"
            y1="15.2373"
            x2="32.8887"
            y2="15.2373"
            stroke="#515667"
            strokeWidth="2"
            strokeDasharray="4 4"
          />
        </svg>
      ),
      label: t('email.blockDesign.divider'),
    },
    {
      type: 'spacer',
      icon: (
        <svg
          width="32"
          height="33"
          viewBox="0 0 32 33"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect
            x="1"
            y="1.2373"
            width="30"
            height="30"
            rx="7"
            stroke="#515667"
            strokeWidth="2"
            strokeDasharray="10 10"
          />
        </svg>
      ),
      label: t('email.blockDesign.spacer'),
    },
  ];
};
