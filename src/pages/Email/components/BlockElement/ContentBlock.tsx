import { ItemDesignTypes } from '@/constants/email';
import { TBlockDesign } from '@/types/email';
import { useRef } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import Video from '../DesignForm/Video';
import Social from '../DesignForm/Social';
import ImageBlock from '../DesignForm/ImageBlock';
import DividerBlock from '../DesignForm/DividerBlock';
import ButtonBlock from '../DesignForm/ButtonBlock';
import TextBlock from '../DesignForm/TextBlock';
import TitleBlock from '../DesignForm/TitleBlock';
import LinkBlock from '../DesignForm/LinkBlock';

type Props = {
  index: number;
  moveBlock: (dragIndex: number, hoverIndex: number) => void;
  removeBlock: (id: string) => void;
} & TBlockDesign;

const ContentBlock = (props: Props) => {
  const { id, type, index, moveBlock } = props;
  const ref = useRef<HTMLDivElement>(null);

  interface DragItem {
    id: string;
    index: number;
    type: string;
  }

  const [{ handlerId }, drop] = useDrop<DragItem, void, { handlerId: string | symbol | null }>({
    accept: ItemDesignTypes.CONTENT_BLOCK,
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      };
    },
    hover(item, monitor) {
      if (!ref.current) {
        return;
      }

      const dragIndex = item.index;
      const hoverIndex = index;

      if (dragIndex === hoverIndex) {
        return;
      }

      const hoverRect = ref.current.getBoundingClientRect();
      const hoverMiddleY = (hoverRect.bottom - hoverRect.top) / 2;
      const clientOffset = monitor.getClientOffset();

      if (!clientOffset) {
        return;
      }

      const hoverClientY = clientOffset.y - hoverRect.top;

      if (
        (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) ||
        (dragIndex > hoverIndex && hoverClientY > hoverMiddleY)
      ) {
        return;
      }

      moveBlock(dragIndex, hoverIndex);
      item.index = hoverIndex;
    },
  });

  const [{ isDragging }, drag] = useDrag({
    type: ItemDesignTypes.CONTENT_BLOCK,
    item: (): DragItem => ({
      id,
      index,
      type: ItemDesignTypes.CONTENT_BLOCK,
    }),
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  drag(drop(ref));

  const block = () => {
    switch (type) {
      case 'title':
        return <TitleBlock />;
      case 'text':
        return <TextBlock />;
      case 'video':
        return <Video />;
      case 'social':
        return <Social />;
      case 'image':
        return <ImageBlock />;
      case 'divider':
        return <DividerBlock />;
      case 'button':
        return <ButtonBlock />;
      case 'navigation':
        return <LinkBlock />;
      default:
        return null;
    }
  };

  return (
    <div
      style={{
        borderRadius: '10px',
        display: 'flex',
        flexDirection: 'row',
        opacity: isDragging ? '0.5' : '1',
      }}
      ref={ref}
      data-handler-id={handlerId}
    >
      {block()}
    </div>
  );
};

export default ContentBlock;
