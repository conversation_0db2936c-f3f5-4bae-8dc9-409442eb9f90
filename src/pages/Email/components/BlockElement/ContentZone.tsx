import { ItemDesignTypes } from '@/constants/email';
import { cn } from '@/lib/utils';
import { TBlockDesign } from '@/types/email';
import { useDrop } from 'react-dnd';
import EmptyBlock from './EmptyBlock';
import ContentBlock from './ContentBlock';
import { useEffect, useRef } from 'react';

type Props = {
  blocks: TBlockDesign[];
  setBlocks: React.Dispatch<React.SetStateAction<TBlockDesign[]>>;
  setPreview: (value: string) => void;
};

const ContentZone = ({ blocks, setBlocks, setPreview }: Props) => {
  const blockRef = useRef<HTMLDivElement>(null);
  const [{ canDrop, isOver }, drop] = useDrop(() => ({
    accept: [ItemDesignTypes.TOOLBAR_ITEM, ItemDesignTypes.CONTENT_BLOCK],
    drop: (item: TBlockDesign, monitor) => {
      if (monitor.getItemType() === ItemDesignTypes.TOOLBAR_ITEM) {
        const newBlock = {
          id: Date.now().toString(),
          type: item.type,
          label: item.label,
          icon: item.icon,
        };
        setBlocks((prev) => [...prev, newBlock]);
      }
      return undefined;
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  }));

  const moveBlock = (dragIndex: number, hoverIndex: number) => {
    setBlocks((prevBlocks) => {
      const newBlocks = [...prevBlocks];
      const [removed] = newBlocks.splice(dragIndex, 1);
      newBlocks.splice(hoverIndex, 0, removed);
      return newBlocks;
    });
  };

  useEffect(() => {
    setPreview(blockRef.current?.outerHTML.toString() || '');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [blockRef.current?.outerHTML, blocks]);

  const removeBlock = (id: string) => {
    setBlocks((prevBlocks) => prevBlocks.filter((block) => block.id !== id));
  };

  const isActive = canDrop && isOver;

  return (
    <div
      ref={drop}
      className={cn(
        'shadow-medium flex-1 min-h-[261px] rounded-2xl',
        blocks.length === 0 ? 'flex flex-col items-center justify-center' : '',
        isActive ? '' : '',
      )}
    >
      {blocks.length === 0 ? (
        <EmptyBlock />
      ) : (
        <div
          ref={blockRef}
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '8px',
            backgroundColor: '#ffffff',
            padding: '24px',
            borderRadius: '16px',
          }}
        >
          {blocks.map((block, index) => (
            <ContentBlock
              key={block.id}
              index={index}
              moveBlock={moveBlock}
              removeBlock={removeBlock}
              id={block.id}
              type={block.type}
              label={block.label}
              icon={block.icon}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default ContentZone;
