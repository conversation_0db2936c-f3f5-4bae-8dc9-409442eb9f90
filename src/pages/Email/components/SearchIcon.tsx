import SearchBar from '@/components/SearchBar';
import { Button } from '@/components/ui/button';
import { RiAddLine } from '@remixicon/react';
import { t } from 'i18next';

type Props = {
  setSearchQuery: (value: string) => void;
  value: string;
  onClick: () => void;
};

const SearchIcon = ({ setSearchQuery, value, onClick }: Props) => {
  return (
    <div className="flex items-center justify-between gap-4">
      <SearchBar
        value={value}
        setSearchQuery={setSearchQuery}
        className="flex-1 h-10 [&_input]:bg-inherit"
        placeholder={t('email.searchIconPlaceholder')}
      />
      <Button
        onClick={onClick}
        className="p-3 rounded-xl font-medium text-sm"
        variant={'outline'}
        size={'lg'}
      >
        <RiAddLine />
        {t('email.addIconCustom')}
      </Button>
    </div>
  );
};
export default SearchIcon;
