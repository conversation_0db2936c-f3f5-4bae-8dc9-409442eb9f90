import { RiInformation2Line } from '@remixicon/react';
import { ReactNode } from 'react';

type Props = {
  content: string;
  rightContent?: ReactNode;
};

const NoticeLabel = ({ content, rightContent }: Props) => (
  <div className="flex items-center h-8 bg-secondary p-2 rounded-md text-secondary text-sm gap-1">
    <RiInformation2Line size={20} />
    {content}
    {rightContent}
  </div>
);
export default NoticeLabel;
