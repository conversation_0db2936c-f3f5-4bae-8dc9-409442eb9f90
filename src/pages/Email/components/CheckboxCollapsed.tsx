import { Control, FieldPath, FieldValues } from 'react-hook-form';
import Check<PERSON><PERSON>ield from './form/CheckboxField';
import InputField from './form/InputField';
import { ReactNode } from 'react';

type Props<T extends FieldValues> = {
  control: Control<T>;
  name: FieldPath<T>;
  placeholder?: string;
  title?: string;
  subName?: FieldPath<T>;
  subTitle?: string;
  tooltip?: ReactNode;
  content?: ReactNode;
};

const CheckboxCollapsed = <T extends FieldValues>({
  control,
  name,
  title,
  subName,
  subTitle,
  tooltip,
  content,
  placeholder,
}: Props<T>) => {
  return (
    <div className="flex flex-col gap-1">
      <CheckboxField control={control} name={name} title={title} tooltipContent={tooltip} />
      {control._getWatch(name) &&
        (content ? (
          content
        ) : (
          <div className="ml-4 border-l pl-2">
            <InputField
              className="max-w-[294px]"
              control={control}
              name={subName!}
              title={subTitle}
              placeholder={placeholder}
            />
          </div>
        ))}
    </div>
  );
};
export default CheckboxCollapsed;
