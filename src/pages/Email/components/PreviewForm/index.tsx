import { Button } from '@/components/ui/button';
import { RiEyeLine } from '@remixicon/react';
import { t } from 'i18next';
import LabelValue from './LabelValue';
import { Control } from 'react-hook-form';
import { TEmailCampaign } from '@/constants/email/validate';

type Props = {
  control: Control<TEmailCampaign>;
};

const PreviewForm = ({ control }: Props) => {
  return (
    <div className="col-span-1 border h-fit sticky top-2 py-6 px-4 rounded-2xl shadow-light bg-secondary">
      <div className="text-sm font-medium text-start mb-3">{t('email.preview')}</div>
      <LabelValue label={t('email.from')} value={''} />
      <LabelValue label={t('email.subject')} value={control._getWatch('subject.subject_line')} />
      <LabelValue label={t('email.preview')} value={control._getWatch('subject.preview_text')} />
      <div className="min-h-[307px] flex flex-col mt-3 items-center justify-center gap-2.5 w-full bg-black/30 rounded-xl">
        <Button size={'sm'} variant={'primary'} className="w-[119px] font-medium">
          {t('common.button.edit')}
        </Button>
        <Button size={'sm'} variant={'secondary'} className="font-medium hover:bg-muted-foreground">
          <RiEyeLine size={16} /> {t('email.preview')}
        </Button>
      </div>
    </div>
  );
};
export default PreviewForm;
