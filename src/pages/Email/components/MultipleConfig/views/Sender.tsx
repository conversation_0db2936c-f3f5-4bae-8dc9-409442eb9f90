import DataTable from '@/components/table/DataTable';
import { SenderContext } from '@/pages/Email/context/senderProvider';
import { useContext, useState } from 'react';
import senderCol from '../col/senderCol';
import SearchBar from '@/components/SearchBar';
import HeaderModal from '../components/HeaderModal';
import { t } from 'i18next';
import SenderNote from '../components/SenderNote';

const Sender = () => {
  const [filter, setFilter] = useState<{
    search: string;
    currentPage: number;
    pageSize: number;
  }>({
    search: '',
    currentPage: 1,
    pageSize: 10,
  });
  const { state } = useContext(SenderContext);

  return (
    <div className="flex flex-col gap-3">
      <HeaderModal title={t('email.sender')} subTitle={t('email.subSender')} />
      <SenderNote />
      <SearchBar
        className="max-w-[361px]"
        placeholder={t('email.placeholder.searchSender')}
        value={filter.search}
        setSearchQuery={(value) => setFilter({ ...filter, search: value })}
      />
      <DataTable
        classNameBody="mt-1"
        className="min-h-[336px]"
        data={state.list}
        columns={senderCol()}
        total={state.list.length}
        pagination={{ currentPage: filter?.currentPage, pageSize: filter?.pageSize }}
        setPagination={(value) => setFilter({ ...filter, ...value })}
      />
    </div>
  );
};
export default Sender;
