import { t } from 'i18next';
import HeaderModal from '../components/HeaderModal';
import SearchBar from '@/components/SearchBar';
import { useState } from 'react';

const Domains = () => {
  const [filter, setFilter] = useState<{
    search: string;
    currentPage: number;
    pageSize: number;
  }>({ search: '', currentPage: 1, pageSize: 10 });

  return (
    <div className="flex flex-col gap-3">
      <HeaderModal title={t('email.sender')} subTitle={t('email.subSender')} />
      <SearchBar
        className="max-w-[361px]"
        placeholder={t('email.placeholder.searchSender')}
        value={filter.search}
        setSearchQuery={(value) => setFilter({ ...filter, search: value })}
      />
    </div>
  );
};
export default Domains;
