import { t } from 'i18next';

type Props = {
  isVerify: boolean;
};

const SenderStatusCel = ({ isVerify }: Props) => {
  switch (isVerify) {
    case true:
      return (
        <div className="p-1 mx-auto rounded-md text-xs w-fit text-[#205B2B] text-center bg-[#E0F8E3]">
          {t('email.verify')}
        </div>
      );
    case false:
      return (
        <div className="p-1 mx-auto rounded-md text-xs w-fit text-[#BF1616] text-center bg-[#FFD9D9]">
          {t('email.unverify')}
        </div>
      );
    default:
      return (
        <div className="p-1 rounded-md mx-auto text-xs w-fit text-gray-500 bg-gray-200">
          {t('email.invalid')}
        </div>
      );
  }
};
export default SenderStatusCel;
