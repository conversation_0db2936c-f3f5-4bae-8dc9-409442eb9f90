import { Button } from '@/components/ui/button';
import { TEmailInfo } from '@/types/email';
import { t } from 'i18next';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { RiEdit2Line, RiMore2Line } from '@remixicon/react';
import { useNavigate } from 'react-router-dom';
import { EMAIL_ROUTER, ROOT_PATH } from '@/constants/router';
import ConfirmDeleteSender from '../components/ConfirmDeleteSender';

const ActionSenderCel = (props: TEmailInfo) => {
  const { is_verified, id } = props;
  const navigation = useNavigate();

  const handleVerify = () => {
    navigation(`${ROOT_PATH}/${EMAIL_ROUTER.email}/${EMAIL_ROUTER.verifySender}/${id}`);
  };
  const handleEdit = () => {
    navigation(`${ROOT_PATH}/${EMAIL_ROUTER.email}/${EMAIL_ROUTER.sender}/${id}`);
  };

  return (
    <div className="flex items-center justify-end gap-2">
      <Button
        onClick={is_verified ? handleEdit : handleVerify}
        className="w-20 rounded-xl text-sm font-medium"
        variant={'outline'}
        size={'sm'}
      >
        {!is_verified ? t('common.button.verify') : t('common.button.edit')}
      </Button>
      <DropdownMenu>
        <DropdownMenuTrigger>
          <Button
            className="p-1 rounded-xl w-8 hover:bg-secondary"
            size={'sm'}
            variant={'secondary'}
          >
            <RiMore2Line size={20} />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="p-2 rounded-xl gap-1 flex flex-col">
          {!is_verified && (
            <DropdownMenuItem
              onClick={handleEdit}
              className="cursor-pointer text-sm font-medium gap-1"
            >
              <RiEdit2Line size={16} />
              {t('common.button.edit')}
            </DropdownMenuItem>
          )}
          <ConfirmDeleteSender id={id} />
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
export default ActionSenderCel;
