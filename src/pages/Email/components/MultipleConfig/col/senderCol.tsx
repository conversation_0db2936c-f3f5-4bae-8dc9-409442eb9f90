import { TEmailInfo } from '@/types/email';
import { ColumnDef } from '@tanstack/react-table';
import { t } from 'i18next';
import ActionSenderCel from '../cel/ActionSenderCel';
import SenderStatusCel from '../cel/SenderStatusCel';

const senderCol = (): ColumnDef<TEmailInfo>[] => {
  return [
    {
      accessorKey: 'sender',
      header: () => <div>{t('email.name')}</div>,
      cell: ({ row }) => <div>{row.original.name}</div>,
      size: 209,
    },
    {
      accessorKey: 'sender',
      header: () => <div>{t('email.sender')}</div>,
      cell: ({ row }) => <div>{row.original.email}</div>,
      size: 209,
    },
    {
      accessorKey: 'status',
      header: () => <div className="w-full text-center">{t('email.status')}</div>,
      cell: ({ row }) => <SenderStatusCel isVerify={row.original.is_verified} />,
      size: 120,
    },
    {
      accessorKey: 'setting',
      header: () => '',
      cell: ({ row }) => <ActionSenderCel {...row.original} />,
    },
  ];
};

export default senderCol;
