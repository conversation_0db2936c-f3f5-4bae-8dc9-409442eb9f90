import { EMAIL_ROUTER_CONFIG } from '@/constants/router';
import { cn } from '@/lib/utils';
import { RiAccountCircleLine, RiGlobalLine, RiMapPin5Line } from '@remixicon/react';
import { t } from 'i18next';
import { Link, useLocation } from 'react-router-dom';

const TabsSelect = () => {
  const { pathname } = useLocation();
  const tabConfig = [
    {
      title: t('email.sender'),
      url: EMAIL_ROUTER_CONFIG.SENDER,
      icon: <RiAccountCircleLine size={24} />,
    },
    {
      title: t('email.domains'),
      url: EMAIL_ROUTER_CONFIG.DOMAINS,
      icon: <RiGlobalLine size={24} />,
    },
    {
      title: t('email.dedicatedIps'),
      url: EMAIL_ROUTER_CONFIG.DEDICATED_IPS,
      icon: <RiMapPin5Line size={24} />,
    },
  ];

  const pathArray = pathname.split('/');

  return (
    <div className="flex items-center border-b gap-6">
      {tabConfig.map((tab, index) => (
        <Link key={index} to={tab.url}>
          <div
            className={cn(
              'flex items-center gap-2 pl-2 py-4 pr-10 text-tertiary font-medium',
              pathArray.includes(tab.url) && 'text-brand border-b-[2px] border-brand',
            )}
          >
            {tab.icon} {tab.title}
          </div>
        </Link>
      ))}
    </div>
  );
};
export default TabsSelect;
