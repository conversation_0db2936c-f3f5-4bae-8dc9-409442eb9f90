import { emailApis } from '@/apis/emailApis';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { toast } from '@/hooks/use-toast';
import { SenderContext } from '@/pages/Email/context/senderProvider';
import { RiDeleteBin6Line, RiErrorWarningLine, RiLoader2Line } from '@remixicon/react';
import { t } from 'i18next';
import { useContext, useState } from 'react';

type Props = { id: string };

const ConfirmDeleteSender = ({ id }: Props) => {
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const { dispatch } = useContext(SenderContext);

  const handleDelete = async () => {
    try {
      setLoading(true);
      await emailApis.deleteSender(id);
      dispatch({ type: 'DELETE', payload: { id } });
      toast({
        title: t('email.deleteSenderSuccess'),
        status: 'success',
      });
      setOpen(false);
    } catch (error) {
      return error;
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger className="cursor-pointer flex items-center hover:bg-secondary h-8 p-2 rounded-xl w-full text-sm font-medium text-destructive gap-1">
        <RiDeleteBin6Line size={16} />
        {t('common.button.delete')}
      </DialogTrigger>

      <DialogContent className="items-center flex flex-col">
        <RiErrorWarningLine size={80} color="#F53E3E" />
        <div className="flex flex-col items-center">
          <span className="text-lg font-medium">{t('email.deleteSender')}</span>
          <span className="text-sm text-secondary">{t('email.confirmDeleteSender')}</span>
        </div>
        <div className="w-full grid grid-cols-2 gap-4 mt-4">
          <Button onClick={() => setOpen(false)} variant={'cancel'}>
            {t('common.button.cancel')}
          </Button>
          <Button onClick={handleDelete} variant={'delete'}>
            {loading ? (
              <RiLoader2Line size={20} className="animate-spin" />
            ) : (
              t('common.button.delete')
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
export default ConfirmDeleteSender;
