import { RiAlertLine } from '@remixicon/react';
import { t } from 'i18next';

const URL_BREVO =
  'https://help.brevo.com/hc/en-us/articles/14925263522578-Comply-with-Gmail-and-Yahoo-s-requirements-for-email-senders';

const SenderNote = () => (
  <div className="flex items-start gap-2 p-3 rounded-xl bg-[#FFFCEB]">
    <RiAlertLine size={20} color="#D98206" />
    <div className="text-start text-xs text-secondary">
      <span>{t('email.senderNote1')} </span>
      <a className="underline" href={URL_BREVO} target="_blank">
        {t('email.noteLink')}
      </a>
      <span>{t('email.senderNote2')}</span>
    </div>
  </div>
);
export default SenderNote;
