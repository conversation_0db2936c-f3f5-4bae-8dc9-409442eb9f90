import { cn } from '@/lib/utils';
import { RiInformation2Line } from '@remixicon/react';

type Props = {
  title: string;
  className?: string;
};

const NoteTitle = ({ title, className }: Props) => (
  <div className={cn('flex items-start gap-1', className)}>
    <RiInformation2Line size={16} className="flex-shrink-0" />
    <div className="text-left text-xs">{title}</div>
  </div>
);
export default NoteTitle;
