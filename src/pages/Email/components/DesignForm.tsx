import { TEmailCampaign } from '@/constants/email/validate';
import { Control } from 'react-hook-form';
import CheckboxCollapsed from './CheckboxCollapsed';
import { t } from 'i18next';
import CheckboxField from './form/CheckboxField';

type Props = {
  control: Control<TEmailCampaign>;
};

const DesignForm = ({ control }: Props) => {
  return (
    <>
      <CheckboxCollapsed<TEmailCampaign>
        control={control}
        name={'header.isSelect'}
        subName={'header.value'}
        title={t('email.design.header')}
        tooltip={t('email.tooltip.header')}
        placeholder={t('email.placeholder.header')}
      />
      <CheckboxCollapsed<TEmailCampaign>
        control={control}
        name={'footer.isSelect'}
        title={t('email.design.footer')}
        subName={'footer.value'}
        tooltip={t('email.tooltip.footer')}
        placeholder={t('email.placeholder.footer')}
      />
      <CheckboxField<TEmailCampaign>
        control={control}
        name={'mirror_active'}
        title={t('email.design.browser')}
        tooltipContent={t('email.tooltip.browser')}
      />
    </>
  );
};
export default DesignForm;
