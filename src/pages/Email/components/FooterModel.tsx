import { Button } from '@/components/ui/button';
import { RiArrowLeftSLine } from '@remixicon/react';
import { t } from 'i18next';
import { useNavigate } from 'react-router-dom';

const FooterModel = () => {
  const navigate = useNavigate();
  return (
    <div className="flex items-center justify-between w-full">
      <Button variant={'ghost'} className="p-3 rounded-xl" onClick={() => navigate(-1)}>
        <RiArrowLeftSLine size={20} />
        {t('common.button.cancel')}
      </Button>
      <div className="flex items-center gap-3">
        <Button className="p-3 rounded-xl" size={'lg'} variant={'cancel'}>
          {t('email.previewTest')}
        </Button>
        <Button className="p-3 rounded-xl" size={'lg'} variant={'primary'}>
          {t('common.button.schedule')}
        </Button>
      </div>
    </div>
  );
};
export default FooterModel;
