import { Button } from '@/components/ui/button';
import { EMAIL_ROUTER, ROOT_PATH } from '@/constants/router';
import { RiAccountCircleLine, RiAddCircleLine, RiArrowDownSLine } from '@remixicon/react';
import { t } from 'i18next';
import { useContext, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { SenderContext } from '../context/senderProvider';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

const SenderFeature = () => {
  const { state } = useContext(SenderContext);
  const [openModal, setOpenModal] = useState(false);
  const navigate = useNavigate();
  const router = `${ROOT_PATH}/${EMAIL_ROUTER.email}/${EMAIL_ROUTER.create_sender}`;
  const viewMoreRouter = `${ROOT_PATH}/${EMAIL_ROUTER.email}/${EMAIL_ROUTER.multiple_config}`;

  return (
    <div className="flex items-center justify-between">
      <Popover open={openModal} onOpenChange={setOpenModal}>
        <PopoverTrigger>
          <div className="flex font-medium items-center h-10 min-w-[244px] gap-2 border px-4 py-2 rounded-2xl">
            <RiAccountCircleLine size={20} />
            {t('email.gmailAccount')}
            <RiArrowDownSLine className="flex-shrink-0 ml-auto" size={20} />
          </div>
        </PopoverTrigger>
        <PopoverContent className="max-w-[244px] overflow-x-hidden max-h-[288px] rounded-xl p-2 gap-1 flex flex-col">
          {state.list.map((item) => (
            <Button
              className="w-full text-start  justify-start text-base font-medium rounded-md"
              variant={'ghost'}
              size={'sm'}
              key={item.brevo_email_id}
            >
              <span className="max-w-[244px] truncate">{item.email}</span>
            </Button>
          ))}
          <Button
            size={'sm'}
            variant={'secondary'}
            className="w-full sticky bottom-0 font-medium"
            onClick={() => navigate(viewMoreRouter)}
          >
            {t('common.button.viewMore')}
          </Button>
        </PopoverContent>
      </Popover>
      <Button
        onClick={() => navigate(router)}
        className="p-3 rounded-xl"
        size={'lg'}
        variant={'cancel'}
      >
        <RiAddCircleLine size={20} />
        {t('email.addSender')}
      </Button>
    </div>
  );
};
export default SenderFeature;
