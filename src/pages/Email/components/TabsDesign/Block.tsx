import { Dnd<PERSON>rovider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { toolbarConfig } from '../BlockElement/toolbarConfig';
import EditerBlock from '../BlockElement/EditerBlock';
import { useState } from 'react';
import { TBlockDesign } from '@/types/email';
import ContentZone from '../BlockElement/ContentZone';
import { Button } from '@/components/ui/button';
import { t } from 'i18next';
import { RiEyeLine } from '@remixicon/react';
import PreviewFormPopup from '../PreviewFormPopup';

const Block = () => {
  const [blocks, setBlocks] = useState<TBlockDesign[]>([]);
  const [preview, setPreview] = useState<string>('');
  const [openPreview, setOpenPreview] = useState(false);

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="flex gap-6 overflow-auto p-6">
        <div className="flex flex-col items-center gap-[7.11px]">
          {toolbarConfig().map((item, index) => (
            <EditerBlock key={index} icon={item.icon} type={item.type} label={item.label} />
          ))}
        </div>
        <ContentZone setPreview={setPreview} blocks={blocks} setBlocks={setBlocks} />
      </div>
      <div className="p-6 pt-0 flex items-center justify-end gap-3">
        <Button onClick={() => setOpenPreview(true)} variant={'outline'}>
          <RiEyeLine size={20} />
          {t('common.button.preview')}
        </Button>
        <Button variant={'primary'}>{t('common.button.save')}</Button>
      </div>
      <PreviewFormPopup innerHtml={preview} open={openPreview} onClose={setOpenPreview} />
    </DndProvider>
  );
};
export default Block;
