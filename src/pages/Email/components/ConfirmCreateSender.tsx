import { emailApis } from '@/apis/emailApis';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { RiAlertLine, RiInformation2Line, RiLoader2Line } from '@remixicon/react';
import { t } from 'i18next';
import { useContext, useState } from 'react';
import { SenderContext } from '../context/senderProvider';
import { toast } from '@/components/ui/sonner';
import { useNavigate } from 'react-router-dom';
import { EMAIL_ROUTER, ROOT_PATH } from '@/constants/router';

type Props = {
  open: boolean;
  onClose: () => void;
  name: string;
  email: string;
};

const ConfirmCreateSender = ({ onClose, open, name, email }: Props) => {
  const [loading, setLoading] = useState(false);
  const { dispatch } = useContext(SenderContext);
  const navigate = useNavigate();

  const handleAddAndAuthenticate = async () => {};

  const handleAddThisSenderAnyway = async () => {
    setLoading(true);
    try {
      const res = await emailApis.createSender(name, email);
      dispatch({ type: 'CREATE_SENDER', payload: res });
      onClose();
      toast({
        status: 'success',
        title: t('email.addThisSenderSuccess'),
      });
      navigate(`${ROOT_PATH}/${EMAIL_ROUTER.email}/${EMAIL_ROUTER.verifySender}/${res.id}`);
    } catch (error) {
      return error;
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="p-6 max-w-[780px] gap-6">
        <DialogHeader>
          <DialogTitle>{t('email.addThisSender')}</DialogTitle>
        </DialogHeader>
        <div className="flex gap-3 p-3 rounded-xl bg-[#FFFCEB]">
          <RiAlertLine className="flex-shrink-0" size={20} color="#D98206" />
          <div className="text-xs flex flex-col">
            <span>{t('email.addThisSenderNotice.senderEmailAddress')}</span>
            <span> {t('email.addThisSenderNotice.improveEmail')}</span>
          </div>
        </div>
        <div className="flex items-center gap-2 text-sm font-medium [&_svg]:flex-shrink-0">
          <RiInformation2Line size={24} />
          <span>{t('email.addThisSenderTooltip')}</span>
        </div>
        <div className="flex justify-end gap-4">
          <Button onClick={handleAddThisSenderAnyway} className="p-3 rounded-xl" variant={'ghost'}>
            {loading ? (
              <RiLoader2Line className="animate-spin" size={20} />
            ) : (
              t('email.addThisSenderAnyway')
            )}
          </Button>
          <Button onClick={handleAddAndAuthenticate} className="p-3 rounded-xl" variant={'primary'}>
            {t('email.addAndAuthenticate')}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
export default ConfirmCreateSender;
