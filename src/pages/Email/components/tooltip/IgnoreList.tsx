import { RiCircleFill } from '@remixicon/react';
import { t } from 'i18next';

const IgnoreList = () => (
  <div className="flex flex-col text-xs">
    <span>{t('email.tooltip.customSetting.context')}</span>
    <span className="flex items-center gap-1">
      <RiCircleFill className="flex-shrink-0" size={6} />
      {t('email.tooltip.customSetting.address')}
    </span>
    <span className="flex items-center gap-1">
      <RiCircleFill className="flex-shrink-0" size={6} />
      {t('email.tooltip.customSetting.name')}
    </span>
    <span className="flex items-center gap-1">
      <RiCircleFill className="flex-shrink-0" size={6} />
      {t('email.tooltip.customSetting.reply')}
    </span>
    <span>{t('email.tooltip.customSetting.replace')}</span>
  </div>
);
export default IgnoreList;
