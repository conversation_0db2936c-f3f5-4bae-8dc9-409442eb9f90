import { t } from 'i18next';

const AttachmentTooltip = () => {
  return (
    <div>
      <p>{t('email.tooltip.parameter')}</p>
      <Label label="UTM_SOURCE" value="Brevo" />
      <Label label="UTM_MEDIUM" value="EMAIL" />
      <Label label="UTM_CAMPAIGN" value="NAME_OF_YOUR_CAMPAIGN" />
      <p>{t('email.tooltip.term')}</p>
    </div>
  );
};
export default AttachmentTooltip;

const Label = ({ label, value }: { label: string; value: string }) => {
  return (
    <div className="flex items-center gap-1 text-xs">
      <span className="font-semibold">{label}:</span>
      <span>{value}</span>
    </div>
  );
};
