import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { RiVideoLine } from '@remixicon/react';
import { t } from 'i18next';

const Video = () => {
  return (
    <div className="h-[263px] w-full flex-col bg-secondary flex items-center justify-center gap-3 rounded-xl">
      <RiVideoLine size={40} />
      <div className="text-xs flex items-center gap-1">
        <Button className="text-brand text-xs p-0 underline" variant={'ghost'}>
          {t('common.button.upload')}
        </Button>
        <span>{t('email.copyUrl')}</span>
      </div>
      <Input className="bg-card max-w-[342px]" />
    </div>
  );
};
export default Video;
