import { Dialog, DialogContent } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { RiLinkM } from '@remixicon/react';
import { t } from 'i18next';
import { useState } from 'react';
import DataTable from '@/components/table/DataTable';
import { socialIcons, socialIconsCol, TSocialList } from '@/constants/email/socialIcons';
import SearchIcon from '../SearchIcon';
import IconSocial from './IconSocial';
import AddIconSocial from '../BlockDesign/AddIconSocial';

const Social = () => {
  const [openModal, setOpenModal] = useState(false);
  const [socialList, setSocialList] = useState<TSocialList[]>([]);
  const [search, setSearch] = useState('');
  const [selected, setSelected] = useState<TSocialList[]>([]);
  const [opendAddIcon, setOpendAddIcon] = useState(false);

  const handleSelected = (): void => {
    setSelected(socialList);
  };

  const dataSearch = socialIcons().filter((item) =>
    item.name.toLowerCase().includes(search.toLowerCase()),
  );

  return selected.length > 0 ? (
    <IconSocial icons={selected} />
  ) : (
    <div
      style={{
        background: `
            linear-gradient(to right, #d1d5db 50%, transparent 50%) top/20px 1px repeat-x,
            linear-gradient(to right, #d1d5db 50%, transparent 50%) bottom/20px 1px repeat-x,
            linear-gradient(to bottom, #d1d5db 50%, transparent 50%) left/1px 20px repeat-y,
            linear-gradient(to bottom, #d1d5db 50%, transparent 50%) right/1px 20px repeat-y
            `,
        backgroundOrigin: 'padding-box',
        padding: '1px',
      }}
      className="h-[154px] w-full rounded-2xl flex flex-col items-center justify-center gap-3"
    >
      <RiLinkM size={24} />
      <span className="text-xs text-muted-foreground">{t('email.addSocialLink')}</span>

      <Button
        onClick={() => setOpenModal(true)}
        className="rounded-xl"
        size={'lg'}
        variant={'outline'}
      >
        <RiLinkM size={24} />
        {t('email.addSocialLink')}
      </Button>
      <Dialog open={openModal} onOpenChange={setOpenModal}>
        <DialogContent className="max-w-[780px] p-6 rounded-2xl max-h-dvh overflow-x-hidden">
          <div className="text-lg font-medium text-start">{t('email.addSocialMedia')}</div>
          <SearchIcon
            setSearchQuery={setSearch}
            value={search}
            onClick={() => setOpendAddIcon(!opendAddIcon)}
          />
          {opendAddIcon && <AddIconSocial />}
          <div className="flex flex-col gap-4">
            <span className="font-medium">{t('email.socialMedia')}</span>
            <DataTable
              data={dataSearch}
              columns={socialIconsCol()}
              total={socialIcons().length}
              className="max-h-[490px] [&_thead]:hidden"
              getRowsSelected={setSocialList}
            />
          </div>
          <div className="flex items-center justify-end gap-4">
            <Button variant={'textOnly'} className="" onClick={() => setOpenModal(false)}>
              {t('common.button.cancel')}
            </Button>
            <Button variant={'primary'} className="" onClick={handleSelected}>
              {t('email.addToList')}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
export default Social;
