import { Dialog, DialogContent } from '@/components/ui/dialog';
import { t } from 'i18next';
import BlockWrapper from '../BlockElement/BlockWrapper';
import useClassSetting from '@/hooks/useClassSetting';
import Counter from '../BlockDesign/Counter';
import BlockAlignment from '../BlockDesign/BlockAlignment';
import ColorPicker from '../BlockDesign/ColorPicker';
import UnitInput from '../BlockDesign/UnitInput';
import UnitSelect from '../BlockDesign/UnitSelect';
import { borderStyle } from '@/constants/email';
import { useState } from 'react';

const CustomPopup = () => {
  const [openModal, setOpenModal] = useState(false);
  const { configToStyle, updateProperty, classConfig } = useClassSetting({
    width: '100%',
    borderColor: '#20232C',
    borderRadius: '0',
    borderTop: '1',
    borderStyle: 'solid',
    margin: 'auto',
  });

  return (
    <>
      <div onClick={() => setOpenModal(true)} style={{ ...configToStyle, cursor: 'pointer' }} />
      <Dialog open={openModal} onOpenChange={setOpenModal}>
        <DialogContent className="max-w-[780px] bg-secondary p-6 rounded-2xl">
          <div className="text-lg font-medium text-start">{t('email.editDivider')}</div>
          <BlockWrapper title={t('email.dividerSetting')}>
            <div className="grid grid-cols-2 gap-x-6 gap-y-4">
              <UnitInput
                value={classConfig.borderTop?.toString() || ''}
                setValue={(value) => updateProperty('borderTop', value)}
                title={t('email.thickness')}
                units={'px'}
                className="justify-between"
              />
              <UnitInput
                className="justify-between"
                value={classConfig.width?.toString() || ''}
                setValue={(value) => updateProperty('width', value)}
                title={t('email.width')}
                units={['px', '%']}
                defaultUnit="%"
              />
              <UnitSelect
                className="justify-between"
                options={borderStyle}
                selected={classConfig.borderStyle || ''}
                title={t('email.style')}
                setSelected={(value) => updateProperty('borderStyle', value)}
              />
              <ColorPicker
                color={classConfig.borderColor!}
                setColor={(value) => updateProperty('borderColor', value)}
              />
              <BlockAlignment
                value={classConfig.alignment || 'center'}
                onChange={(value) => updateProperty('alignment', value)}
              />
            </div>
          </BlockWrapper>
          <BlockWrapper title={t('email.spacing')}>
            <div className="flex justify-between">
              <div>{t('email.padding')}</div>
              <div className="flex items-center gap-4">
                <Counter
                  title={t('email.top')}
                  value={Number(classConfig.marginTop || 0)}
                  onChange={(value) => updateProperty('marginTop', value.toString())}
                />
                <Counter
                  title={t('email.left')}
                  value={Number(
                    typeof classConfig.marginLeft === 'string'
                      ? classConfig.marginLeft.replace('px', '')
                      : classConfig.marginLeft || 0,
                  )}
                  onChange={(value) => updateProperty('marginLeft', value.toString())}
                />
                <Counter
                  title={t('email.bottom')}
                  value={Number(classConfig.marginBottom || 0)}
                  onChange={(value) => updateProperty('marginBottom', value.toString())}
                />
                <Counter
                  title={t('email.right')}
                  value={Number(classConfig.marginRight || 0)}
                  onChange={(value) => updateProperty('marginRight', value.toString())}
                />
              </div>
            </div>
          </BlockWrapper>
        </DialogContent>
      </Dialog>
    </>
  );
};
export default CustomPopup;
