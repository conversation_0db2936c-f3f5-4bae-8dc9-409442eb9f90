import { useRef, useState } from 'react';
import { CKEditor } from '@ckeditor/ckeditor5-react';
import { DecoupledEditor, EditorConfig } from 'ckeditor5';
import useCkEditor from '@/hooks/useCkEditor';

const TitleBlock = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [contentHtml, setContentHtml] = useState('');
  const [shouldHandleBlur, setShouldHandleBlur] = useState(true);

  const editorContainerRef = useRef<HTMLDivElement>(null);
  const editorMenuBarRef = useRef<HTMLDivElement>(null);
  const editorToolbarRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<HTMLDivElement>(null);

  const { editorConfig } = useCkEditor({ initialData: contentHtml });

  const toggleEditMode = () => {
    if (!isEditing) {
      setIsEditing(true);
    } else {
      setIsEditing(false);
    }
  };

  const handleDisplayClick = () => {
    toggleEditMode();
  };

  // Fix the event type
  const handleEditorBlur = (e: React.FocusEvent<HTMLDivElement>) => {
    if (!shouldHandleBlur) return;

    // Check if relatedTarget exists and is contained within the toolbar
    if (editorToolbarRef.current?.contains(e.relatedTarget as Node)) {
      return;
    }

    if (contentHtml.trim() !== '') {
      toggleEditMode();
    }
  };

  // Fix the event type
  const handleToolbarMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    e.preventDefault();
    setShouldHandleBlur(false);
  };

  const handleToolbarMouseUp = () => {
    setTimeout(() => {
      setShouldHandleBlur(true);
    }, 0);
  };

  return (
    <div className="w-full relative">
      {!isEditing ? (
        <div
          className="w-full min-h-[3rem] rounded-xl p-2 cursor-text hover:bg-gray-50"
          onClick={handleDisplayClick}
        >
          {contentHtml ? (
            <div dangerouslySetInnerHTML={{ __html: contentHtml }} />
          ) : (
            <div className="text-gray-400 italic">Click to add content...</div>
          )}
        </div>
      ) : (
        <div className="w-full group relative" ref={editorContainerRef} onBlur={handleEditorBlur}>
          <div
            className="absolute bg-card -top-10 flex border h-10 flex-row items-center rounded-md w-fit ml-auto shadow-md right-0"
            ref={editorToolbarRef}
            onMouseDown={handleToolbarMouseDown}
            onMouseUp={handleToolbarMouseUp}
          ></div>
          <div className="hidden" ref={editorMenuBarRef}></div>
          <div className="w-full  border rounded-xl border-dashed p-2 text-sm">
            <div
              style={{
                fontSize: '36px',
                height: '60px',
                lineHeight: '60px',
              }}
              ref={editorRef}
            >
              {editorConfig && (
                <CKEditor
                  onReady={(editorInstance) => {
                    if (editorToolbarRef.current && editorInstance.ui.view.toolbar.element) {
                      editorToolbarRef.current.appendChild(editorInstance.ui.view.toolbar.element);
                    }

                    if (editorMenuBarRef.current && editorInstance.ui.view.menuBarView?.element) {
                      editorMenuBarRef.current.appendChild(
                        editorInstance.ui.view.menuBarView.element,
                      );
                    }
                    editorInstance.editing.view.focus();
                    const fontSizes = editorInstance.commands.get('fontSize');
                    fontSizes?.execute({ value: '36px' });
                    const fontWeights = editorInstance.commands.get('bold');
                    fontWeights?.execute({ forceValue: true });
                  }}
                  onAfterDestroy={() => {
                    if (editorToolbarRef.current) {
                      while (editorToolbarRef.current.firstChild) {
                        editorToolbarRef.current.removeChild(editorToolbarRef.current.firstChild);
                      }
                    }

                    if (editorMenuBarRef.current) {
                      while (editorMenuBarRef.current.firstChild) {
                        editorMenuBarRef.current.removeChild(editorMenuBarRef.current.firstChild);
                      }
                    }
                  }}
                  editor={DecoupledEditor}
                  config={editorConfig as EditorConfig}
                  data={contentHtml}
                  onChange={(_, editorInstance) => {
                    const data = editorInstance.getData();
                    setContentHtml(data);
                  }}
                />
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
export default TitleBlock;
