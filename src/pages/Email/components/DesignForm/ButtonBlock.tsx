import { Dialog, DialogContent } from '@/components/ui/dialog';
import { t } from 'i18next';
import { useCallback, useEffect, useState } from 'react';
import BlockWrapper from '../BlockElement/BlockWrapper';
import useClassSetting from '@/hooks/useClassSetting';
import UnitInput from '../BlockDesign/UnitInput';
import ColorPicker from '../BlockDesign/ColorPicker';
import UnitSelect from '../BlockDesign/UnitSelect';
import { fontFamily } from '@/constants/email';
import TextFormat from '../BlockDesign/TextFormat';
import Counter from '../BlockDesign/Counter';
import BlockAlignment from '../BlockDesign/BlockAlignment';
import ContentInput from '../BlockDesign/ContentInput';

const ButtonBlock = () => {
  const [open, setOpen] = useState(false);
  const { configToStyle, updateProperty, classConfig } = useClassSetting({
    fontSize: 14,
    fontFamily: 'Arial',
    color: '#ffffff',
    fontWeight: 'normal',
    fontStyle: 'normal',
    textDecoration: 'none',
    border: '0',
    borderRadius: '12',
    width: '150px',
    paddingBottom: '12',
    paddingTop: '12',
    paddingLeft: '12',
    paddingRight: '12',
    backgroundColor: '#8F5CFF',
  });

  const [contentBtn, setContentBtn] = useState<string>(t('email.callToAction'));

  const [format, setFormat] = useState<string[]>(() => {
    const initialFormats: string[] = [];
    if (classConfig.fontWeight === 'bold') initialFormats.push('bold');
    if (classConfig.fontStyle === 'italic') initialFormats.push('italic');
    if (classConfig.textDecoration === 'underline') initialFormats.push('underline');
    return initialFormats;
  });

  useEffect(() => {
    const newFormats: string[] = [];
    if (classConfig.fontWeight === 'bold') newFormats.push('bold');
    if (classConfig.fontStyle === 'italic') newFormats.push('italic');
    if (classConfig.textDecoration === 'underline') newFormats.push('underline');
    if (JSON.stringify(newFormats) !== JSON.stringify(format)) {
      setFormat(newFormats);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [classConfig.fontWeight, classConfig.fontStyle, classConfig.textDecoration]);

  const handleFormatChange = useCallback(
    (newFormats: string[]) => {
      setFormat(newFormats);
      updateProperty('fontWeight', newFormats.includes('bold') ? 'bold' : 'normal');
      updateProperty('fontStyle', newFormats.includes('italic') ? 'italic' : 'normal');
      updateProperty('textDecoration', newFormats.includes('underline') ? 'underline' : 'none');
    },
    [updateProperty],
  );

  return (
    <>
      <button style={configToStyle} onClick={() => setOpen(true)}>
        {contentBtn}
      </button>
      <Dialog onOpenChange={setOpen} open={open}>
        <DialogContent className="max-w-[780px] bg-secondary p-6 rounded-2xl">
          <div className="text-lg font-medium text-start">{t('email.editButton')}</div>
          <BlockWrapper title={t('email.content')}>
            <div className="grid grid-cols-2 gap-x-6 gap-y-4">
              <UnitInput
                value={classConfig.fontSize?.toString() || ''}
                setValue={(value) => updateProperty('fontSize', value)}
                title={t('email.fontSize')}
                units={'px'}
                className="justify-between"
              />
              <ColorPicker
                title={t('email.textColor')}
                color={classConfig.color!}
                setColor={(value) => updateProperty('color', value)}
              />
              <UnitSelect
                selected={classConfig.fontFamily || ''}
                setSelected={(value) => updateProperty('fontFamily', value)}
                title={t('email.font')}
                className="justify-between"
                options={fontFamily}
              />
              <TextFormat selected={format} onChange={handleFormatChange} />
            </div>
            <ContentInput
              className="[&_span]:w-[94px]"
              title={t('email.content')}
              value={contentBtn}
              onChange={setContentBtn}
            />
          </BlockWrapper>
          <BlockWrapper title={t('email.btnSetting')}>
            <div className="grid grid-cols-2 gap-x-6 gap-y-4">
              <Counter
                className="flex items-center flex-row justify-between"
                title={t('email.border')}
                value={Number(classConfig.border) || 0}
                onChange={(value) => updateProperty('border', value.toString())}
              />
              <ColorPicker
                title={t('email.borderColor')}
                color={classConfig.borderColor!}
                setColor={(value) => updateProperty('borderColor', value)}
              />
              <Counter
                className="flex items-center flex-row justify-between"
                title={t('email.roundedCorner')}
                value={Number(classConfig.borderRadius) || 0}
                onChange={(value) => updateProperty('borderRadius', value.toString())}
              />
              <ColorPicker
                title={t('email.backgroundColor')}
                color={classConfig.backgroundColor!}
                setColor={(value) => updateProperty('backgroundColor', value)}
              />
              <UnitInput
                className="justify-between"
                value={classConfig.width?.toString() || ''}
                setValue={(value) => updateProperty('width', value)}
                title={t('email.width')}
                units={['px', '%']}
                defaultUnit="%"
              />
              <BlockAlignment
                value={classConfig.alignment || 'center'}
                onChange={(value) => updateProperty('alignment', value)}
              />
            </div>
          </BlockWrapper>
          <BlockWrapper title={t('email.spacing')}>
            <div className="flex justify-between">
              <div>{t('email.padding')}</div>
              <div className="flex items-center gap-4">
                <Counter
                  title={t('email.top')}
                  value={Number(classConfig.paddingTop || 0)}
                  onChange={(value) => updateProperty('paddingTop', value.toString())}
                />
                <Counter
                  title={t('email.left')}
                  value={Number(
                    typeof classConfig.paddingLeft === 'string'
                      ? classConfig.paddingLeft.replace('px', '')
                      : classConfig.paddingLeft || 0,
                  )}
                  onChange={(value) => updateProperty('paddingLeft', value.toString())}
                />
                <Counter
                  title={t('email.bottom')}
                  value={Number(classConfig.paddingBottom || 0)}
                  onChange={(value) => updateProperty('paddingBottom', value.toString())}
                />
                <Counter
                  title={t('email.right')}
                  value={Number(classConfig.paddingRight || 0)}
                  onChange={(value) => updateProperty('paddingRight', value.toString())}
                />
              </div>
            </div>
            <div className="flex justify-between">
              <div>{t('email.margin')}</div>
              <div className="flex items-center gap-4">
                <Counter
                  title={t('email.top')}
                  value={Number(classConfig.marginTop || 0)}
                  onChange={(value) => updateProperty('marginTop', value.toString())}
                />
                <Counter
                  title={t('email.left')}
                  value={Number(
                    typeof classConfig.marginLeft === 'string'
                      ? classConfig.marginLeft.replace('px', '')
                      : classConfig.marginLeft || 0,
                  )}
                  onChange={(value) => updateProperty('marginLeft', value.toString())}
                />
                <Counter
                  title={t('email.bottom')}
                  value={Number(classConfig.marginBottom || 0)}
                  onChange={(value) => updateProperty('marginBottom', value.toString())}
                />
                <Counter
                  title={t('email.right')}
                  value={Number(classConfig.marginRight || 0)}
                  onChange={(value) => updateProperty('marginRight', value.toString())}
                />
              </div>
            </div>
          </BlockWrapper>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ButtonBlock;
