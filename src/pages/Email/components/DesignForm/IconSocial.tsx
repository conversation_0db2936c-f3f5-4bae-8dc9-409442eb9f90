import { TSocialList } from '@/constants/email/socialIcons';
import useClassSetting from '@/hooks/useClassSetting';
import { useState } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { t } from 'i18next';
import BlockWrapper from '../BlockElement/BlockWrapper';
import UnitInput from '../BlockDesign/UnitInput';
import UnitSelect from '../BlockDesign/UnitSelect';
import { iconSize, iconStyle, iconTheme } from '@/constants/email';
import Counter from '../BlockDesign/Counter';
import BlockAlignment from '../BlockDesign/BlockAlignment';

type Props = {
  icons: TSocialList[];
};

const IconSocial = ({ icons }: Props) => {
  const [openModal, setOpenModal] = useState(false);
  const { classConfig, configToStyle, updateProperty } = useClassSetting({
    display: 'flex',
    flexDirection: 'row',
    gap: '3',
    iconSize: 32,
    width: '100%',
    alignment: 'center',
  });

  return (
    <>
      <div onClick={() => setOpenModal(true)} style={configToStyle}>
        {icons.map((icon, index) => (
          <icon.icon
            style={{
              height: `${classConfig.iconSize}px`,
              width: `${classConfig.iconSize}px`,
            }}
            key={index}
            color={icon.color}
          />
        ))}
      </div>
      <Dialog open={openModal} onOpenChange={setOpenModal}>
        <DialogContent className="max-w-[780px] bg-secondary p-6 rounded-2xl">
          <div className="text-lg font-medium text-start">{t('email.editSocialIcon')}</div>
          <BlockWrapper title={t('email.iconDesign')}>
            <div className="grid grid-cols-2 gap-x-6 gap-y-4">
              <UnitInput
                className="justify-between"
                value={String(classConfig.gap)}
                setValue={(value) => updateProperty('gap', value)}
                title={t('email.spacing')}
                units={'px'}
              />
              <UnitSelect
                className="justify-between"
                isShowLabel={true}
                options={iconSize}
                selected={classConfig.iconSize?.toString() || ''}
                title={t('email.size')}
                setSelected={(value) => updateProperty('iconSize', Number(value))}
              />
              <UnitSelect
                className="justify-between"
                isShowLabel={true}
                options={iconStyle}
                selected={classConfig.iconSize?.toString() || ''}
                title={t('email.style')}
                setSelected={(value) => updateProperty('iconSize', Number(value))}
              />
              <UnitSelect
                className="justify-between"
                isShowLabel={true}
                options={iconTheme}
                selected={classConfig.iconSize?.toString() || ''}
                title={t('email.theme')}
                setSelected={(value) => updateProperty('iconSize', Number(value))}
              />
            </div>
          </BlockWrapper>
          <BlockWrapper title={t('email.layout')}>
            <div className="grid grid-cols-2 gap-x-6 gap-y-4">
              <UnitInput
                className="justify-between"
                value={classConfig.width?.toString() || ''}
                setValue={(value) => updateProperty('width', value)}
                title={t('email.width')}
                units={['px', '%']}
                defaultUnit="%"
              />
              <BlockAlignment
                value={classConfig.alignment || 'center'}
                onChange={(value) => updateProperty('alignment', value)}
              />
            </div>
          </BlockWrapper>
          <BlockWrapper title={t('email.spacing')}>
            <div className="flex justify-between">
              <div>{t('email.padding')}</div>
              <div className="flex items-center gap-4">
                <Counter
                  title={t('email.top')}
                  value={Number(classConfig.paddingTop || 0)}
                  onChange={(value) => updateProperty('paddingTop', value.toString())}
                />
                <Counter
                  title={t('email.left')}
                  value={Number(
                    typeof classConfig.paddingLeft === 'string'
                      ? classConfig.paddingLeft.replace('px', '')
                      : classConfig.paddingLeft || 0,
                  )}
                  onChange={(value) => updateProperty('paddingLeft', value.toString())}
                />
                <Counter
                  title={t('email.bottom')}
                  value={Number(classConfig.paddingBottom || 0)}
                  onChange={(value) => updateProperty('paddingBottom', value.toString())}
                />
                <Counter
                  title={t('email.right')}
                  value={Number(classConfig.paddingRight || 0)}
                  onChange={(value) => updateProperty('paddingRight', value.toString())}
                />
              </div>
            </div>
            <div className="flex justify-between">
              <div>{t('email.margin')}</div>
              <div className="flex items-center gap-4">
                <Counter
                  title={t('email.top')}
                  value={Number(classConfig.marginTop || 0)}
                  onChange={(value) => updateProperty('marginTop', value.toString())}
                />
                <Counter
                  title={t('email.left')}
                  value={Number(
                    typeof classConfig.marginLeft === 'string'
                      ? classConfig.marginLeft.replace('px', '')
                      : classConfig.marginLeft || 0,
                  )}
                  onChange={(value) => updateProperty('marginLeft', value.toString())}
                />
                <Counter
                  title={t('email.bottom')}
                  value={Number(classConfig.marginBottom || 0)}
                  onChange={(value) => updateProperty('marginBottom', value.toString())}
                />
                <Counter
                  title={t('email.right')}
                  value={Number(classConfig.marginRight || 0)}
                  onChange={(value) => updateProperty('marginRight', value.toString())}
                />
              </div>
            </div>
          </BlockWrapper>
        </DialogContent>
      </Dialog>
    </>
  );
};
export default IconSocial;
