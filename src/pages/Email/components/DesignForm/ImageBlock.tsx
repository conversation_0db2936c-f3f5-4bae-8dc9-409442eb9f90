import { useState } from 'react';
import { RiImageLine } from '@remixicon/react';

const ImageBlock = () => {
  const [image, setImage] = useState<string | null>(null);

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    handleFile(file);
  };

  const handleFile = (file: File) => {
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => setImage(e.target?.result as string);
      reader.readAsDataURL(file);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  };

  const handleClick = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = (event: Event) => {
      const target = event.target as HTMLInputElement;
      if (target.files && target.files[0]) {
        handleFile(target.files[0]);
      }
    };
    input.click();
  };

  return (
    <div
      style={{
        backgroundColor: '#F0F0F0',
        minHeight: '263px',
        width: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        justifyItems: 'center',
        cursor: 'pointer',
      }}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onClick={handleClick}
    >
      {image ? (
        <img
          style={{
            height: 'auto',
            width: '100%',
            objectFit: 'contain',
          }}
          src={image}
          alt="Uploaded"
        />
      ) : (
        <RiImageLine size={40} />
      )}
    </div>
  );
};

export default ImageBlock;
