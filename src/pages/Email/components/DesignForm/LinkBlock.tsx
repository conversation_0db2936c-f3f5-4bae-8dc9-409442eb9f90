import { useCallback, useEffect, useState } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { t } from 'i18next';
import BlockWrapper from '../BlockElement/BlockWrapper';
import useClassSetting from '@/hooks/useClassSetting';
import UnitInput from '../BlockDesign/UnitInput';
import ColorPicker from '../BlockDesign/ColorPicker';
import UnitSelect from '../BlockDesign/UnitSelect';
import TextFormat from '../BlockDesign/TextFormat';
import ContentInput from '../BlockDesign/ContentInput';
import { fontFamily } from '@/constants/email';
import Counter from '../BlockDesign/Counter';
import BlockAlignment from '../BlockDesign/BlockAlignment';

const LinkBlock = () => {
  const [openModal, setOpenModal] = useState(false);
  const [contentBtn, setContentBtn] = useState<{
    content: string;
    url: string;
  }>({ content: t('email.callToAction'), url: '' });

  const { classConfig, configToStyle, updateProperty } = useClassSetting({
    fontSize: 14,
    fontFamily: 'Arial',
    color: '#000000',
    fontWeight: 'normal',
    fontStyle: 'normal',
    textDecoration: 'none',
    border: '0',
    borderRadius: '0',
    width: '100%',
    alignment: 'center',
    borderColor: '#000000',
    backgroundColor: '#ffffff',
  });
  const [format, setFormat] = useState<string[]>(() => {
    const initialFormats: string[] = [];
    if (classConfig.fontWeight === 'bold') initialFormats.push('bold');
    if (classConfig.fontStyle === 'italic') initialFormats.push('italic');
    if (classConfig.textDecoration === 'underline') initialFormats.push('underline');
    return initialFormats;
  });

  useEffect(() => {
    const newFormats: string[] = [];
    if (classConfig.fontWeight === 'bold') newFormats.push('bold');
    if (classConfig.fontStyle === 'italic') newFormats.push('italic');
    if (classConfig.textDecoration === 'underline') newFormats.push('underline');
    if (JSON.stringify(newFormats) !== JSON.stringify(format)) {
      setFormat(newFormats);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [classConfig.fontWeight, classConfig.fontStyle, classConfig.textDecoration]);

  const handleFormatChange = useCallback(
    (newFormats: string[]) => {
      setFormat(newFormats);
      updateProperty('fontWeight', newFormats.includes('bold') ? 'bold' : 'normal');
      updateProperty('fontStyle', newFormats.includes('italic') ? 'italic' : 'normal');
      updateProperty('textDecoration', newFormats.includes('underline') ? 'underline' : 'none');
    },
    [updateProperty],
  );
  return (
    <>
      <a
        style={{
          ...configToStyle,
          textDecoration: contentBtn.url ? 'underline' : 'none',
          cursor: 'pointer',
        }}
        onClick={() => setOpenModal(true)}
      >
        {contentBtn.content}
      </a>
      <Dialog open={openModal} onOpenChange={setOpenModal}>
        <DialogContent className="max-w-[780px] bg-secondary p-6 rounded-2xl max-h-dvh overflow-x-hidden">
          <div className="text-lg font-medium text-start">{t('email.editNavigation')}</div>
          <BlockWrapper title={t('email.content')}>
            <div className="grid grid-cols-2 gap-x-6 gap-y-4">
              <UnitInput
                value={classConfig.fontSize?.toString() || ''}
                className="justify-between"
                setValue={(value) => updateProperty('fontSize', value)}
                title={t('email.fontSize')}
              />
              <ColorPicker
                color={classConfig.color!}
                setColor={(value) => updateProperty('color', value)}
                title={t('email.textColor')}
              />
              <UnitSelect
                className="justify-between"
                options={fontFamily}
                selected={classConfig.fontFamily || ''}
                title={t('email.font')}
                setSelected={(value) => updateProperty('fontFamily', value)}
              />
              <TextFormat selected={format} onChange={handleFormatChange} />
            </div>
            <ContentInput
              className="gap-16"
              title={t('email.content')}
              value={contentBtn.content}
              onChange={(value) => setContentBtn({ ...contentBtn, content: value })}
              placeholder={t('email.content')}
            />
            <ContentInput
              className="gap-[88px]"
              title={t('email.url')}
              value={contentBtn.url}
              onChange={(value) => setContentBtn({ ...contentBtn, url: value })}
              placeholder="https://"
              isEmoji={false}
            />
          </BlockWrapper>
          <BlockWrapper title={t('email.navigationSetting')}>
            <div className="grid grid-cols-2 gap-x-6 gap-y-4">
              <Counter
                title={t('email.border')}
                value={Number(classConfig.border || 0)}
                onChange={(value) => updateProperty('border', value.toString())}
                className="flex-row items-center [&_span]:text-base justify-between"
              />
              <ColorPicker
                color={classConfig.borderColor!}
                setColor={(value) => updateProperty('borderColor', value)}
                title={t('email.borderColor')}
              />
              <Counter
                className="flex-row items-center [&_span]:text-base justify-between"
                title={t('email.roundedCorner')}
                value={Number(classConfig.borderRadius) || 0}
                onChange={(value) => updateProperty('borderRadius', value.toString())}
              />
              <ColorPicker
                color={classConfig.backgroundColor!}
                setColor={(value) => updateProperty('backgroundColor', value)}
                title={t('email.backgroundColor')}
              />
              <UnitInput
                className="justify-between"
                value={classConfig.width?.toString() || ''}
                setValue={(value) => updateProperty('width', value)}
                title={t('email.width')}
                units={['px', '%']}
                defaultUnit="%"
              />
              <BlockAlignment
                value={classConfig.alignment || 'center'}
                onChange={(value) => updateProperty('alignment', value)}
              />
            </div>
          </BlockWrapper>
          <BlockWrapper title={t('email.spacing')}>
            <div className="flex justify-between">
              <div>{t('email.padding')}</div>
              <div className="flex items-center gap-4">
                <Counter
                  title={t('email.top')}
                  value={Number(classConfig.paddingTop || 0)}
                  onChange={(value) => updateProperty('paddingTop', value.toString())}
                />
                <Counter
                  title={t('email.left')}
                  value={Number(
                    typeof classConfig.paddingLeft === 'string'
                      ? classConfig.paddingLeft.replace('px', '')
                      : classConfig.paddingLeft || 0,
                  )}
                  onChange={(value) => updateProperty('paddingLeft', value.toString())}
                />
                <Counter
                  title={t('email.bottom')}
                  value={Number(classConfig.paddingBottom || 0)}
                  onChange={(value) => updateProperty('paddingBottom', value.toString())}
                />
                <Counter
                  title={t('email.right')}
                  value={Number(classConfig.paddingRight || 0)}
                  onChange={(value) => updateProperty('paddingRight', value.toString())}
                />
              </div>
            </div>
            <div className="flex justify-between">
              <div>{t('email.margin')}</div>
              <div className="flex items-center gap-4">
                <Counter
                  title={t('email.top')}
                  value={Number(classConfig.marginTop || 0)}
                  onChange={(value) => updateProperty('marginTop', value.toString())}
                />
                <Counter
                  title={t('email.left')}
                  value={Number(
                    typeof classConfig.marginLeft === 'string'
                      ? classConfig.marginLeft.replace('px', '')
                      : classConfig.marginLeft || 0,
                  )}
                  onChange={(value) => updateProperty('marginLeft', value.toString())}
                />
                <Counter
                  title={t('email.bottom')}
                  value={Number(classConfig.marginBottom || 0)}
                  onChange={(value) => updateProperty('marginBottom', value.toString())}
                />
                <Counter
                  title={t('email.right')}
                  value={Number(classConfig.marginRight || 0)}
                  onChange={(value) => updateProperty('marginRight', value.toString())}
                />
              </div>
            </div>
          </BlockWrapper>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default LinkBlock;
