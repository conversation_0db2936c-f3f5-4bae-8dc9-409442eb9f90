import { emailApis } from '@/apis/emailApis';
import { TEmailInfo } from '@/types/email';
import { createContext, useEffect, useReducer } from 'react';

export type TInitState = {
  list: TEmailInfo[];
};

const initialState: TInitState = {
  list: [],
};

type Action =
  | { type: 'GET_LIST'; payload: TEmailInfo[] }
  | { type: 'CREATE_SENDER'; payload: TEmailInfo }
  | { type: 'UPDATE_SENDER'; payload: TEmailInfo }
  | { type: 'DELETE'; payload: { id: string } };

export const emailReducer = (state: TInitState = initialState, action: Action): TInitState => {
  switch (action.type) {
    case 'GET_LIST':
      return { ...state, list: action.payload };
    case 'CREATE_SENDER':
      return { ...state, list: [...state.list, action.payload] };
    case 'UPDATE_SENDER':
      return {
        ...state,
        list: state.list.map((item) =>
          item.id === action.payload.id ? { ...item, ...action.payload } : item,
        ),
      };
    case 'DELETE':
      return { ...state, list: state.list.filter((item) => item.id !== action.payload.id) };
    default:
      return state;
  }
};

export const SenderContext = createContext<{
  state: TInitState;
  dispatch: React.Dispatch<Action>;
}>({ state: initialState, dispatch: () => null });

const SenderProvider = ({ children }: { children: React.ReactNode }) => {
  const [state, dispatch] = useReducer(emailReducer, initialState);

  useEffect(() => {
    (async () => {
      try {
        const res = await emailApis.getOtpSender();
        dispatch({ type: 'GET_LIST', payload: res.data });
      } catch (error) {
        return error;
      }
    })();
  }, []);

  return <SenderContext.Provider value={{ state, dispatch }}>{children}</SenderContext.Provider>;
};

export default SenderProvider;
