import { TFilterEmail } from '@/types/email';
import FilterCampaign from '../components/FilterCampaign';
import { useState } from 'react';
import { t } from 'i18next';
import PageTitle from '../components/PageTitle';
import SenderFeature from '../components/SenderFeature';

const GetList = () => {
  const [filterPayload, setFilterPayload] = useState<Partial<TFilterEmail>>({
    page: 1,
    limit: 10,
  });

  return (
    <div className="flex flex-col gap-6">
      <PageTitle title={t('email.title')} subTitle={t('email.subTitle')} />
      <SenderFeature />
      <FilterCampaign setFilterPayload={setFilterPayload} filterPayload={filterPayload} />
    </div>
  );
};

export default GetList;
