import { Form } from '@/components/ui/form';
import { t } from 'i18next';
import { SubmitHandler, useForm } from 'react-hook-form';
import InputField from '../components/form/InputField';
import { createSenderSchema, TCreateSender } from '@/constants/email/validate';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import NoteTitle from '../components/NoteTitle';
import ConfirmCreateSender from '../components/ConfirmCreateSender';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { EMAIL_ROUTER, ROOT_PATH } from '@/constants/router';
import PageTitle from '../components/PageTitle';

const CreateSender = () => {
  const navigate = useNavigate();
  const form = useForm<TCreateSender>({
    resolver: zodResolver(createSenderSchema),
  });
  const [openModal, setOpenModal] = useState(false);

  const toggleModal = () => setOpenModal(!openModal);

  const onSubmit: SubmitHandler<TCreateSender> = async () => {
    toggleModal();
  };

  const handleCancel = () => {
    navigate(`${ROOT_PATH}/${EMAIL_ROUTER.email}`);
  };

  return (
    <div className="flex flex-col gap-6">
      <PageTitle title={t('email.title')} subTitle={t('email.subTitle')} />
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="p-10 flex items-start justify-between">
            <div className="flex flex-col max-w-[504px] w-full">
              <div className="text-start text-lg font-medium">{t('email.addSender')}</div>
              <div className="text-start text-sm text-secondary">
                {t('email.subTittleAddSender')}
              </div>
              <InputField
                className="mt-6"
                title={t('email.fromName')}
                name={'name'}
                control={form.control}
              />
              <NoteTitle className="mb-4 mt-2" title={t('email.nameNote')} />
              <InputField title={t('email.fromEmail')} name={'email'} control={form.control} />
              <NoteTitle className="mt-2" title={t('email.emailNote')} />
            </div>
            <div className="max-w-[408px] w-full border rounded-2xl overflow-hidden shadow-sm">
              <div className="bg-secondary p-4 flex items-center gap-2">
                <div className="h-8 w-8 rounded-full bg-[#D9D9D9]" />
                <div className="flex flex-col text-sm text-left">
                  <span className="font-medium">{form.watch('name')}</span>
                  <span className="text-secondary">{form.watch('email')}</span>
                </div>
              </div>
              <div className="px-4 py-[47px] flex flex-col gap-6">
                {[69.91, 270.18, 221.17, 298.28].map((item) => (
                  <div
                    className="h-4 bg-secondary animate-pulse rounded-lg"
                    key={item}
                    style={{ width: `${item}px` }}
                  />
                ))}
              </div>
            </div>
          </div>
          <div className="flex items-center justify-between mt-6 px-10">
            <Button
              onClick={handleCancel}
              type="button"
              size={'lg'}
              className="p-3 rounded-xl"
              variant={'textOnly'}
            >
              {t('common.button.cancel')}
            </Button>
            <Button size={'lg'} className="p-3 rounded-xl" variant={'primary'}>
              {t('email.addSender')}
            </Button>
          </div>
          <ConfirmCreateSender
            name={form.getValues('name')}
            email={form.getValues('email')}
            open={openModal}
            onClose={toggleModal}
          />
        </form>
      </Form>
    </div>
  );
};
export default CreateSender;
