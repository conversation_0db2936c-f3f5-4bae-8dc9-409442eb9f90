import { t } from 'i18next';
import AccordionWrapper from '../components/AccordionWrapper';
import FooterModel from '../components/FooterModel';
import { Form } from '@/components/ui/form';
import { SubmitHandler, useForm } from 'react-hook-form';
import { emailCampaignSchema, TEmailCampaign } from '@/constants/email/validate';
import { zodResolver } from '@hookform/resolvers/zod';
import InputField from '../components/form/InputField';
import { CAMPAIGN_KEY } from '@/constants/email';
import NoticeLabel from '../components/NoticeLabel';
import { Button } from '@/components/ui/button';
import DropdownField from '../components/form/DropdownField';
import DesignContent from '../components/DesignContent';
import TextareaField from '../components/form/TextareaField';
import CheckboxField from '../components/form/CheckboxField';
import styled from 'styled-components';
import PersonalTooltip from '../components/tooltip/PersonalTooltip';
import SendingTracking from '../components/SendingTracking';
import Personalization from '../components/Personalization';
import DesignForm from '../components/DesignForm';
import PreviewForm from '../components/PreviewForm';
import { useContext } from 'react';
import { SenderContext } from '../context/senderProvider';
import { useSelector } from 'react-redux';
import { getSelector } from '@/hooks/reduxHooks';
import PageTitle from '../components/PageTitle';
import SenderFeature from '../components/SenderFeature';

const Campaign = () => {
  const { state } = useContext(SenderContext);
  const { segments } = useSelector(getSelector('segment'));
  const form = useForm<TEmailCampaign>({
    resolver: zodResolver(emailCampaignSchema),
  });

  const onSubmit: SubmitHandler<TEmailCampaign> = async () => {};

  const emailOptions = state.list.map((sender) => ({
    label: sender.email,
    value: sender.email,
  }));

  const segmentOptions = segments.items.map((segment) => ({
    label: segment.name,
    value: segment.name,
  }));

  return (
    <div className="flex flex-col gap-6">
      <PageTitle title={t('email.title')} subTitle={t('email.subTitle')} />
      <SenderFeature />
      <div className="grid grid-cols-4 gap-6">
        <div className="flex items-center col-span-3 flex-col gap-6">
          <div className="text-start w-full font-medium">{t('email.createEmailCampaign')}</div>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="flex items-center flex-col w-full gap-6"
            >
              <InputField
                title={t('email.campaignName')}
                control={form.control}
                name={CAMPAIGN_KEY.CAMPAIGN_NAME}
                placeholder={t('email.placeholder.campaignName')}
              />
              <AccordionWrapper
                title={t('email.sender')}
                subTitle={t('email.desSender')}
                element={
                  <Box>
                    <DropdownField<TEmailCampaign>
                      title={t('email.emailAddress')}
                      control={form.control}
                      name={'sender.email'}
                      placeholder={t('email.placeholder.emailAddress')}
                      option={emailOptions}
                    />
                    <InputField<TEmailCampaign>
                      title={t('email.name')}
                      control={form.control}
                      name={'sender.name'}
                      placeholder={t('email.placeholder.name')}
                    />
                    <NoticeLabel content={t('email.senderNotice')} />
                  </Box>
                }
              />
              <AccordionWrapper
                title={t('email.recipients')}
                subTitle={t('email.desRecipients')}
                element={
                  <Box>
                    <DropdownField<TEmailCampaign>
                      title={t('email.sendTo')}
                      control={form.control}
                      name={'recipients.segmentIds'}
                      option={segmentOptions}
                      placeholder={t('email.placeholder.sendTo')}
                    />
                    <CheckboxField<TEmailCampaign>
                      control={form.control}
                      name={'recipients.dont_send'}
                      title={t('email.dontSend')}
                      tooltipContent={t('email.unengagedTooltip')}
                    />
                    <NoticeLabel
                      content={t('email.senderNotice')}
                      rightContent={
                        <Button
                          className="ml-auto text-brand p-0 h-8"
                          type="button"
                          variant={'ghost'}
                        >
                          {t('email.upgradePlan')}
                        </Button>
                      }
                    />
                  </Box>
                }
              />
              <DesignContent />
              <AccordionWrapper
                title={t('email.subject')}
                subTitle={t('email.desSubject')}
                element={
                  <Box>
                    <TextareaField
                      tooltipContent={t('email.subjectLine.tooltip')}
                      title={t('email.subjectLine.title')}
                      control={form.control}
                      name={'subject'}
                      placeholder={t('email.placeholder.subjectLine')}
                    />
                    <TextareaField
                      placeholder={t('email.placeholder.previewText')}
                      tooltipContent={t('email.previewText.tooltip')}
                      title={t('email.previewText.title')}
                      control={form.control}
                      name={'preview_text'}
                    />
                  </Box>
                }
              />
              <AccordionWrapper
                title={t('email.additionalSettings')}
                element={
                  <Box>
                    <Label>{t('email.personalization.title')}</Label>
                    <CheckboxField<TEmailCampaign>
                      control={form.control}
                      title={t('email.personalization.value')}
                      name={'to_field.isSelect'}
                      tooltipContent={<PersonalTooltip />}
                    />
                    <Label>{t('email.sendTracking.title')}</Label>
                    <SendingTracking control={form.control} />
                    <Label>{t('email.personalization.title')}</Label>
                    <Personalization control={form.control} />
                    <Label>{t('email.design.title')}</Label>
                    <DesignForm control={form.control} />
                  </Box>
                }
              />
            </form>
          </Form>
          <FooterModel />
        </div>
        <PreviewForm control={form.control} />
      </div>
    </div>
  );
};
export default Campaign;

const Box = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;
const Label = styled.div`
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  text-align: left;
`;
