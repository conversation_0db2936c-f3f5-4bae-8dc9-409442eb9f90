import { Outlet } from 'react-router-dom';
import TabsSelect from '../components/MultipleConfig/components/TabsSelect';
import { t } from 'i18next';
import PageTitle from '../components/PageTitle';

const MultipleConfig = () => {
  return (
    <div className="flex flex-col gap-6">
      <PageTitle title={t('email.multipleConfig')} subTitle={t('email.subTitleMultipleConfig')} />
      <TabsSelect />
      <Outlet />
    </div>
  );
};
export default MultipleConfig;
