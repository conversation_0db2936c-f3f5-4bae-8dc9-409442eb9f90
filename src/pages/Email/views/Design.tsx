import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { t } from 'i18next';

import Block from '../components/TabsDesign/Block';
import Section from '../components/TabsDesign/Section';
import YourTemplate from '../components/TabsDesign/YourTemplate';
import PageTitle from '../components/PageTitle';
import SenderFeature from '../components/SenderFeature';

export const TABS_DESIGN = [
  { tab: t('email.block'), subTab: '', component: <Block />, value: 'block' },
  {
    tab: t('email.section'),
    subTab: t('email.comingSoon'),
    component: <Section />,
    value: 'section',
  },
  { tab: t('email.yourTemplate'), subTab: '', component: <YourTemplate />, value: 'template' },
];

const Design = () => {
  return (
    <div className="flex flex-col gap-6">
      <PageTitle title={t('email.title')} subTitle={t('email.subTitle')} />
      <SenderFeature />
      <Tabs defaultValue="block" className="border rounded-2xl flex flex-col">
        <TabsList className="w-full items-start flex gap-6 px-6 pb-0 border-b rounded-none">
          {TABS_DESIGN.map((item) => (
            <TabsTrigger value={item.value} key={item.tab}>
              {item.tab}
              {item.subTab && (
                <div className="bg-secondary px-1 py-0.5 rounded-md ml-2 text-xs">
                  {item.subTab}
                </div>
              )}
            </TabsTrigger>
          ))}
        </TabsList>
        {TABS_DESIGN.map((item) => (
          <TabsContent value={item.value} key={item.tab}>
            {item.component}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};
export default Design;
