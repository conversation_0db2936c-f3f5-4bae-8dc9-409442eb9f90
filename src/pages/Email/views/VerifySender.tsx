import InputOtp from '@/components/InputOtp';
import { Trans, useTranslation } from 'react-i18next';
import { useContext, useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useNavigate, useParams } from 'react-router-dom';
import { EMAIL_ROUTER, ROOT_PATH } from '@/constants/router';
import { emailApis } from '@/apis/emailApis';
import { TEmailInfo } from '@/types/email';
import { SenderContext } from '../context/senderProvider';
import PageTitle from '../components/PageTitle';

const VerifySender = () => {
  const { id } = useParams<{ id: string }>();
  const { dispatch } = useContext(SenderContext);
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [otpCode, setOtpCode] = useState('');
  const [countDown, setCountDown] = useState(60);
  const [sender, setSender] = useState<TEmailInfo>();

  useEffect(() => {
    if (!id) return;
    (async () => {
      try {
        const res = await emailApis.getSenderDetail(id);
        setSender(res.data);
      } catch (error) {
        return error;
      }
    })();
  }, [id]);

  useEffect(() => {
    if (countDown === 0) return;
    const interval = setInterval(() => {
      setCountDown((prev) => prev - 1);
    }, 1000);

    return () => clearInterval(interval);
  }, [countDown]);

  const handleDoThisLater = () => {
    navigate(`${ROOT_PATH}/${EMAIL_ROUTER.email}`);
  };

  const handleVerifySender = async () => {
    try {
      const res = await emailApis.verifySender(sender?.id || '', otpCode);
      dispatch({ type: 'UPDATE_SENDER', payload: res.data });
      navigate(`${ROOT_PATH}/${EMAIL_ROUTER.email}/${EMAIL_ROUTER.multiple_config}`);
    } catch (error) {
      return error;
    }
  };

  return (
    <div className="flex flex-col gap-6">
      <PageTitle title={t('email.title')} subTitle={t('email.subTitle')} />
      <div className="p-10 flex flex-col gap-6 items-start">
        <div className="text-lg font-medium">{t('email.addSender')}</div>
        <Trans
          i18nKey="email.addSenderDes" // optional -> fallbacks to defaults if not provided
          defaults="Before you can use {{name}} as a sender, we must verify its email address.We have sent you a verification code by email at {{email}}"
          values={{ name: 'world' }}
          components={{ italic: <i />, bold: <strong /> }}
        />
        <InputOtp title={t('email.enterOtp')} length={6} value={otpCode} setValue={setOtpCode} />
        <div className="flex items-center gap-4">
          <span className="text-sm text-secondary">{t('email.receiveCode')}</span>
          <Button
            disabled={countDown !== 0}
            size={'lg'}
            variant={countDown === 0 ? 'textOnly' : 'outline'}
            className={cn(
              'p-3 rounded-xl',
              countDown === 0 && 'text-brand hover:text-brand-secondary',
            )}
          >
            {t('email.resendCode', {
              time: countDown === 0 ? '' : `(${countDown}s)`,
            })}
          </Button>
        </div>
        <div className="flex items-center gap-4">
          <Button onClick={handleDoThisLater} className="p-3 rounded-xl" variant={'outline'}>
            {t('email.doThisLater')}
          </Button>
          <Button
            onClick={handleVerifySender}
            disabled={otpCode.length < 6}
            className="p-3 rounded-xl"
            variant={'primary'}
          >
            {t('email.verifySender')}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default VerifySender;
