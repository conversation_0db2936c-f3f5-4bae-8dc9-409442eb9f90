import { emailApis } from '@/apis/emailApis';
import { createSenderSchema, TCreateSender } from '@/constants/email/validate';
import { TEmailInfo } from '@/types/email';
import { zodResolver } from '@hookform/resolvers/zod';
import { useContext, useEffect, useState } from 'react';
import { SubmitHandler, useForm, FormProvider } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router-dom';
import InputField from '../components/form/InputField';
import { t } from 'i18next';
import NoteTitle from '../components/NoteTitle';
import { Button } from '@/components/ui/button';
import { EMAIL_ROUTER, EMAIL_ROUTER_CONFIG, ROOT_PATH } from '@/constants/router';
import PreviewSender from '../components/PreviewSender';
import { SenderContext } from '../context/senderProvider';
import { RiLoader2Line } from 'react-icons/ri';
import { toast } from '@/components/ui/sonner';
import PageTitle from '../components/PageTitle';

const UpdateSender = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { dispatch } = useContext(SenderContext);
  const [loading, setLoading] = useState(false);
  const [sender, setSender] = useState<TEmailInfo>();
  const formMethods = useForm<TCreateSender>({
    resolver: zodResolver(createSenderSchema),
    defaultValues: {
      email: '',
      name: '',
    },
  });

  const { control, watch, setValue, handleSubmit } = formMethods;

  const handleCancel = () => {
    navigate(`${ROOT_PATH}/${EMAIL_ROUTER.email}`);
  };

  // Fetch sender details and update form
  useEffect(() => {
    if (!id) return;

    const fetchSender = async () => {
      try {
        const res = await emailApis.getSenderDetail(id);
        setSender(res.data);

        // Set form values when data is received
        setValue('name', res.data.name || '');
        setValue('email', res.data.email || '');
      } catch (error) {
        console.error('Error fetching sender details:', error);
      }
    };

    fetchSender();
  }, [id, setValue]);

  useEffect(() => {
    formMethods.setValue('name', sender?.name || '');
    formMethods.setValue('email', sender?.email || '');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sender]);

  const onSubmit: SubmitHandler<TCreateSender> = async (data) => {
    try {
      setLoading(true);
      const res = await emailApis.updateSender(sender?.id || '', {
        name: data.name,
      });
      toast({
        status: 'success',
        title: t('email.updateSenderSuccess'),
      });
      dispatch({ type: 'UPDATE_SENDER', payload: res.data });
      navigate(
        `${ROOT_PATH}/${EMAIL_ROUTER.email}/${EMAIL_ROUTER.multiple_config}/${EMAIL_ROUTER_CONFIG.SENDER}`,
      );
    } catch (error) {
      return error;
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col gap-6">
      <PageTitle title={t('email.title')} subTitle={t('email.subTitle')} />
      <FormProvider {...formMethods}>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="p-10 flex items-start justify-between">
            <div className="flex flex-col max-w-[504px] w-full">
              <div className="text-start text-lg font-medium">{t('email.updateSender')}</div>
              <div className="text-start text-sm text-secondary">
                {t('email.subTittleAddSender')}
              </div>

              <InputField
                className="mt-6"
                title={t('email.fromName')}
                name="name"
                control={control}
              />
              <NoteTitle className="mb-4 mt-2" title={t('email.nameNote')} />

              <InputField title={t('email.fromEmail')} name="email" control={control} />
              <NoteTitle className="mt-2" title={t('email.emailNote')} />
            </div>

            <PreviewSender name={watch('name')} email={watch('email')} />
          </div>
          <div className="flex items-center justify-between mt-6 px-10">
            <Button
              onClick={handleCancel}
              type="button"
              size="lg"
              className="p-3 rounded-xl"
              variant="textOnly"
            >
              {t('common.button.cancel')}
            </Button>
            <Button type="submit" size="lg" className="p-3 rounded-xl" variant="primary">
              {loading ? (
                <RiLoader2Line className="flex-shrink-0 animate-spin" size={20} />
              ) : (
                t('email.updateSender')
              )}
            </Button>
          </div>
        </form>
      </FormProvider>
    </div>
  );
};

export default UpdateSender;
