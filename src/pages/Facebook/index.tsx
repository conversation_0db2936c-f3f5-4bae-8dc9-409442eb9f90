import Header from './components/Header';
import { useFBContext } from './context/FbAuthContext';
import EmptyDataView from './views/EmptyDataView';
import Container from './components/Container';
import ConnectFacebookPopup from './components/ConnectFacebokPopup';
import Breadcrumb from '@/components/Breadcrumb';

const FacebookPage = () => {
  const { loading, isAccountSelected } = useFBContext();

  if (loading) {
    return <ConnectFacebookPopup />;
  }

  return (
    <div className="w-full h-full flex flex-col">
      <Breadcrumb />
      {!isAccountSelected ? (
        <>
          <Header />
          <EmptyDataView />
        </>
      ) : (
        <Container />
      )}
    </div>
  );
};

export default FacebookPage;
