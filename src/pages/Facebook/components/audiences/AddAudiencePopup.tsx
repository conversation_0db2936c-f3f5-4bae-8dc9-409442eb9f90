import Modal from '@/components/Modal';
import { Button } from '@/components/ui/button';
import { RiAddCircleLine } from '@remixicon/react';
import { t } from 'i18next';
import { useState } from 'react';
import AddAudienceView from './AddAudienceView';
import { TFilterAudience } from '@/types/facebook';

interface IAddAudiencePopup {
  filterPayload?: TFilterAudience;
  ad_Account_id?: string;
}

const AddAudiencePopup = ({ ...props }: IAddAudiencePopup) => {
  const { filterPayload, ad_Account_id } = props;
  const [open, setOpen] = useState<boolean>(false);
  const toggleOpen = () => setOpen(!open);
  return (
    <Modal
      openModal={open}
      onOpenChange={setOpen}
      isCloseIcon={false}
      className="max-w-[650px]"
      trigger={
        <Button className="bg-[#14161D] hover:bg-[#14161D]/90 px-2 py-1 rounded-xl" size={'lg'}>
          <RiAddCircleLine />
          {t('common.button.addCustomAudience')}
        </Button>
      }
    >
      <AddAudienceView toggleOpen={toggleOpen} ad_Account_id={ad_Account_id} filterPayload={filterPayload} />
    </Modal>
  );
};
export default AddAudiencePopup;
