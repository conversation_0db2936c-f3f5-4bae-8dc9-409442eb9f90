import { fb<PERSON>udi<PERSON><PERSON><PERSON> } from '@/apis/fbAudience';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import { useSegmentContext } from '@/pages/context/SegmentContext';
import { RiInformation2Line, RiLoader2Line } from '@remixicon/react';
import { useMutation } from '@tanstack/react-query';
import { t } from 'i18next';
import { useContext, useState } from 'react';
import { AudienceContext } from '../../context/AudienceContext';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { TFilterAudience } from '@/types/facebook';

type Props = {
  toggleOpen: () => void;
  filterPayload?: TFilterAudience;
  ad_Account_id?: string;
};

const AddAudienceView = ({ toggleOpen, filterPayload, ad_Account_id }: Props) => {
  const { items } = useSegmentContext();
  const { handleGetAudience } = useContext(AudienceContext);
  const [payload, setPayload] = useState<{
    audience_name: string;
    segment_id: string;
  }>({ audience_name: '', segment_id: '' });

  const newOptions = items.reduce<{ label: string; value: string }[]>((acc, item) => {
    if (item.contact_quantity > 0) {
      acc.push({
        label: item.name,
        value: item.id,
      });
    }
    return acc;
  }, []);
  const segmentSelected = items.find((item) => item.id === payload.segment_id);
  const mutation = useMutation({
    mutationFn: fbAudienceApi.addAudience,
    onSuccess: () => {
      handleGetAudience({
        ...filterPayload,
        ad_account_id: ad_Account_id,
      });
      toggleOpen();
      toast({
        title: t('common.facebookAds.audiences.addCustomAudience'),
        status: 'success',
      });
    },
    onError: (e) => e,
  });

  const onAddAudience = async () => {
    if (!segmentSelected) return;
    mutation.mutate(payload);
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center flex-col">
        <span className="text-lg leading-8 font-medium tracking-[0.6px]">
          {t('common.facebookAds.audiences.addCustomAudience')}
        </span>
        <span className="text-secondary text-sm">
          {t('common.facebookAds.audiences.chooseSegment')}
        </span>
      </div>
      <div className="flex flex-col">
        <Label
          isRequire={true}
          className="mb-1"
          label={t('common.facebookAds.audiences.segment')}
        />
        <Select onValueChange={(value) => setPayload((prev) => ({ ...prev, segment_id: value }))}>
          <SelectTrigger className="w-full h-10 rounded-xl">
            <SelectValue placeholder={t('common.facebookAds.audiences.segmentPlaceholder')} />
          </SelectTrigger>
          <SelectContent className="max-h-[250px] overflow-auto p-2 rounded-xl">
            {newOptions.map((item) => (
              <SelectItem
                className={cn(
                  'text-sm p-2 rounded-md cursor-pointer',
                  item.value === payload.segment_id &&
                    '!bg-brand text-white hover:text-white focus:text-white',
                )}
                key={item.value}
                value={item.value}
              >
                {item.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {payload.segment_id && (
          <div className="flex items-center gap-1 mt-2">
            <span className="text-sm text-secondary">{t('common.facebookAds.totalContact')}:</span>
            <span className="font-medium">{Number(segmentSelected?.contact_quantity).toLocaleString()}</span>
          </div>
        )}
      </div>
      <div className="flex flex-col">
        <Label className="mb-1" label={t('common.facebookAds.audiences.name')} />
        <input
          value={payload.audience_name}
          onChange={(e) => setPayload((prev) => ({ ...prev, audience_name: e.target.value }))}
          className={cn('outline-none border h-10 w-full p-3 rounded-xl text-sm')}
          placeholder={t('common.facebookAds.enterAudienceName')}
        />
        <div className="flex items-center gap-1 text-xs text-secondary mt-2">
          <RiInformation2Line size={16} />
          {t('common.facebookAds.audiences.notice')}
        </div>
      </div>
      <div className="flex items-end justify-end gap-3">
        <Button
          onClick={toggleOpen}
          className="px-3 py-1 rounded-xl"
          variant={'secondary'}
          size={'lg'}
        >
          {t('common.button.cancel')}
        </Button>
        <Button
          onClick={onAddAudience}
          disabled={!Boolean(segmentSelected && payload.audience_name)}
          className="px-3 py-1 rounded-xl min-w-[144px]"
          size={'lg'}
        >
          {mutation.isPending ? (
            <RiLoader2Line className="animate-spin" />
          ) : (
            t('common.facebookAds.audiences.pushToFb')
          )}
        </Button>
      </div>
    </div>
  );
};
export default AddAudienceView;

const Label = ({
  label,
  className,
  isRequire,
}: {
  label: string;
  className?: string;
  isRequire?: boolean;
}) => (
  <span
    className={cn(
      'text-sm font-semibold text-secondary-foreground flex items-center gap-1',
      className,
    )}
  >
    {label}
    {isRequire && <span className="text-destructive">*</span>}
  </span>
);
