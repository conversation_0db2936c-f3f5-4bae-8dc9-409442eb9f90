import { cn } from '@/lib/utils';
import { DISTRIBUTION_STATUS } from '@/types/audience';

type Props = {
  status: DISTRIBUTION_STATUS;
};

const DistributionCel = ({ status }: Props) => {
  const mapClass = {
    ACTIVE: 'bg-green-200 text-green-500',
    PAUSED: 'bg-yellow-200 text-yellow-500',
    DELETED: 'bg-red-200 text-red-500',
  };

  return (
    <div className="flex items-center w-full justify-center">
      <div className={cn('rounded-md w-fit text-xs p-1', mapClass[status])}>{status}</div>
    </div>
  );
};
export default DistributionCel;
