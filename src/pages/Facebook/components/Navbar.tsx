import { TAB_FACEBOOK } from '@/constants/facebook';
import { cn } from '@/lib/utils';
import { RiBodyScanLine, RiSlideshow3Line } from '@remixicon/react';
import { t } from 'i18next';

const router = [
  {
    path: TAB_FACEBOOK.AUDIENCES,
    name: t('common.facebookAds.audiences.title'),
    icon: <RiBodyScanLine />,
  },
  {
    path: TAB_FACEBOOK.CAMPAIGN,
    name: t('common.facebookAds.campaign.title'),
    icon: <RiSlideshow3Line />,
  },
];

type Props = {
  path: TAB_FACEBOOK;
  setPath: (path: TAB_FACEBOOK) => void;
};

const Navbar = ({ path, setPath }: Props) => {
  return (
    <div className="flex items-center border-b border-brand">
      {router.map((item) => {
        return (
          <button
            onClick={() => setPath(item.path)}
            className={cn(
              'py-4 px-3 relative text-base leading-[26px] flex gap-2 items-center',
              path === item.path ? 'font-medium text-brand' : 'text-tertiary',
            )}
            key={item.path}
          >
            {item.icon}
            {item.name}
            {path === item.path && (
              <div className="absolute w-full h-0.5 bg-brand bottom-0 right-0 left-0" />
            )}
          </button>
        );
      })}
    </div>
  );
};
export default Navbar;
