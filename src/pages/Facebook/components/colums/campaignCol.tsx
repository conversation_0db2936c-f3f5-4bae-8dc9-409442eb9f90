import { DATE_FORMAT } from '@/constants/time';
import { TCampaign } from '@/types/audience';
import { fDate } from '@/utils/helper';
import { ColumnDef } from '@tanstack/react-table';
import { t } from 'i18next';
import DistributionCel from '../audiences/DistributionCel';
import BudgetCel from '../audiences/BudgetCel';
import ToggleStatusCel from '../audiences/ToggleStatusCell';
import { formatCurrency, formatNumber } from '@/utils/number';

const campaignCol = ({
  updateCampaign,
}: {
  updateCampaign: (value: TCampaign) => Promise<boolean>;
}): ColumnDef<TCampaign>[] => {
  return [
    {
      id: 'select',
      header: () => <div className="text-center">{t('common.button.onOff')}</div>,
      cell: ({ row }) => {
        return (
          <div className="flex justify-center">
            <ToggleStatusCel
              onToggleStatus={(isChecked) =>
                updateCampaign({ ...row.original, status: isChecked ? 'ACTIVE' : 'PAUSED' })
              }
              value={row.original.status === 'ACTIVE'}
            />
          </div>
        );
      },
      size: 80,
    },
    {
      accessorKey: 'name',
      header: t('common.facebookAds.campaign.title'),
      cell: ({ row }) => <div className="capitalize line-clamp-1">{row.original.name}</div>,
      size: 300,
      enableResizing: true,
    },
    {
      accessorKey: 'status',
      header: () => (
        <div className="w-full text-center">{t('common.facebookAds.campaign.distribution')}</div>
      ),
      cell: ({ row }) => <DistributionCel status={row.original.status} />,
      size: 100,
    },
    {
      accessorKey: 'objective',
      header: t('common.facebookAds.campaign.distribution'),
      cell: ({ row }) => <div className="capitalize">{row.original.objective || '--'}</div>,
      size: 200,
    },
    {
      accessorKey: 'daily_budget',
      header: t('common.facebookAds.campaign.budget'),
      cell: ({ row }) => (
        <BudgetCel
          value={row.original.daily_budget}
          onChangeBudget={async (value) =>
            await updateCampaign({ ...row.original, daily_budget: value })
          }
        />
      ),
      size: 150,
    },
    {
      accessorKey: 'clicks',
      header: t('common.facebookAds.campaign.approacher'),
      cell: ({ row }) => <div className="capitalize">{formatNumber(row.original.clicks,'en') || '--'}</div>,
      size: 100,
    },
    {
      accessorKey: 'impressions',
      header: t('common.facebookAds.campaign.impressions'),
      cell: ({ row }) => <div className="capitalize">{formatNumber(row.original.impressions,'en') || '--'}</div>,
      size: 100,
    },
    {
      accessorKey: 'spend',
      header: t('common.facebookAds.campaign.amountSpent'),
      cell: ({ row }) => <div className="capitalize">{formatCurrency(row.original.spend, 'USD') || '--'}</div>,
      size: 150,
    },
    {
      accessorKey: 'stop_time',
      header: t('common.facebookAds.campaign.endDate'),
      cell: ({ row }) => (
        <div className="capitalize">
          {row.original.stop_time ? fDate(row.original.stop_time, DATE_FORMAT.DD_MM_YYYY) : '--'}
        </div>
      ),
      size: 100,
    },
  ];
};

export default campaignCol;
