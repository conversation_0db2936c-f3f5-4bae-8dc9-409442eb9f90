import { useFBContext } from '@/pages/Facebook/context/FbAuthContext';
import { Box } from '@/components/Box';
import { Button } from '@/components/ui/button';
import { RiArrowRightSLine, RiLoader2Line } from '@remixicon/react';
import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';
import { IFbPage } from '@/types/facebook';
import { fbOauthApi } from '@/apis/fbOauth';
import { toast } from '@/hooks/use-toast';
import { RESPONSE_MESSAGES } from '@/constants/messages/response.message';
import { cn } from '@/lib/utils';
import { RiInformation2Line } from 'react-icons/ri';
import CustomToolTips from '@/components/CustomToolTips';

type TListBusinessProps = {
  onCloseModal?: () => void;
};

export const ListBusiness = ({ ...props }: TListBusinessProps) => {
  const { onCloseModal } = props;
  const { listPagesGrouped, updateStateSelected, handleRefetchListPage } = useFBContext();
  const { t } = useTranslation();
  const [loading, setLoading] = useState<boolean>(false);
  const [titleBusiness, setTitleBusiness] = useState<string>('');
  const [listAccount, setListAccount] = useState<IFbPage[]>([]);

  const handleSelectPage = async (account: IFbPage) => {
    const { id, selected } = account;
    if (selected) {
      return;
    }
    setLoading(true);
    try {
      await fbOauthApi.selectFbAdAccount({
        ad_account_id: id,
      });
      toast({
        status: 'success',
        description: RESPONSE_MESSAGES.SELECT_DEFAULT,
      });
      updateStateSelected(id);
      handleRefetchListPage();
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      onCloseModal && onCloseModal();
      setLoading(false);
    } catch (error) {
      setLoading(false);
      return error;
    }
  };

  const handleCheckDefault = () => {
    const selectedGroup = listPagesGrouped.find((group) =>
      group.accounts.some((account) => account.selected),
    );
    if (selectedGroup) {
      setTitleBusiness(selectedGroup.business_name);
      setListAccount(selectedGroup.accounts);
    }
  };

  useEffect(() => {
    if (!!listPagesGrouped.length) {
      handleCheckDefault();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [listPagesGrouped]);

  return (
    <Box className="h-[414px]">
      <Box variant={'col-start'} className="gap-2 bg-secondary rounded-2xl p-3 h-full w-[296px]">
        <p className="flex gap-2 items-center text-sm font-semibold text-primary mb-5">
          {t('common.facebookAds.account.portfolios', {
            value: listPagesGrouped.length > 1 ? 's' : '',
          })}
          <CustomToolTips
            className="text-xs z-50"
            content={
              <div className="w-[231px] text-xs">{t('common.facebookAds.account.tooltip')}</div>
            }
            element={<RiInformation2Line size={20} />}
          />
        </p>
        <div className="flex flex-col gap-2 overflow-auto">
          {listPagesGrouped.map((group) => {
            const isSelect = titleBusiness === group.business_name;
            return (
              <Button
                key={group.business_id}
                className={cn(
                  'w-[272px] bg-transparent hover:bg-tertiary justify-between p-2 h-full focus-visible:outline-none focus-visible:ring-0',
                  isSelect && 'bg-tertiary',
                )}
                onClick={() => {
                  setListAccount(group.accounts);
                  setTitleBusiness(group.business_name);
                }}
              >
                <p className="flex flex-col text-left">
                  <span className="text-sm font-semibold text-primary-crm">
                    {group.business_name}
                  </span>
                  <span className="text-xs font-normal text-secondary">
                    {group.accounts.length} {t('common.facebookAds.account.businessAsset')}
                  </span>
                </p>
                <RiArrowRightSLine
                  size={20}
                  color={'#20232C'}
                  className={cn(isSelect ? 'block' : 'hidden')}
                />
              </Button>
            );
          })}
        </div>
      </Box>
      {/*right col*/}
      {!!listAccount.length ? (
        <Box variant={'col-start'} className="flex-1 gap-2 rounded-2xl p-3 h-full">
          <p className="flex flex-col text-sm font-semibold text-primary mb-[2px]">
            {titleBusiness}
            <span className="text-xs text-secondary font-normal">
              {t('common.facebookAds.account.portfolios', {
                value: listAccount.length > 1 ? 's' : '',
              })}
            </span>
          </p>
          <div className="overflow-auto w-full h-full">
            {listAccount.map((account) => {
              const { name, account_id, is_accepted_term, business, selected, link_accept_tos } =
                account;
              return (
                <Box
                  key={account_id}
                  className={cn('w-full p-2 rounded-xl', selected ? 'bg-secondary' : '')}
                >
                  <p className="flex flex-col text-left">
                    <span className="text-sm font-semibold text-primary-crm">{name}</span>
                    <span className="text-xs font-normal text-secondary">{account_id}</span>
                  </p>
                  {typeof business !== 'string' && (
                    <>
                      {!is_accepted_term ? (
                        <div className="flex flex-col items-start text-sm">
                          <a
                            className="text-[#2C9EFF] underline"
                            href={link_accept_tos}
                            target="_blank"
                          >
                            {t('common.facebookAds.acceptTerm')}
                          </a>
                        </div>
                      ) : (
                        <Button
                          variant={!selected ? 'outline' : 'secondary'}
                          size={'lg'}
                          className={cn(
                            'p-3 rounded-xl text-sm font-semibold',
                            selected && 'bg-white',
                          )}
                          onClick={() => handleSelectPage(account)}
                          disabled={loading || !is_accepted_term || selected}
                        >
                          {loading ? (
                            <RiLoader2Line className="animate-spin" size={20} />
                          ) : selected ? (
                            t('common.button.selected')
                          ) : (
                            t('common.button.select')
                          )}
                        </Button>
                      )}
                    </>
                  )}
                </Box>
              );
            })}
          </div>
          <Button onClick={handleRefetchListPage} className="w-full" variant={'outline'}>
            {t('common.button.reload')}
          </Button>
        </Box>
      ) : (
        <Box variant={'col-center'} className="flex-1 justify-center items-center">
          <div className="text-center">{t('common.facebookAds.account.selectYourBusiness')}</div>
          <Button onClick={handleRefetchListPage} className="w-fit" variant={'outline'}>
            {t('common.button.reload')}
          </Button>
        </Box>
      )}
    </Box>
  );
};
