import { t } from 'i18next';
import SearchBar from '@/components/SearchBar';
import { useEffect, useState } from 'react';
import { TFilterCampaign } from '@/types/facebook';
// import { fTotoISOString } from '@/utils/helper';
import DateRangerPickerCampaign from '@/components/DateRanger/DateRangerPickerCampaign';
import { formatDateYYYYMMDD } from '@/utils/helper';

type TFilter = Omit<TFilterCampaign, 'currentPage' | 'pageSize'>;

type Props = {
  setFilterPayload: (filterPayload: TFilter) => void;
  filterPayload: TFilter;
  placeHolder?: string;
};

const FilterPanelCampaign = ({ filterPayload, setFilterPayload, placeHolder }: Props) => {
  const [search, setSearch] = useState<string>('');
  const [isSearch, setIsSearch] = useState<boolean>(false);

  useEffect(() => {
    if (!isSearch) {
      return;
    }
    const timeOut = setTimeout(() => {
      setFilterPayload({ ...filterPayload, search });
    }, 300);
    return () => clearTimeout(timeOut);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [search]);

  return (
    <div className="flex w-full gap-3 mb-6 mt-4">
      <SearchBar
        className="flex-1"
        placeholder={t('common.facebookAds.searchTemplate')}
        value={search}
        setSearchQuery={(value) => {
          setSearch(value);
          setIsSearch(true);
        }}
      />
      <DateRangerPickerCampaign
        placeholder={placeHolder ?? t('common.facebookAds.pushDate')}
        onChange={(date) => {
          if (!date.from && !date.to) {
            return;
          } else {
            setFilterPayload({
              ...filterPayload,
              date_preset: '',
              since: formatDateYYYYMMDD(date.from, '-'),
              until: formatDateYYYYMMDD(date.to, '-'),
            });
          }
        }}
        onSelectPreset={(preset) => {
          setFilterPayload({
            ...filterPayload,
            date_preset: preset,
            since: '',
            until: '',
          });
        }}
        onClear={() => {
          setFilterPayload({
            ...filterPayload,
            since: '',
            until: '',
          });
        }}
      />
    </div>
  );
};
export default FilterPanelCampaign;
