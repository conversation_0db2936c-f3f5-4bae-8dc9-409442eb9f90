import { t } from 'i18next';

const ConnectFacebookPopup = () => (
  <div className="w-full h-full flex flex-col items-center justify-center min-h-[500px] gap-2">
    <span className="font-semibold text-xl">{t('common.facebookAds.connectFacebook')}</span>
    <div className="flex items-center justify-center space-x-2">
      <div
        className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"
        style={{ animationDelay: '0ms' }}
      />
      <div
        className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"
        style={{ animationDelay: '150ms' }}
      />
      <div
        className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"
        style={{ animationDelay: '300ms' }}
      />
    </div>
  </div>
);

export default ConnectFacebookPopup;
