import Modal from '@/components/Modal';
import { Button } from '@/components/ui/button';
import { RiLogoutBoxLine } from '@remixicon/react';
import i18n from '@/config-translation';
import { ErrorIcon } from '@/assets/ErrorIcon';
import { useFBContext } from '../context/FbAuthContext';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

const LogoutPopup = () => {
  const { logout } = useFBContext();
  const [open, setOpen] = useState(false);
  const { t } = useTranslation();
  return (
    <Modal
      openModal={open}
      isCloseIcon={false}
      onOpenChange={setOpen}
      trigger={
        <Button
          size={'lg'}
          className="p-2 rounded-sm bg-transparent hover:bg-[#f3f4f6] w-full justify-start"
          variant={'secondary'}
        >
          <RiLogoutBoxLine size={20} color={'#20232C'} />
          {t('common.button.logOut')}
        </Button>
      }
      className="max-w-[529px]"
    >
      <div className="flex items-center flex-col">
        <ErrorIcon />
        <div>{i18n.t('common.facebookAds.logoutFromFb')}</div>
        <div className="flex flex-col mt-2 items-center text-sm text-secondary">
          <div>{i18n.t('common.facebookAds.confirmLogout')}</div>
          <div>{i18n.t('common.facebookAds.subConfirmLogout')}</div>
        </div>
        <div className="grid grid-cols-2 gap-3 mt-6 w-full">
          <Button
            onClick={() => setOpen(false)}
            className="px-3 py-1 rounded-xl w-full"
            variant={'secondary'}
            size={'lg'}
          >
            {i18n.t('common.button.cancel')}
          </Button>
          <Button
            onClick={() => {
              logout();
              setOpen(false);
            }}
            variant={'destructive'}
            className="px-3 py-1 rounded-xl w-full"
            size={'lg'}
          >
            {i18n.t('common.button.logOut')}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default LogoutPopup;
