import AddAudiencePopup from './audiences/AddAudiencePopup';
import LogoutPopup from './LogoutPopup';
import { useFBContext } from '../context/FbAuthContext';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { RiArrowDownSLine, RiArrowLeftRightLine, RiNewsLine } from '@remixicon/react';
import { useEffect, useState } from 'react';
import SelectAccountPopup from './SelectAccountPopup';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';
import { TFilterAudience } from '@/types/facebook';

interface IHeaderProps {
  filterPayload?: TFilterAudience;
  ad_Account_id?: string;
}

const Header = ({ ...props }: IHeaderProps) => {
  const { filterPayload, ad_Account_id } = props;
  const { isLogin, listPages, loading, isAccountSelected } = useFBContext();
  const [showModal, setShowModal] = useState(false);
  const { t } = useTranslation();

  const isSelect = listPages.find((item) => item.selected);
  useEffect(() => {
    if (!loading && listPages.length > 0) {
      if (!isSelect) {
        setShowModal(true);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loading, listPages]);

  return (
    <div className="flex flex-col gap-4">
      <div className="text-left mb-6">
        <div className="flex-1 h-[64px]">
          <h3 className="text-2xl font-medium mb-2">{t('common.facebookAds.title')}</h3>
          <p className="text-base font-normal text-tertiary-foreground tracking-[0.2px]">
            {t('common.facebookAds.subTitle')}
          </p>
        </div>
      </div>

      {isLogin && (
        <>
          <div
            className={cn(
              'flex items-center',
              isAccountSelected ? 'justify-between' : 'justify-end',
            )}
          >
            <div className="flex w-[250px] justify-between py-1 px-1 border rounded-2xl border-[#E1E2E3]">
              <div className="flex gap-1 items-center">
                <div className="p-1 border rounded-full border-[#E1E2E3] w-6 h-6 flex items-center justify-center">
                  <RiNewsLine className="flex-shrink-0" size={16} color="#515667" />
                </div>
                <span className="truncate overflow-hidden w-full text-start font-medium">
                  {isSelect?.name || t('common.facebookAds.switchAccount')}
                </span>
              </div>
              <div className="pl-1 border-l border-[#E1E2E3]">
                <DropdownMenu>
                  <DropdownMenuTrigger>
                    <div className="bg-secondary flex h-8 w-8 border items-center justify-center rounded-xl gap-2 p-2.5">
                      <RiArrowDownSLine className="ml-auto flex-shrink-0" size={20} />
                    </div>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    className="w-[250px] p-3 flex flex-col gap-1"
                    align="end"
                    alignOffset={-4}
                    sideOffset={10}
                  >
                    {isAccountSelected && (
                      <DropdownMenuItem
                        className="cursor-pointer h-10"
                        onClick={() => setShowModal(true)}
                      >
                        <RiArrowLeftRightLine size={20} color={'#20232C'} />
                        {t('common.facebookAds.switchAccount')}
                      </DropdownMenuItem>
                    )}
                    <LogoutPopup />
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            <div className="flex items-center gap-3">
              {isSelect && (
                <AddAudiencePopup filterPayload={filterPayload} ad_Account_id={ad_Account_id} />
              )}
            </div>
          </div>
        </>
      )}
      {showModal && <SelectAccountPopup open={showModal} setOpen={setShowModal} />}
    </div>
  );
};
export default Header;
