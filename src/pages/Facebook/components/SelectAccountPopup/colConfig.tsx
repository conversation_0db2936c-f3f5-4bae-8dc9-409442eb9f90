import { IFbPage } from '@/types/facebook';
import { ColumnDef } from '@tanstack/react-table';
import ActionCol from './ActionCol';
import StatusCol from './StatusCol';
import { t } from 'i18next';
import TermCol from './TermCol';
import CustomToolTips from '@/components/CustomToolTips';
import { RiAlertLine } from '@remixicon/react';

type Props = {
  onCloseModal?: () => void;
};

const colConfig = ({ onCloseModal }: Props): ColumnDef<IFbPage>[] => {
  return [
    {
      accessorKey: t('common.facebookAds.adsAccount'),
      cell: ({ row }) => (
        <div className="flex flex-col justify-start h-full gap-2 mb-auto">
          <div className="font-medium p-1 flex items-center gap-1">
            {row.original.name}
            {typeof row.original.business === 'string' && (
              <CustomToolTips
                content={t('common.facebookAds.accountBusinessNotice')}
                element={<RiAlertLine color="#E06C14" size={20} />}
                className="z-50"
              />
            )}
          </div>
          <div className="flex items-center gap-1 text-sm">
            {t('common.facebookAds.ownedBy')}
            <span className="font-semibold">
              {typeof row.original.business !== 'string' ? row.original.business.name : '--'}
            </span>
          </div>
        </div>
      ),
      size: 315,
    },
    {
      accessorKey: 'Status',
      cell: ({ row }) => <StatusCol status={row.original.account_status} />,
    },
    {
      id: 'term',
      enableHiding: false,
      cell: ({ row }) => <TermCol account={row.original} />,
      size: 400,
    },
    {
      header: '',
      accessorKey: 'action',
      cell: ({ row }) => <ActionCol account={row.original} onCloseModal={onCloseModal} />,
      size: 135,
    },
  ];
};
export default colConfig;
