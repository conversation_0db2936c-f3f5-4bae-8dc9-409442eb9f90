import { t } from 'i18next';

type Props = {
  status: number;
};

const StatusCol = ({ status }: Props) => {
  switch (status) {
    case 1:
      return (
        <div className="text-xs p-1 rounded-lg text-[#205B2B] bg-[#E0F8E3] w-fit px-2 py-1">
          {t('common.facebookAds.active')}
        </div>
      );
    default:
      return (
        <div className="text-xs text-[#BF1616] bg-[#FFD9D9] w-fit px-2 py-1 rounded-lg">
          {t('common.facebookAds.inactive')}
        </div>
      );
  }
};
export default StatusCol;
