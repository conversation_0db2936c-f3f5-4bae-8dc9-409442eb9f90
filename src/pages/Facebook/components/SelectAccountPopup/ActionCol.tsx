import { Button } from '@/components/ui/button';
import { RiLoader2Line } from '@remixicon/react';
import { t } from 'i18next';
import { cn } from '@/lib/utils';
import { useState } from 'react';
import { useFBContext } from '../../context/FbAuthContext';
import { fbOauthApi } from '@/apis/fbOauth';
import { IFbPage } from '@/types/facebook';
import { toast } from '@/hooks/use-toast';
import { RESPONSE_MESSAGES } from '@/constants/messages/response.message';

type Props = {
  account: IFbPage;
  onCloseModal?: () => void;
};

const ActionCol = ({ account, onCloseModal }: Props) => {
  const [loading, setLoading] = useState<boolean>(false);
  const { updateStateSelected } = useFBContext();
  const { id, is_accepted_term, business, selected } = account;

  const handleSelectPage = async () => {
    if (selected) {
      return;
    }
    setLoading(true);
    try {
      await fbOauthApi.selectFbAdAccount({
        ad_account_id: account.id,
      });
      toast({
        status: 'success',
        description: RESPONSE_MESSAGES.SELECT_DEFAULT,
      });
      updateStateSelected(id);
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      onCloseModal && onCloseModal();
      setLoading(false);
    } catch (error) {
      setLoading(false);
      return error;
    }
  };

  return (
    <div className="flex w-full items-center">
      <Button
        variant={!selected ? 'outline' : 'secondary'}
        size={'lg'}
        className={cn('p-3 rounded-xl text-sm font-semibold')}
        onClick={handleSelectPage}
        disabled={loading || typeof business === 'string' || !is_accepted_term || selected}
      >
        {loading ? (
          <RiLoader2Line className="animate-spin" size={20} />
        ) : selected ? (
          t('common.button.selected')
        ) : (
          t('common.button.select')
        )}
      </Button>
    </div>
  );
};

export default ActionCol;
