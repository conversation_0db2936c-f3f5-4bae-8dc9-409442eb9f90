import { useTranslation } from 'react-i18next';

const TitlePopup = () => {
  const { t } = useTranslation();
  return (
    <>
      <div className="flex flex-col gap-2">
        <h2 className="font-medium text-lg tracking-[0.6px] line-clamp-1">
          {t('common.facebookAds.account.title')}
        </h2>
        <p>
          <span className="text-sm tracking-[0.2px] text-secondary line-clamp-1">
            {t('common.facebookAds.account.subTitle')}
          </span>
          <span className="text-sm tracking-[0.2px] text-secondary line-clamp-1">
            {t('common.facebookAds.account.description')}
          </span>
        </p>
      </div>
    </>
  );
};
export default TitlePopup;
