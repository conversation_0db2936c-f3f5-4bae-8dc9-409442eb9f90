import Modal from '@/components/Modal';
import TitlePopup from './TitlePopup';
import { useFBContext } from '../../context/FbAuthContext';
import { LoadingButtonIcon } from '@/components/Loading/LoadingButton';
import { ListBusiness } from '@/pages/Facebook/components/ListBusiness';

type Props = {
  open: boolean;
  setOpen?: (value: boolean) => void;
};

const SelectAccountPopup = ({ open, setOpen }: Props) => {
  const { refetchLoading } = useFBContext();

  const onCloseModal = () => setOpen && setOpen(false);

  return (
    <Modal
      className="max-w-[786px] gap-6 max-h-svh overflow-auto"
      isCloseIcon={false}
      onOpenChange={setOpen}
      openModal={open}
      trigger={<></>}
    >
      <TitlePopup />
      {refetchLoading ? (
        <div className="w-full border-[1px] border-secondary shadow-sm rounded-2xl h-[484px] flex items-center justify-center">
          <LoadingButtonIcon width={'50px'} height={'50px'} />
        </div>
      ) : (
        <ListBusiness onCloseModal={onCloseModal} />
      )}
    </Modal>
  );
};
export default SelectAccountPopup;
