import { IFbPage } from '@/types/facebook';
import { t } from 'i18next';

type Props = { account: IFbPage };

const TermCol = ({ account }: Props) => {
  const { is_accepted_term, link_accept_tos } = account;
  return !is_accepted_term ? (
    <div className="flex flex-col items-start text-sm">
      <span className="font-semibold">{t('common.facebookAds.actionRequired')}</span>
      <span>{t('common.facebookAds.descriptionActionRequired')}</span>
      <a className="text-[#2C9EFF] underline" href={link_accept_tos} target="_blank">
        {t('common.facebookAds.acceptTerm')}
      </a>
    </div>
  ) : null;
};
export default TermCol;
