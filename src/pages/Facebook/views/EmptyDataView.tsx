import { fbOauth<PERSON><PERSON> } from '@/apis/fbOauth';
import ConnectModal from '@/components/ConnectModal';
import { useMutation } from '@tanstack/react-query';
import { useFBContext } from '@/pages/Facebook/context/FbAuthContext';
import SelectAccountModal from '@/components/SelectAccountModal';
import SelectAccountPopup from '@/pages/Facebook/components/SelectAccountPopup';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

const BIG360_URL = import.meta.env.REACT_APP_BIG360_URL;

const EmptyDataView = () => {
  const { t } = useTranslation();
  const [showModal, setShowModal] = useState(false);

  const { loading, isAccountSelected, isLogin } = useFBContext();
  const mutation = useMutation({
    mutationFn: fbOauthApi.getFbOauthUrl,
    onSuccess: (data) => {
      window.location.href = data;
    },
  });

  const handleLogin = () => mutation.mutate(BIG360_URL + '/crm360/facebook');

  return (
    <div className="flex flex-col items-center gap-6 my-32">
      {!isLogin && (
        <ConnectModal
          disabled={mutation.isSuccess}
          onConnect={handleLogin}
          value={'Facebook'}
          loading={mutation.isPending}
        />
      )}
      {!isAccountSelected && isLogin && (
        <SelectAccountModal
          disabled={mutation.isSuccess}
          onSelect={()=>setShowModal(true)}
          value={'Facebook PM'}
          loading={loading}
          buttonText={t('common.button.selectFacebookAccount')}
        />
      )}
      {showModal && <SelectAccountPopup open={showModal} setOpen={setShowModal} />}
    </div>
  );
};

export default EmptyDataView;
