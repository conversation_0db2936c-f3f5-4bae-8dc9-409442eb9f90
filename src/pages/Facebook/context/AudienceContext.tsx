import { fbAudience<PERSON><PERSON> } from '@/apis/fbAudience';
import { deleteEmptyKey } from '@/utils/helper';
import { useMutation } from '@tanstack/react-query';
import { createContext, ReactNode, useRef, useState } from 'react';
import { IAudienceDetail } from '@/types/audience';

type TAudienceContext = {
  items: IAudienceDetail[];
  count: number;
  handleAddAudience: (payload: IAudienceDetail) => void;
  handleGetAudience: (payload: { [key: string]: unknown }) => void;
  loading: boolean;
};

const AudienceContext = createContext<TAudienceContext>({
  items: [],
  count: 0,
  handleAddAudience: () => {},
  handleGetAudience: () => {},
  loading: false,
});

const AudienceProvider = ({ children }: { children: ReactNode }) => {
  const [audiences, setAudiences] = useState<{
    items: IAudienceDetail[];
    count: number;
  }>({ items: [], count: 0 });

  const controllerRef = useRef<AbortController | null>(null);

  const mutation = useMutation({
    /* eslint-disable @typescript-eslint/no-explicit-any */
    mutationFn: async (params: { payload: any; signal: AbortSignal }) => {
      return await fbAudienceApi.getAudience(params.payload, params.signal);
    },
    onError: (e) => e,
    onSuccess: (data) => {
      setAudiences({ items: data.items, count: data.total });
    },
  });

  const handleAddAudience = (payload: IAudienceDetail) => {
    setAudiences((prev) => ({
      items: [payload, ...prev.items],
      count: prev.count + 1,
    }));
  };

  const handleGetAudience = (payload: { [key: string]: unknown }) => {
    if (controllerRef.current) {
      controllerRef.current.abort();
    }

    controllerRef.current = new AbortController();

    mutation.mutate({
      payload: deleteEmptyKey(payload),
      signal: controllerRef.current.signal,
    });
  };

  return (
    <AudienceContext.Provider
      value={{
        items: audiences.items,
        count: audiences.count,
        handleAddAudience,
        handleGetAudience,
        loading: mutation.isPending,
      }}
    >
      {children}
    </AudienceContext.Provider>
  );
};

export { AudienceContext, AudienceProvider };
