import { fbAudience<PERSON><PERSON> } from '@/apis/fbAudience';
import { TCampaign } from '@/types/audience';
import { deleteEmptyKey } from '@/utils/helper';
import { createContext, ReactNode, useRef, useState } from 'react';

type TCampaignContext = {
  items: TCampaign[];
  count: number;
  handleAddCampaign: (payload: TCampaign) => void;
  handleGetCampaign: (payload: { [key: string]: unknown }) => void;
  loading: boolean;
};

const CampaignContext = createContext<TCampaignContext>({
  items: [],
  count: 0,
  handleAddCampaign: () => {},
  handleGetCampaign: () => {},
  loading: false,
});

const CampaignProvider = ({ children }: { children: ReactNode }) => {
  const [campaign, setCampaign] = useState<{
    items: TCampaign[];
    count: number;
  }>({ items: [], count: 0 });

  const [loading, setLoading] = useState(false);
  const controllerRef = useRef<AbortController | null>(null);

  const handleAddCampaign = (payload: TCampaign) => {
    setCampaign((prev) => ({
      items: [payload, ...prev.items],
      count: prev.count + 1,
    }));
  };

  const handleGetCampaign = async (payload: { [key: string]: unknown }) => {
    if (controllerRef.current) controllerRef.current.abort();
    controllerRef.current = new AbortController();

    setLoading(true);
    try {
      const data = await fbAudienceApi.getCampaign(
        deleteEmptyKey(payload),
        controllerRef.current.signal,
      );
      setCampaign({ items: data.data, count: data.count });
      /* eslint-disable @typescript-eslint/no-explicit-any */
    } catch (err: any) {
      if (err.name !== 'CanceledError') {
        console.error('getCampaign error:', err);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <CampaignContext.Provider
      value={{
        items: campaign.items,
        count: campaign.count,
        handleAddCampaign,
        handleGetCampaign,
        loading,
      }}
    >
      {children}
    </CampaignContext.Provider>
  );
};

export { CampaignContext, CampaignProvider };
