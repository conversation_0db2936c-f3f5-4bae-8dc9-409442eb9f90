import { get } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import TableContactList from '@/components/ContactList/TableContactList';
import { TContactHistoryItems, TContactItems, TTimeReminder } from '@/types/contactList';
import { TMultiResponse } from '@/types/ResponseApi';
import { TPagination } from '@/types/table';
import { PAGE_SIZE, QUERY_KEY } from '@/utils/constants';
import { useMutation, useQuery } from '@tanstack/react-query';
import { ColumnDef, SortingState } from '@tanstack/react-table';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { ContactListColumn } from '@/pages/ContactList/components/columns/ContactListColumn';
import { arrayToUrlParams, mergeArraysById, verifyParamSearch } from '@/utils/helper';
import { formatDate } from '@/utils/date';
import { useTranslation } from 'react-i18next';
import { getSelector, useAppSelector } from '@/hooks/reduxHooks';
import dayjs from 'dayjs';

//-------------------------------------------------------------------
const defaultParams = {
  page: 1,
  limit: 50,
  order_by: '-date_created',
};

//-------------------------------------------------------------------

interface ContactListProps {
  setExportData: (state: { [key: string]: string }[]) => void;
  isDataSet: boolean;
}

const ContactList: React.FC<ContactListProps> = ({ setExportData, isDataSet }) => {
  const { isOverdue, listOverdue, isRefresh } = useAppSelector(getSelector('contactList'));
  const resetFilterRef = useRef<(() => void) | null>(null);
  const { t } = useTranslation();
  const [listRemove, setListRemove] = useState<TContactItems[]>([]);
  const [searchParams] = useSearchParams();
  const [idExpand, setIdExpand] = useState<string>('');
  const [sortContactList, setSortContactList] = useState<SortingState>([]);
  const allQueryParams = Object.fromEntries(searchParams.entries());
  const [loadingExpand, setLoadingExpand] = useState<boolean>(false);
  const [historyCall, setHistoryCall] = useState<{
    history: TContactHistoryItems[];
    activity: TContactHistoryItems[];
  }>({
    activity: [],
    history: [],
  });
  const [contactListData, setContactListData] = useState<TMultiResponse<TContactItems>>({
    code: 0,
    message: '',
    data: {
      items: [],
      count: 0,
    },
  });

  const [pagination, setPagination] = useState<TPagination>({
    currentPage: 1,
    pageSize: PAGE_SIZE,
  });

  const abortControllerRef = useRef<AbortController | null>(null);

  const handleGetList = async ({
    params,
    signal,
  }: {
    params?: { [key: string]: string | number | boolean };
    signal?: AbortSignal;
  }) => {
    const res = await get<TContactItems>({
      endpoint: ENDPOINTS.contact_list,
      params: {
        page: pagination.currentPage,
        limit: pagination.pageSize,
        datatype: isDataSet ? 'DATASET' : 'AUDIENCE',
        order_by: `${sortContactList.length > 0 ? `${arrayToUrlParams(sortContactList)},` : ''}-date_created,full_name`,
        ...verifyParamSearch(allQueryParams),
        ...params,
      },
      signal,
    });

    const contact: TMultiResponse<TContactItems> = res.data as TMultiResponse<TContactItems>;
    if (contact.data?.items && isOverdue) {
      setContactListData({
        ...contactListData,
        data: {
          ...contactListData.data,
          items: contact.data.items ?? [],
          count: contact.data.count ?? 0,
        },
      });
    }
    handleResetFilter();
    return res;
  };

  const {
    data: contactListDataResponse,
    isLoading: loadingContactList,
    refetch: refetchContact,
    isRefetching: isRefetchingContact,
  } = useQuery({
    queryKey: [
      QUERY_KEY.CONTACT_LIST,
      allQueryParams,
      pagination,
      sortContactList,
      isRefresh,
      isDataSet,
    ],
    enabled: !isOverdue,
    refetchOnMount: true,
    queryFn: async () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      abortControllerRef.current = new AbortController();
      return handleGetList({ signal: abortControllerRef.current.signal });
    },
  });

  const contactListOverdueMutation = useMutation({
    mutationFn: () =>
      handleGetList({
        params: { reminder_overdue: true },
      }),
  });

  const handleFormatContactToExport = (items: TContactItems[]) => {
    let exportData: { [key: string]: string }[] = [];
    items.map((item) => {
      const formatData = {
        [t('common.name')]: item.full_name,
        [t('common.phoneNumber')]: item.phone_number,
        [t('common.email')]: item.email,
        [t('common.segment.title')]: item.segments.map((sm) => sm.segment.name).join(', '),
        [t('common.dob')]: formatDate(item.dob),
        [t('common.gender')]: item.gender,
        [t('common.address')]: item.person_address,
        [t('common.location')]: item.person_province,
        [t('common.company')]: item.company_name,
        [t('common.position')]: item.position,
      };
      exportData = [...exportData, formatData];
    });
    return exportData;
  };

  const dataTable: TContactItems[] = useMemo(() => {
    return contactListData?.data?.items ?? [];
  }, [contactListData]);

  const handleResetFilter = () => {
    if (resetFilterRef.current) {
      resetFilterRef.current();
    }
  };

  const handleUpdateReminder = (id: number, payload: TTimeReminder | null) => {
    setContactListData((prev) => {
      const newItems =
        prev.data?.items?.map((item) => (item.id === id ? { ...item, reminder: payload } : item)) ||
        [];
      return { ...prev, data: { ...prev.data, items: newItems, count: prev.data?.count || 0 } };
    });
  };

  const countItems = contactListData?.data?.count ?? 0;

  const fetchHistory = async () => {
    return get<TContactHistoryItems>({
      endpoint: ENDPOINTS.history_call(idExpand),
      params: defaultParams,
    }).then((res) => {
      const historyRes = (res.data as TMultiResponse<TContactHistoryItems>)?.data?.items ?? [];
      setHistoryCall((prev) => ({ ...prev, history: historyRes }));
      return historyRes;
    });
  };

  const fetchActivity = async () => {
    return get<TContactHistoryItems>({
      endpoint: ENDPOINTS.activity(idExpand),
      params: defaultParams,
    }).then((res) => {
      const activityRes = (res.data as TMultiResponse<TContactHistoryItems>)?.data?.items ?? [];
      setHistoryCall((prev) => ({ ...prev, activity: activityRes }));
      return activityRes;
    });
  };

  const handleCallHistory = async () => {
    setLoadingExpand(true);
    Promise.all([fetchHistory(), fetchActivity()]).finally(() => setLoadingExpand(false));
  };

  const flatData: TContactHistoryItems[] = useMemo(() => {
    return (mergeArraysById(historyCall.history, historyCall.activity) as TContactHistoryItems[])
      .filter((item) => item.type !== 'email')
      .sort((a, b) => dayjs(b.date_created).valueOf() - dayjs(a.date_created).valueOf());
  }, [historyCall]);

  const handleGetListOverdue = (pagination: TPagination) => {
    setPagination(pagination);
    if (isOverdue) {
      contactListOverdueMutation.mutate();
    }
  };

  useEffect(() => {
    if (isOverdue) {
      contactListOverdueMutation.mutate();
    }
    //   eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams, sortContactList, isOverdue]);

  useEffect(() => {
    if (!!flatData.length) {
      const temp = dataTable.map((item) => {
        if (item.id.toString() === idExpand) {
          return {
            ...item,
            history: flatData,
          };
        }
        return item;
      });
      setContactListData({
        ...contactListData,
        data: {
          ...contactListData.data,
          items: temp,
          count: contactListData.data?.count || 0,
        },
      });
      setIdExpand('');
    }
    //   eslint-disable-next-line react-hooks/exhaustive-deps
  }, [flatData]);

  useEffect(() => {
    if (contactListDataResponse?.data) {
      let exportData: { [key: string]: string }[] = [];
      if (isOverdue && listOverdue.items) {
        setContactListData({ ...contactListData, data: listOverdue });
        exportData = handleFormatContactToExport(listOverdue.items || []);
      } else {
        setContactListData(contactListDataResponse?.data);
        exportData = handleFormatContactToExport(contactListDataResponse?.data?.data?.items || []);
      }
      setExportData(exportData);
    }
    //   eslint-disable-next-line react-hooks/exhaustive-deps
  }, [contactListDataResponse, pagination.currentPage, setExportData, isOverdue]);

  useEffect(() => {
    if (idExpand) {
      handleCallHistory().finally(() => {});
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [idExpand]);

  const columns = ContactListColumn({
    listRemove,
    pagination,
    countItems,
    dataTable,
    setListRemove,
    handleUpdateReminder,
    setPagination,
    handleResetFilter,
    refetchContact,
    setIdExpand,
    isDataSet,
  }) as ColumnDef<TContactItems>[];

  return (
    <TableContactList
      data={dataTable ?? []}
      count={countItems}
      isShowHistory={true}
      isOverdue={isOverdue}
      columns={columns}
      sortContactList={sortContactList}
      pagination={pagination}
      setPagination={handleGetListOverdue}
      isLoading={loadingContactList || isRefetchingContact}
      getRowSelected={setListRemove}
      onResetFilter={(ref) => (resetFilterRef.current = ref)}
      setSortContactList={setSortContactList}
      historyData={{
        loadingExpand: loadingExpand,
      }}
      isDataSet={isDataSet}
    />
  );
};

export default ContactList;
