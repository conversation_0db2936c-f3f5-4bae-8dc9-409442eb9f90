import PhoneBoughtView from './components/ContactContainer';
import HeaderModel from './components/HeaderModel';
import GetPhoneModal from './components/GetPhone/GetPhoneModal';
import { Button } from '@/components/ui/button';
import { RiAddCircleLine } from '@remixicon/react';
import Breadcrumb from '@/components/Breadcrumb';
import { useTranslation } from 'react-i18next';

const GetPhone = () => {
  const { t } = useTranslation();
  return (
    <>
      <Breadcrumb />
      <HeaderModel
        title={t('getPhoneNumber.title')}
        subTitle={t('getPhoneNumber.description')}
        rightContent={
          <GetPhoneModal
            trigger={
              <Button
                variant={'primary'}
                className="text-background-foreground h-10 rounded-2xl flex gap-1 px-3 ml-auto font-medium bg-brand hover:bg-primary-crm"
              >
                <RiAddCircleLine size={20} />
                {t('getPhoneNumber.title')}
              </Button>
            }
          />
        }
      />
      <PhoneBoughtView />
    </>
  );
};
export default GetPhone;
