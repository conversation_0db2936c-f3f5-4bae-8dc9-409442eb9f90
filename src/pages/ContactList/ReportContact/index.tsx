import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { RiFileChart2Line } from '@remixicon/react';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { get } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { TReportItem } from '@/types/report';
import { toCamelCase } from '@/utils/helper';
import { ChartConfig, ChartContainer } from '@/components/ui/chart';
import { Pie, PieChart, ResponsiveContainer } from 'recharts';
import { cn } from '@/lib/utils';

const defaultReportData: TReportItem = {
  rent_hotline: 0,
  total_confirmed_contacts: 0,
  total_contacts: 0,
  total_duration: 0,
  total_imported_contacts: 0,
  total_interactions: 0,
};
const n = (key: keyof TReportItem) => toCamelCase(key);
export const ReportContact: React.FC = () => {
  const { t } = useTranslation();
  const [reportData, setReportData] = useState<TReportItem>(defaultReportData);
  const {
    total_contacts,
    total_imported_contacts,
    total_confirmed_contacts,
    total_interactions,
    total_duration,
    rent_hotline,
  } = reportData;

  const handleGetReport = async () => {
    await get<TReportItem>({
      endpoint: ENDPOINTS.report.get,
    }).then((res) => {
      setReportData((res.data as unknown as TReportItem) || defaultReportData);
    });
  };

  const chartConfig = {
    total_contacts: {
      label: t('report.totalContacts'),
      color: '#F0F0F0',
    },
    total_imported_contacts: {
      label: '345345345',
      color: '#8F5CFF',
    },
  } satisfies ChartConfig;

  const chartData = useMemo(() => {
    return [
      {
        contact: t('report.totalContacts'),
        total: total_contacts - total_imported_contacts,
        fill: '#F0F0F0',
      },
      {
        contact: t('report.totalImportedContacts'),
        total: total_imported_contacts,
        fill: '#8F5CFF',
      },
    ];
  }, [t, total_contacts, total_imported_contacts]);

  return (
    <Popover onOpenChange={(open) => open && handleGetReport()}>
      <PopoverTrigger asChild>
        <Button size={'lg'} className="px-3 py-1 rounded-2xl" variant={'secondary'}>
          <RiFileChart2Line size={20} />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        align={'end'}
        side={'bottom'}
        className="w-[365px] p-3 rounded-2xl border-tertiary shadow-[0px_0px_32px_0px_rgba(9,10,13,0.02),_0px_4px_20px_-8px_rgba(9,10,13,0.10)]"
      >
        <div className="flex justify-between ">
          <div className="w-[160px]">
            <ReportItem count={total_contacts} isBetween={false} title={n('total_contacts')} />
            <ReportItem
              count={total_imported_contacts}
              isBetween={false}
              title={n('total_imported_contacts')}
            />
            <div className="-rotate-90 w-fit">
              <ChartContainer config={chartConfig} className="aspect-square h-[80px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={chartData}
                      cx="50%"
                      cy="50%"
                      outerRadius={40}
                      dataKey="total"
                      nameKey="contact"
                    />
                  </PieChart>
                </ResponsiveContainer>
              </ChartContainer>
            </div>
          </div>
          <div className="w-[160px]">
            {/*todo update plan name*/}
            <p className="text-primary-crm font-medium text-sm mb-1">Basic Plan</p>
            <ReportItem count={total_confirmed_contacts} title={n('total_confirmed_contacts')} />
            <ReportItem count={rent_hotline} title={n('rent_hotline')} />
            <ReportItem count={total_interactions} title={n('total_interactions')} />
            <ReportItem count={total_duration} title={n('total_duration')} />
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

type TReportItemProps = {
  title: string;
  count: number;
  isBetween?: boolean;
};
const ReportItem: React.FC<TReportItemProps> = ({ ...props }: TReportItemProps) => {
  const { title, count, isBetween = true } = props;
  const { t } = useTranslation();
  return (
    <div
      className={cn(
        'flex items-center gap-1 my-0.5',
        isBetween ? 'justify-between' : 'justify-start',
      )}
    >
      <p className="text-tertiary font-normal text-xs">{t(`report.${title}`)}</p>
      <p className="text-primary-crm font-medium text-sm">{count}</p>
    </div>
  );
};
