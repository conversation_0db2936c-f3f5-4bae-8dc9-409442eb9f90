import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { CONTACT_STATUS } from '@/constants/contact-list';
import { getSelector, useAppSelector } from '@/hooks/reduxHooks';
import type { TPhoneBought } from '@/types/contactList';
import type { Row } from '@tanstack/react-table';
// import RenewPopup from './RenewPopup';

type Props = {
  row: Row<TPhoneBought>;
  handleRenew: () => void;
  handleSelectPhone: () => void;
  isDisabled?: boolean;
};

const PhoneSelectAction = ({ row, handleSelectPhone, isDisabled }: Props) => {
  const { phone } = useAppSelector(getSelector('used_phone'));
  const checked = row.original.phone_number === phone.using_hotline;

  switch (row.original.status) {
    case CONTACT_STATUS.USED:
    case CONTACT_STATUS.AVAILABLE:
      return (
        <RadioGroup>
          <div className="flex items-center space-x-2">
            <RadioGroupItem
              value={row.original.phone_number}
              id={`phone-${row.original.phone_number}`}
              checked={checked}
              onClick={handleSelectPhone}
              disabled={isDisabled}
              className={checked?'border-brand':''}
            />
          </div>
        </RadioGroup>
      );
    // case CONTACT_STATUS.EXPIRED:
    //   return <RenewPopup handleRenew={handleRenew} />;
    case CONTACT_STATUS.SPAM:
      return (
        <RadioGroup>
          <div className="flex items-center space-x-2">
            <RadioGroupItem
              value={row.original.phone_number}
              id={`phone-${row.original.phone_number}`}
              checked={false}
              disabled={true}
            />
          </div>
        </RadioGroup>
      );
    default:
      return null;
  }
};

export default PhoneSelectAction;
