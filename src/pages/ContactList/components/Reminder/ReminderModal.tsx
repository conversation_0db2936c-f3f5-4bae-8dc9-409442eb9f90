import Modal from '@/components/Modal';
import { TContactItems, TTimeReminder } from '@/types/contactList';

import { ReactNode, useState } from 'react';
import DeleteReminderPopup from './DeleteReminderPopup';
import { useMutation } from '@tanstack/react-query';
import { reminderApi } from '@/apis/reminder';
import { toast } from '@/hooks/use-toast';
import { t } from 'i18next';
import ReminderPopup from './ReminderPopup';
import { getSelector, useAppDispatch, useAppSelector } from '@/hooks/reduxHooks';
import { setListOverdue } from '@/store/contactList/contactListSlice';

type Props = {
  row: TContactItems;
  trigger: ReactNode;
  handleUpdateReminder: (id: number, payload: TTimeReminder | null) => void;
};

const ReminderModal = ({ trigger, row, handleUpdateReminder }: Props) => {
  const [open, setOpen] = useState(false);
  const dispatch = useAppDispatch();
  const { listOverdue } = useAppSelector(getSelector('contactList'));

  const toggleModal = () => setOpen(!open);
  const [isShowDelete, setIsShowDelete] = useState(false);

  const mutationDelete = useMutation({
    mutationFn: reminderApi.remove,
    onSuccess: () => {
      handleUpdateReminder(row.id, null);
      toast({
        status: 'success',
        description: t('common.reminder.delete'),
        duration: 3000,
      });
      if (listOverdue.count === 1) {
        dispatch(
          setListOverdue({
            items: [],
            count: 0,
            isOverdue: false,
          }),
        );
      }
      handleClose();
    },
    onError: (error) => {
      handleClose();
      return error;
    },
  });

  const handleClose = () => {
    setIsShowDelete(false);
    setOpen(false);
  };

  const handleDelete = () => {
    mutationDelete.mutate(row.reminder?.id.toString() || '');
  };

  return (
    <>
      <Modal
        onOpenChange={setOpen}
        openModal={open}
        trigger={<button onClick={toggleModal}>{trigger}</button>}
        className="max-w-[850px]"
        isCloseIcon={false}
      >
        <ReminderPopup
          onOpenDeleteModal={() => setIsShowDelete(true)}
          row={row}
          handleUpdateReminder={handleUpdateReminder}
          onOpenModal={toggleModal}
        />
      </Modal>
      <DeleteReminderPopup
        loading={mutationDelete.isPending}
        open={isShowDelete}
        onClose={setIsShowDelete}
        onDelete={handleDelete}
      />
    </>
  );
};
export default ReminderModal;
