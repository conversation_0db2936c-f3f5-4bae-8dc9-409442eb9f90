import { TContactItems, TTimeReminder } from '@/types/contactList';
import { useEffect, useState } from 'react';
import { REMINDER_STATUS } from '@/constants/reminder';
import { RiAlarmLine } from '@remixicon/react';
import { fDate } from '@/utils/helper';
import { DATE_FORMAT } from '@/constants/time';
import CustomToolTips from '@/components/CustomToolTips';
import ReminderModal from './ReminderModal';
import { useTranslation } from 'react-i18next';

type Props = {
  row: TContactItems;
  handleUpdateReminder: (id: number, payload: TTimeReminder | null) => void;
};
const TEN_MINUTES = 10 * 60 * 1000;

const NoticeReminder = ({ row, handleUpdateReminder }: Props) => {
  const { t } = useTranslation();
  const [reminderStatus, setReminderStatus] = useState<REMINDER_STATUS | null>(null);
  const [action, setAction] = useState(Object.values(REMINDER_STATUS));
  const targetDate = new Date(row.reminder?.time_reminder || '');
  const now = new Date();
  const timeDifference = targetDate.getTime() - now.getTime();

  const removeAction = (status: REMINDER_STATUS) => {
    setAction(action.filter((item) => item !== status));
  };

  useEffect(() => {
    if (row.reminder === null) {
      setReminderStatus(REMINDER_STATUS.DONE);
      return;
    }

    switch (true) {
      case timeDifference <= 0:
        if (!action.includes(REMINDER_STATUS.OVER_DUE)) {
          return;
        }
        setReminderStatus(REMINDER_STATUS.OVER_DUE);
        removeAction(REMINDER_STATUS.OVER_DUE);
        break;
      case timeDifference <= TEN_MINUTES:
        if (!action.includes(REMINDER_STATUS.TEN_MINUTES)) {
          return;
        }
        setReminderStatus(REMINDER_STATUS.TEN_MINUTES);
        removeAction(REMINDER_STATUS.TEN_MINUTES);
        break;
      case timeDifference > 0:
        if (!action.includes(REMINDER_STATUS.DOING)) {
          return;
        }
        setReminderStatus(REMINDER_STATUS.DOING);
        removeAction(REMINDER_STATUS.DOING);
        break;
      default:
        if (!action.includes(REMINDER_STATUS.DOING)) {
          return;
        }
        setReminderStatus(REMINDER_STATUS.DONE);
        removeAction(REMINDER_STATUS.DONE);
        break;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [action, row.reminder, timeDifference]);

  return (
    <CustomToolTips
      content={
        reminderStatus === 'over_due'
          ? t('common.button.overDue')
          : !!row.reminder?.time_reminder
            ? fDate(row.reminder?.time_reminder || '', DATE_FORMAT.DD_MM_YYYY_HH_mm)
            : t('common.reminder.title')
      }
      element={
        <div className="flex items-center cursor-pointer">
          <ReminderModal
            handleUpdateReminder={handleUpdateReminder}
            row={row}
            trigger={<RiAlarmLine size={20} color={mapColor(reminderStatus)} />}
          />
        </div>
      }
    />
  );
};
export default NoticeReminder;

const mapColor = (status: REMINDER_STATUS | null): string => {
  const map = {
    [REMINDER_STATUS.OVER_DUE]: '#F53E3E',
    [REMINDER_STATUS.DONE]: '#20232C',
    [REMINDER_STATUS.DOING]: '#27923A',
    [REMINDER_STATUS.TEN_MINUTES]: '#27923A',
  };
  return map[status || REMINDER_STATUS.DONE];
};
