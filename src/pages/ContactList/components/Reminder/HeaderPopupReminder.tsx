import Typography from '@/components/Typography';
import { TContactItems } from '@/types/contactList';
import { RiCheckboxBlankCircleFill } from '@remixicon/react';
import { t } from 'i18next';
import styled from 'styled-components';
import ActionReminder from './ActionReminder';

type Props = {
  row: TContactItems;
  isShowAction: boolean;
  onEdit: () => void;
  onDelete: () => void;
};

const HeaderPopupReminder = ({ row, isShowAction = false, onEdit, onDelete }: Props) => {
  return (
    <div className="flex justify-between items-center">
      <div className="flex flex-col">
        <Typography fontWeight={'medium'} content={t('common.setReminder')} />
        <div className="flex items-center gap-x-2">
          <Label>{row.full_name}</Label>
          <RiCheckboxBlankCircleFill size={8} />
          <Label>{row.phone_number}</Label>
        </div>
      </div>
      {isShowAction && <ActionReminder onDelete={onDelete} onEdit={onEdit} />}
    </div>
  );
};
export default HeaderPopupReminder;

const Label = styled.label`
  font-size: 16px;
  font-weight: 500;
`;
