import { ErrorIcon } from '@/assets/ErrorIcon';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { RiLoader2Line } from '@remixicon/react';
import { t } from 'i18next';
type Props = {
  open: boolean;
  onClose: (close: boolean) => void;
  onDelete: () => void;
  loading?: boolean;
};

const DeleteReminderPopup = ({ open, onClose, onDelete, loading }: Props) => {
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[529px] py-4 px-6">
        <div className="flex flex-col items-center mt-7">
          <ErrorIcon />
          <span className="font-medium text-xl leading-8 tracking-[0.6px]">
            {t('common.reminder.deleteTitle')}
          </span>
          <div className="flex text-sm text-secondary flex-col items-center tracking-[0.2px] mt-2">
            <span>{t('common.reminder.confirmDelete')}</span>
            <span>{t('common.reminder.subTitle')}</span>
          </div>
          <div className="grid w-full grid-cols-2 gap-x-4 mt-6">
            <Button
              onClick={() => onClose(false)}
              size={'lg'}
              variant={'outline'}
              className="rounded-xl font-medium"
            >
              {t('common.button.cancel')}
            </Button>
            <Button
              onClick={onDelete}
              size={'lg'}
              variant={'delete'}
              className="rounded-xl font-medium"
            >
              {loading ? (
                <RiLoader2Line size={20} className="animate-spin" />
              ) : (
                t('common.button.delete')
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
export default DeleteReminderPopup;
