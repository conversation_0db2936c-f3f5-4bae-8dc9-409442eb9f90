import { Button } from '@/components/ui/button';
import { RiDeleteBin6Line } from '@remixicon/react';
import { t } from 'i18next';

type Props = {
  onEdit: () => void;
  onDelete: () => void;
};

const ActionReminder = ({ onDelete }: Props) => {
  return (
    <div className="flex items-center gap-3">
      <Button
        onClick={onDelete}
        className="rounded-xl py-1 px-3 border-[#F53E3E] text-[#F53E3E] bg-inherit border text-base flex gap-1 hover:text-error-text hover:bg-white hover:border-error-text"
        size={'lg'}
        variant={'secondary'}
      >
        <RiDeleteBin6Line />
        {t('common.button.delete')}
      </Button>
    </div>
  );
};
export default ActionReminder;
