import { reminder<PERSON>pi } from '@/apis/reminder';
import DatePicker from '@/components/DatePicker';
import { TimePicker } from '@/components/TimePicker';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import { TContactItems, TTimeReminder } from '@/types/contactList';
import { TReminder } from '@/types/reminder';
import { useMutation } from '@tanstack/react-query';
import { t } from 'i18next';
import { useEffect, useState } from 'react';
import HeaderPopupReminder from './HeaderPopupReminder';
import { RiLoader2Line } from '@remixicon/react';
import {
  expectedDate,
  fStringToDate,
  fTotoISOString,
  isErrorDate,
  isOverDue,
} from '@/utils/helper';

type StateReminder = 'edit' | 'init' | 'none';

type Props = {
  row: TContactItems;
  handleUpdateReminder: (id: number, payload: TTimeReminder) => void;
  onOpenModal: () => void;
  onOpenDeleteModal: () => void;
};

type TPayload = { date: string; note: string; activity: string; time: string };
const defaultPayload: TPayload = { date: '', time: '', note: '', activity: '' };

type TError = { date: string; time: string; common: string };
const defaultError: TError = { date: '', time: '', common: '' };

const ReminderPopup = ({ row, handleUpdateReminder, onOpenModal, onOpenDeleteModal }: Props) => {
  const [payload, setPayload] = useState<TPayload>(defaultPayload);
  const [isShowAction, setIsShowAction] = useState<StateReminder>('none');
  const [error, setError] = useState<TError>(defaultError);
  const [disable, setDisable] = useState<boolean>(true);

  useEffect(() => {
    const fDate = `${payload.date} ${payload.time}`;
    const time = fTotoISOString(fDate);
    const isOverDueDate = isOverDue(time);
    if (isOverDueDate) {
      setError({ date: '', time: '', common: t('common.error.overDue') });
      return;
    }
    setError({ date: '', time: '', common: '' });
  }, [payload.date, payload.time]);

  useEffect(() => {
    if (!row.reminder) {
      return;
    }
    setIsShowAction(row.reminder?.time_reminder ? 'init' : 'none');
    const { date, time } = expectedDate(row.reminder?.time_reminder);
    setPayload({
      date,
      time,
      note: row.reminder.note || '',
      activity: row.reminder.activity || '',
    });
  }, [row.reminder]);

  useEffect(() => {
    checkReminderDiff();
    //   eslint-disable-next-line react-hooks/exhaustive-deps
  }, [payload, row.reminder]);

  const checkReminderDiff = () => {
    const { date, time } = expectedDate(row?.reminder?.time_reminder ?? '');
    const note = row.reminder?.note || '';
    const activity = row.reminder?.activity || '';
    if (!payload.date || !payload.time) {
      return;
    }
    if (
      date !== payload.date ||
      time !== payload.time ||
      note !== payload.note ||
      activity !== payload.activity
    ) {
      setDisable(false);
    } else {
      setDisable(true);
    }
  };

  const mutationCreate = useMutation({
    mutationFn: reminderApi.create,
    onSuccess: (e) => {
      handleUpdateReminder(row.id, {
        id: e.id,
        time_reminder: e.time_reminder,
        note: payload.note,
        activity: payload.activity,
      });
      toast({
        status: 'success',
        description: t('common.reminder.createSuccess', { contactName: row.full_name }),
        duration: 3000,
      });
      onOpenModal();
    },
    onError: (error) => error,
  });
  const mutationUpdate = useMutation({
    mutationFn: reminderApi.update,
    onSuccess: (e) => {
      handleUpdateReminder(row.id, {
        id: e.id,
        time_reminder: e.time_reminder,
        note: payload.note,
        activity: payload.activity,
      });
      toast({
        status: 'success',
        description: t('common.reminder.update'),
        duration: 3000,
      });
      onOpenModal();
    },
    onError: (error) => error,
  });

  const handleCreateReminder = () => {
    const errorDate = isErrorDate(payload.date, payload.time);
    if (errorDate) {
      setError({ ...errorDate, common: '' });
      return;
    }
    if (error.common + error.date + error.time !== '') {
      return;
    }
    const fDate = `${payload.date} ${payload.time}`;
    const time = fTotoISOString(fDate);
    const reminderPayload: TReminder = {
      contact_id: row.reminder?.time_reminder ? row.reminder?.id || 0 : row.id,
      time_reminder: time,
      note: payload.note,
      activity: payload.activity,
      is_overdue: false,
    };

    if (row.reminder?.time_reminder) {
      mutationUpdate.mutate(reminderPayload);
    } else {
      mutationCreate.mutate(reminderPayload);
    }
  };

  // const overDue =;
  const timeDt = payload.time.split(':');

  return (
    <>
      <HeaderPopupReminder
        row={row}
        isShowAction={isShowAction !== 'none'}
        onEdit={() => setIsShowAction('edit')}
        onDelete={() => {
          onOpenModal();
          onOpenDeleteModal();
        }}
      />
      <div className="flex flex-col gap-4">
        <input
          onChange={(e) => setPayload({ ...payload, note: e.target.value })}
          value={payload.note}
          className="border h-10 rounded-xl outline-none p-3"
          type="text"
          placeholder={t('common.activity')}
        />
        <textarea
          value={payload.activity}
          onChange={(e) => setPayload({ ...payload, activity: e.target.value })}
          className="h-[138px] border resize-none p-3 rounded-xl outline-none"
          placeholder={t('common.takeNote')}
        />
      </div>
      <div className="grid grid-cols-2 gap-4 relative">
        <DatePicker
          error={error.date || Boolean(error.common)}
          defaultDate={fStringToDate(payload.date)!}
          onChange={(value) => {
            if (!!value) {
              setPayload({ ...payload, date: value });
            }
          }}
        />
        <TimePicker
          error={error.time || Boolean(error.common)}
          value={{ hour: timeDt[0] || null, minute: timeDt[1] || null }}
          onChange={(time) => setPayload({ ...payload, time: `${time.hour}:${time.minute}` })}
        />
        {error.common && isShowAction !== 'init' && (
          <div className="absolute top-10 left-1 text-xs text-red-500 font-medium">
            {error.common}
          </div>
        )}
        <Button onClick={onOpenModal} className="rounded-xl" size={'lg'} variant={'cancel'}>
          {t('common.button.cancel')}
        </Button>
        <Button
          type="button"
          onClick={handleCreateReminder}
          disabled={disable}
          className="rounded-xl bg-primary-crm text-secondary-foreground_crm"
          size={'lg'}
          variant={'primary'}
        >
          {mutationCreate.isPending || mutationUpdate.isPending ? (
            <RiLoader2Line size={20} className="animate-spin" />
          ) : (
            t('common.setReminder')
          )}
        </Button>
      </div>
    </>
  );
};

export default ReminderPopup;
