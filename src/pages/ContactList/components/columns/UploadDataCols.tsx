import { CONTACT_FIELD, LABEL_CONTACT_FIELD } from '@/constants/contact-list';
import { ColumnDef } from '@tanstack/react-table';
import EditableCell from '../EditableCell';
import { TDataUpload, TUpdateValue } from '@/hooks/uploadReducer';
import { Button } from '@/components/ui/button';
import { RiDeleteBin6Line } from '@remixicon/react';
import { cn } from '@/lib/utils';

const uploadDataCols = ({
  handleUpload,
  onFilterError,
  itemHover,
  onDeleteRow,
}: {
  handleUpload: (payload: TUpdateValue) => void;
  onFilterError: (keyFilter: CONTACT_FIELD) => void;
  itemHover: TDataUpload | null;
  onDeleteRow: (id: string) => void;
}): ColumnDef<TDataUpload>[] => {
  return Object.values(CONTACT_FIELD).map((item) => {
    switch (item) {
      case CONTACT_FIELD.ID:
        return {
          id: 'id',
          enableHiding: false,
          cell: ({ row }) => (
            <div className="line-clamp-1 text-center w-11">
              {itemHover?.id === row.original.id ? (
                <Button
                  onClick={() => onDeleteRow(String(row.original.id))}
                  variant={'textOnly'}
                  size={'icon'}
                  className="p-1.5 bg-card hover:text-destructive"
                >
                  <RiDeleteBin6Line size={20} />
                </Button>
              ) : (
                +row.original.id + 1
              )}
            </div>
          ),
          minSize: sizeCol(item),
          meta: 'px-1.5',
        };

      default:
        return {
          accessorKey: item,
          header: ({ column }) => {
            const allData = column.getFacetedRowModel().rows.map((row) => row.original);
            const countError = allData.filter((data) => data.error.includes(item)).length;

            return (
              <div className={cn('flex items-center whitespace-nowrap gap-2')}>
                {LABEL_CONTACT_FIELD[item]}
                {countError > 0 && (
                  <button
                    className="font-medium text-xs px-1 w-[22px] h-[22px] py-0.5 leading-[18px] flex-shrink-0 rounded-md bg-[#F53E3E] text-secondary-foreground_crm"
                    onClick={() => onFilterError(item)}
                  >
                    {countError}
                  </button>
                )}
              </div>
            );
          },
          cell: ({ row }) => <EditableCell handleUpload={handleUpload} keyValue={item} row={row} />,
          minSize: sizeCol(item),
        };
    }
  });
};
export default uploadDataCols;

export type TUploadData = {
  keyObj: CONTACT_FIELD;
};

const sizeCol = (keyObj: CONTACT_FIELD): number => {
  switch (keyObj) {
    case CONTACT_FIELD.Email:
    case CONTACT_FIELD.Address:
    case CONTACT_FIELD.Name:
    case CONTACT_FIELD.Company:
    case CONTACT_FIELD.Position:
    case CONTACT_FIELD.PhoneNumber:
      return 200;
    case CONTACT_FIELD.ID:
      return 44;
    default:
      return 140;
  }
};
