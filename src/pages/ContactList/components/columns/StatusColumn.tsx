import { CONTACT_STATUS } from '@/constants/contact-list';
import { cn } from '@/lib/utils';
import React from 'react';

type Props = {
  status: CONTACT_STATUS;
};

const StatusColumn: React.FC<Props> = ({ status }: Props) => {
  const statusMap = {
    [CONTACT_STATUS.AVAILABLE]: 'bg-[#E0F8E3] text-[#205B2B] p-1 rounded-md text-xs',
    [CONTACT_STATUS.SPAM]: 'bg-[#FFD9D9] text-[#BF1616]',
    [CONTACT_STATUS.USED]: 'bg-yellow-200 text-yellow-700',
    [CONTACT_STATUS.EXPIRED]: 'bg-[#FFD9D9] text-[#BF1616]',
  };
  return (
    <div className={cn('w-fit rounded-xl p-2 text-sm capitalize', statusMap[status])}>{status}</div>
  );
};
export default StatusColumn;
