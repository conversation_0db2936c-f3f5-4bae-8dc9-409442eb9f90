import { ColumnDef } from '@tanstack/react-table';
import { TContactItems } from '@/types/contactList';
import { Checkbox } from '@/components/ui/checkbox';
import { RiMailLine, RiPhoneLine } from '@remixicon/react';
import CustomToolTips from '@/components/CustomToolTips';
import { formatDate, handleCheckEmptyValue, toCamelCase } from '@/utils/helper';
import { LABEL } from '@/utils/constants';
import { useTranslation } from 'react-i18next';
import { useCallContext } from '@/pages/context/CallContext';
import { RenderGender } from '@/components/RenderGender';
import { RenderStatus } from '@/components/RenderStatus';
import { Avatar } from '@/components/Avatar/Avatar';

const n = (key: keyof TContactItems) => key;

export const ContactListSegmentColumn = (): ColumnDef<TContactItems>[] => {
  const { setContactCall } = useCallContext();

  const { t } = useTranslation();

  return [
    {
      id: n('full_name'),
      accessorKey: n('full_name'),
      size: 300,
      minSize: 230,
      cell: ({ row, getValue }) => {
        const full_name = getValue<string>();
        const fb_uid = row.original.fb_uid;

        return (
          <div className="flex justify-between">
            <div className="flex gap-3 items-center">
              <Checkbox
                onCheckedChange={(value) => row.toggleSelected(!!value)}
                checked={row.getIsSelected() || (row.getIsSomeSelected() && 'indeterminate')}
                aria-label="Select all"
              />
              <Avatar uid={fb_uid} name={full_name} />
              {full_name ? (
                <CustomToolTips
                  element={<p className="truncate">{full_name}</p>}
                  content={full_name}
                />
              ) : (
                <>-</>
              )}
            </div>
          </div>
        );
      },
      header: () => {
        return <div className="flex gap-3 w-full">{t('common.name')}</div>;
      },
    },
    {
      id: n('phone_number'),
      accessorKey: n('phone_number'),
      size: 200,
      minSize: 145,
      header: () => <p className="w-full">{t('common.phoneNumber')}</p>,
      cell: ({ getValue, row }) => {
        const phoneNumber = getValue<string>();
        const status = row.original.status;
        if (!phoneNumber) {
          return <></>;
        }
        return (
          <div className="flex items-center gap-1">
            <RenderStatus status={status} />
            <button
              onClick={() => setContactCall(row.original)}
              className="flex flex-1 justify-between gap-1 hover:text-success-hover hover:cursor-pointer hover:border-success-hover hover:border-b"
            >
              {phoneNumber}
              <RiPhoneLine size={20} color={'#27923A'} />
            </button>
          </div>
        );
      },
    },
    {
      id: n('email'),
      accessorKey: n('email'),
      size: 200,
      minSize: 100,
      header: () => <p className="w-full">{t('common.email')}</p>,
      cell: ({ getValue }) => {
        const email = getValue<string>();
        if (!email) {
          return handleCheckEmptyValue(getValue<string>());
        }
        return (
          <CustomToolTips
            element={
              <div className="flex gap-1 justify-between">
                <p className="truncate hover:cursor-pointer hover:border-b">{email}</p>
                <span>
                  <RiMailLine size={20} color={'#515667'} />
                </span>
              </div>
            }
            content={email}
          />
        );
      },
    },

    {
      id: toCamelCase(LABEL.segment),
      accessorKey: toCamelCase(LABEL.segment),
      size: 280,
      minSize: 200,
      header: () => <p className="w-full">{t('common.segment.title')}</p>,
      cell: ({ getValue }) => {
        const segment = getValue<string>();
        if (!segment) {
          return handleCheckEmptyValue(segment);
        }
        return (
          <CustomToolTips
            element={
              <div className="flex gap-1 justify-between">
                <p className="truncate">{segment}</p>
              </div>
            }
            content={segment}
          />
        );
      },
    },
    {
      id: n('dob'),
      accessorKey: n('dob'),
      size: 130,
      minSize: 130,
      header: () => <p className="w-full">{t('common.dob')}</p>,
      enableSorting: true,
      sortUndefined: 'last',
      sortDescFirst: false,
      cell: ({ getValue }) => {
        const dob = getValue<string>();
        return <p className="truncate">{dob ? formatDate(dob, '/') : '-'}</p>;
      },
    },
    {
      id: n('gender'),
      accessorKey: n('gender'),
      size: 92,
      minSize: 92,
      header: () => <p className="w-full">{t('common.gender')}</p>,
      cell: ({ getValue }) => {
        const genderValue = getValue<string>();
        if (!genderValue) {
          return <>-</>;
        }
        return (
          <div className="w-full flex items-center">
            <RenderGender gender={genderValue} />
          </div>
        );
      },
    },
    {
      id: n('person_address'),
      accessorKey: n('person_address'),
      size: 232,
      minSize: 180,
      header: () => <p className="w-full">{t('common.address')}</p>,
      cell: ({ getValue }) => {
        const person_address = getValue<string>();
        if (!person_address) {
          return handleCheckEmptyValue(person_address);
        }
        return (
          <CustomToolTips
            element={<p className="truncate">{person_address}</p>}
            content={person_address}
          />
        );
      },
    },
    {
      id: n('company_name'),
      accessorKey: n('company_name'),
      size: 132,
      minSize: 132,
      header: () => <p className="w-full">{t('common.company')}</p>,
      cell: ({ getValue }) => {
        const companyName = getValue<string>();
        if (!companyName) {
          return handleCheckEmptyValue(companyName);
        }
        return (
          <CustomToolTips
            element={<p className="truncate">{companyName}</p>}
            content={companyName}
          />
        );
      },
    },
    {
      id: n('position'),
      accessorKey: n('position'),
      size: 230,
      minSize: 130,
      header: () => <p className="w-full">{t('common.position')}</p>,
      cell: ({ getValue }) => {
        const position = getValue<string>();
        if (!position) {
          return handleCheckEmptyValue(position);
        }
        return (
          <CustomToolTips element={<p className="truncate">{position}</p>} content={position} />
        );
      },
    },
  ] as ColumnDef<TContactItems>[];
};
