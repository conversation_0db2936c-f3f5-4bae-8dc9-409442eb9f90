import { TPhoneBought } from '@/types/contactList';
import { Row } from '@tanstack/react-table';
import { useAppDispatch } from '@/hooks/reduxHooks';
import { choosePhone, getUsedPhone } from '@/store/usedPhone/usedPhoneSlice';
import { ROOT_PATH, ROOT_ROUTE } from '@/constants/router';
import { useNavigate } from 'react-router-dom';
import { cancelRenewal } from '@/store/phoneBought/action';
import PhoneSelectAction from '@/pages/ContactList/components/PhoneSelectAction';

type Props = {
  row: Row<TPhoneBought>;
  isDisabled?: boolean;
};

const SelectPhoneCol = ({ row, isDisabled }: Props) => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const handleSelectPhone = async () => {
    dispatch(choosePhone(row.original.id))
      .unwrap()
      .then(() => {
        dispatch(getUsedPhone())
          .unwrap()
          .then(() => navigate(`${ROOT_PATH}/${ROOT_ROUTE.contact['']}`));
      });
  };

  const handleRenew = async () => {
    dispatch(cancelRenewal({ hotline_id: row.original.id, is_renewal: true }));
  };

  return (
    <div className="flex gap-2 justify-end items-center">
      <PhoneSelectAction
        row={row}
        handleRenew={handleRenew}
        handleSelectPhone={handleSelectPhone}
        isDisabled={isDisabled}
      />
    </div>
  );
};

export default SelectPhoneCol;
