import React, { useEffect, useRef, useState } from 'react';
import ShowMore from '@/components/ShowMore';
import CustomToolTips from '@/components/CustomToolTips';
import { TagFillIcon } from '@/assets/TagFillIcon';

interface Segment {
  type: string;
  name: string;
  color?: string;
}

interface SegmentColProps {
  segments: Segment[];
  className?: string;
}

const SegmentCol: React.FC<SegmentColProps> = ({ ...props }: SegmentColProps) => {
  const { segments, className } = props;
  const [showSegment, setShowSegment] = useState<Segment[]>([]);
  const [hideSegments, setHideSegments] = useState<Segment[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current || segments.length === 0) {
      setShowSegment([]);
      setHideSegments([]);
      return;
    }

    const handleHideSegments = () => {
      const container = containerRef.current;
      if (!container) {
        return;
      }
      const containerWidth = container.offsetWidth - 50;
      let currentWidth = 0;
      const showList: Segment[] = [];
      const hideList: Segment[] = [];

      if (segments.length === 1) {
        setShowSegment(segments);
        setHideSegments([]);
      } else {
        segments.forEach((segment) => {
          const estimatedWidth = segment.name.length + (segment.color ? 20 : 0) + 40;

          if (currentWidth + estimatedWidth <= containerWidth) {
            showList.push(segment);
            currentWidth += estimatedWidth;
          } else {
            hideList.push(segment);
          }
        });

        setShowSegment(showList);
        setHideSegments(hideList);
      }
    };

    handleHideSegments();

    const resizeObserver = new ResizeObserver(handleHideSegments);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, [segments]);

  if (segments.length === 0) {
    return <div className={`w-full ${className}`}>-</div>;
  }

  return (
    <div ref={containerRef} className={`w-full flex items-center gap-1 ${className}`}>
      {showSegment.map((item, index) => (
        <CustomToolTips
          key={index}
          element={
            <div className="flex items-center justify-center w-fit h-[26px] text-center p-1 rounded-xl truncate bg-secondary-foreground_crm">
              {item.color && <TagFillIcon color={item.color} />}
              <p className="ml-[5px] truncate">{item.name}</p>
            </div>
          }
          content={item.name}
        />
      ))}

      {hideSegments.length > 0 && (
        <ShowMore
          onRemove={() => {}}
          isRemoveClick={true}
          className={'h-[26px]'}
          selected={hideSegments.map((item) => ({
            value: item.type,
            label: item.name,
            color: item.color,
          }))}
        />
      )}
    </div>
  );
};
export default SegmentCol;
