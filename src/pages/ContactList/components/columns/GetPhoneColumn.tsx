import { ColumnDef } from '@tanstack/react-table';
import { TPhoneBought } from '@/types/contactList';
import { CONTACT_LABEL } from '@/constants/contact-list/label';
import { KEY_PHONE_BOUGHT } from '@/constants/contact-list';
import { handleFormatPhone } from '@/utils/helper';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

interface IGetPhonesColumn {
  isDisabledCheckbox: boolean;
}

export const getPhonesColumn = ({ isDisabledCheckbox = false }: IGetPhonesColumn) => {
  return [
    {
      header: CONTACT_LABEL.phone_number,
      accessorKey: KEY_PHONE_BOUGHT.phone_number,
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-3">
            <RadioGroup>
              <div className="flex items-center space-x-2">
                <RadioGroupItem
                  value={row.original.phone_number}
                  checked={row.getIsSelected()}
                  disabled={isDisabledCheckbox}
                  /* eslint-disable @typescript-eslint/no-explicit-any */
                  onClick={(e: any) => {
                    const value = e?.target?.value;
                    row.toggleSelected(!!value);
                  }}
                  className={row.getIsSelected() ? 'border-brand' : ''}
                />
              </div>
            </RadioGroup>
            {handleFormatPhone(row.original.phone_number)}
          </div>
        );
      },
    },
    // {
    //   header: () => {
    //     return <div className="text-center w-[189px]">{CONTACT_LABEL.switchboard}</div>;
    //   },
    //   cell: ({ row }) => (
    //     <div className="text-center w-[189px]">{row.original.telecom_provider}</div>
    //   ),
    //   accessorKey: KEY_PHONE_BOUGHT.telecom_provider,
    //   size: 189,
    // },
  ] as ColumnDef<TPhoneBought>[];
};
