import { TPhoneBought } from '@/types/contactList';
import { Row } from '@tanstack/react-table';
import ActionPopup from '../ActionPopup';

type Props = {
  row: Row<TPhoneBought>;
  isDisabled?: boolean;
};

const ActionColumn = ({ row }: Props) => {
  return (
    <div className="flex gap-2 justify-end items-center">
      <ActionPopup phone={row.original} />
    </div>
  );
};

export default ActionColumn;
