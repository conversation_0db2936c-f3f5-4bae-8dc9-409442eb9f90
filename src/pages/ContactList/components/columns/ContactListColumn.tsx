import { ColumnDef } from '@tanstack/react-table';
import { TContactItems, TSegments, TTimeReminder } from '@/types/contactList';
// import { Checkbox } from '@/components/ui/checkbox';
import ButtonExpand from '@/components/table/ButtonExpand';
import { RiEdit2Line, RiMailLine, RiPhoneLine } from '@remixicon/react';
import CustomToolTips from '@/components/CustomToolTips';
import { formatDate, handleCheckEmptyValue, handleFormatPhone, toCamelCase } from '@/utils/helper';
import { LABEL } from '@/utils/constants';
import { useTranslation } from 'react-i18next';
// import NoticeReminder from '../Reminder/NoticeReminder';
import { Link } from 'react-router-dom';
import { ContactSubmitForm } from '@/components/ContactList/ContactSubmitForm';
import Modal from '@/components/Modal';
import { TPagination } from '@/types/table';
import { useCallContext } from '@/pages/context/CallContext';
// import { ModalRemoveContact } from '@/components/ContactList/ModalRemoveContact';
import { RenderGender } from '@/components/RenderGender';
import { Avatar } from '@/components/Avatar/Avatar';
import { reminderApi } from '@/apis/reminder';
import { ROOT_PATH, ROOT_ROUTE } from '@/constants/router';
import SegmentCol from '@/pages/ContactList/components/columns/SegmentCol';
import { useMemo } from 'react';
import { getSelector, useAppSelector } from '@/hooks/reduxHooks';

const n = (key: keyof TContactItems) => key;

type TContactListColumnProps = {
  listRemove: TContactItems[];
  pagination: TPagination;
  countItems: number;
  dataTable?: TContactItems[];
  isContactInSegment?: boolean;
  idExpand?: string;
  setPagination: (pagination: TPagination) => void;
  setListRemove: (value: TContactItems[]) => void;
  handleUpdateReminder: (id: number, payload: TTimeReminder | null) => void;
  handleResetFilter?: () => void;
  refetchContact: () => void;
  setIdExpand?: (idExpand: string) => void;
  isDataSet: boolean
};

const TYPE = 'CRM';
export const ContactListColumn = ({
  ...props
}: TContactListColumnProps): ColumnDef<TContactItems>[] => {

  const {
    listRemove,
    pagination,
    countItems,
    // isContactInSegment = false,
    dataTable,
    isDataSet,
    setPagination,
    // setListRemove,
    handleUpdateReminder,
    // handleResetFilter,
    refetchContact,
    setIdExpand,
  } = props;
  const { phone } = useAppSelector(getSelector('used_phone'));

  const { setContactCall } = useCallContext();

  const { t } = useTranslation();
  const handleSubmitSuccess = () => {
    refetchContact();
    setPagination({
      ...pagination,
      currentPage: 1,
    });
  };

  const handleMergeArray = (arrSrc: TSegments[]) => [
    ...arrSrc.map((item) => ({
      name: item.segment.name,
      type: TYPE,
      color: item.segment.color,
    })),
  ];

  const handleRemoveReminder = (contact: TContactItems) => {
    const targetDate = new Date(contact.reminder?.time_reminder || '');
    const now = new Date();
    const timeDifference = targetDate.getTime() - now.getTime();
    const TEN_MINUTES = 10 * 60 * 1000;
    if (timeDifference <= TEN_MINUTES) {
      reminderApi.remove(contact.reminder?.id.toString() || '').then(() => {
        handleUpdateReminder(contact.id ?? 0, null);
      });
    }
    setContactCall(contact);
  };

  return useMemo<ColumnDef<TContactItems>[]>(
    () =>
      [
        {
          id: n('full_name'),
          accessorKey: n('full_name'),
          enableSorting: false,
          size: 300,
          minSize: 230,
          cell: ({ row, getValue }) => {
            const full_name = getValue<string>();
            const fb_uid = row.original.fb_uid;
            const { original } = row;
            return (
              <div className="flex justify-between">
                <div className="flex gap-3 items-center">
                  {/*<Checkbox*/}
                  {/*  onCheckedChange={(value) => row.toggleSelected(!!value)}*/}
                  {/*  checked={row.getIsSelected() || (row.getIsSomeSelected() && 'indeterminate')}*/}
                  {/*  aria-label="Select all"*/}
                  {/*/>*/}
                  <Avatar uid={fb_uid} name={full_name} />
                  {full_name ? (
                    <CustomToolTips
                      element={
                        <Link to={`${ROOT_PATH}/${ROOT_ROUTE.contact.detail}/${original.id}`}>
                          <p className="truncate max-w-[170px] hover:font-medium hover:text-primary-crm">
                            {full_name}
                          </p>
                        </Link>
                      }
                      content={full_name}
                    />
                  ) : (
                    <>-</>
                  )}
                </div>
                <ButtonExpand
                  title={''}
                  row={row}
                  id={original.id.toString()}
                  setIdExpand={setIdExpand}
                  classOpen={'rotate-0'}
                  classClose={'-rotate-90'}
                />
              </div>
            );
          },
          header: () => {
            return (
              <div className="flex gap-3">
                {/*<Checkbox*/}
                {/*  checked={*/}
                {/*    table.getIsAllPageRowsSelected() ||*/}
                {/*    (table.getIsSomePageRowsSelected() && 'indeterminate')*/}
                {/*  }*/}
                {/*  className="bg-white"*/}
                {/*  onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}*/}
                {/*  aria-label="Select all"*/}
                {/*/>*/}
                {t('common.name')}
              </div>
            );
          },
        },
        ...(!isDataSet
          ? [{
            id: n('phone_number'),
            accessorKey: n('phone_number'),
            enableSorting: false,
            size: 200,
            minSize: 150,
            header: () => <p className="w-full">{t('common.phoneNumber')}</p>,
            /* eslint-disable @typescript-eslint/no-explicit-any */
            cell: ({ getValue, row }: any) => {
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-expect-error
              const phoneNumber = getValue<string>();
              if (!phoneNumber) {
                return <></>;
              }
              return (
                <div className="flex items-center gap-4 justify-between">
                  <button
                    onClick={() => handleRemoveReminder(row.original)}
                    className={'flex items-center rounded-lg w-fit min-w-[134px] h-[32px] p-1 gap-1 [&_span]:hover:text-base hover:text-success-hover hover:bg-success hover:cursor-pointer [&_svg]:hover:scale-[120%] [&_svg]:duration-300 [&_span]:duration-300'}
                  >
                    <RiPhoneLine color={'#27923A'} size={20} />
                    <p className="text-sm w-max">{handleFormatPhone(phoneNumber)}</p>
                  </button>
                  {/*<RenderStatus status={status} />*/}
                </div>
              );
            },
          }]
          : []),
        {
          id: toCamelCase(LABEL.segment),
          accessorKey: toCamelCase(LABEL.segment),
          enableSorting: false,
          size: 210,
          minSize: 150,
          header: () => <p className="w-full">{t('common.segment.title')}</p>,
          cell: ({ row }) => {
            const segment = handleMergeArray(row.original.segments);
            if (segment.length === 0) {
              return '-';
            }
            return <SegmentCol segments={segment} />;
          },
        },
        {
          id: n('dob'),
          accessorKey: n('dob'),
          size: 130,
          minSize: 130,
          header: () => <p>{t('common.dob')}</p>,
          enableSorting: true,
          sortUndefined: 'last',
          sortDescFirst: false,
          cell: ({ getValue }) => {
            const dob = getValue<string>();
            return <p className="truncate">{dob ? formatDate(dob, '/') : '-'}</p>;
          },
        },
        {
          id: n('gender'),
          accessorKey: n('gender'),
          size: 100,
          minSize: 100,
          header: () => <p className="w-full">{t('common.gender')}</p>,
          enableSorting: false,
          cell: ({ getValue }) => {
            const genderValue = getValue<string>();
            if (!genderValue) {
              return <>-</>;
            }
            return (
              <div className="w-full flex items-center">
                <RenderGender gender={genderValue} />
              </div>
            );
          },
        },
        {
          id: n('person_address'),
          accessorKey: n('person_address'),
          size: 150,
          minSize: 150,
          enableSorting: false,
          header: () => <p className="w-full">{t('common.address')}</p>,
          cell: ({ getValue }) => {
            const person_address = getValue<string>();
            if (!person_address) {
              return handleCheckEmptyValue(person_address);
            }
            return (
              <CustomToolTips
                element={<p className="truncate">{person_address}</p>}
                content={person_address}
              />
            );
          },
        },
        {
          id: n('person_province'),
          accessorKey: n('person_province'),
          enableSorting: false,
          isResizingColumn: true,
          size: 210,
          minSize: 150,
          header: () => <p className="w-full">{t('common.location')}</p>,
          cell: ({ getValue }) => {
            const person_province = getValue<string>();
            if (!person_province) {
              return handleCheckEmptyValue(person_province);
            }
            return (
              <CustomToolTips
                element={<p className="truncate">{person_province}</p>}
                content={person_province}
              />
            );
          },
        },
        {
          id: n('email'),
          accessorKey: n('email'),
          enableSorting: false,
          size: 210,
          minSize: 150,
          header: () => <p className="w-fit">{t('common.email')}</p>,
          cell: ({ getValue }) => {
            const email = getValue<string>();
            if (!email) {
              return handleCheckEmptyValue(getValue<string>());
            }
            return (
              <CustomToolTips
                element={
                  <div className="flex items-center gap-1 px-2 py-0 cursor-pointer rounded-lg [&_p]:duration-300 [&_p]:hover:text-base hover:bg-secondary-foreground_crm hover:border hover:border-border ">
                    <span>
                      <RiMailLine size={20} color={'#515667'} />
                    </span>
                    <p className="truncate text-sm">{email}</p>
                  </div>
                }
                content={email}
              />
            );
          },
        },

        {
          id: n('company_name'),
          accessorKey: n('company_name'),
          enableSorting: false,
          size: 210,
          minSize: 150,
          header: () => <p className="w-full">{t('common.company')}</p>,
          cell: ({ getValue }) => {
            const companyName = getValue<string>();
            if (!companyName) {
              return handleCheckEmptyValue(companyName);
            }
            return (
              <CustomToolTips
                element={<p className="truncate">{companyName}</p>}
                content={companyName}
              />
            );
          },
        },
        {
          id: n('position'),
          accessorKey: n('position'),
          enableSorting: false,
          size: 210,
          minSize: 150,
          header: () => <p className="w-full">{t('common.position')}</p>,
          cell: ({ getValue }) => {
            const position = getValue<string>();
            if (!position) {
              return handleCheckEmptyValue(position);
            }
            return (
              <CustomToolTips element={<p className="truncate">{position}</p>} content={position} />
            );
          },
        },
        {
          id: n('reminder'),
          accessorKey: 'action',
          // enableSorting: true,
          // sortUndefined: 'last',
          // sortDescFirst: false,
          meta: {
            sticky: 'right',
          },
          size: 70,
          minSize: 70,
          // size: 113,
          // minSize: 113,
          header: '',
          // header: () =>
          //   listRemove.length > 0 ? (
          //     <div
          //       className={`text-right ${
          //         listRemove && listRemove.length > 0
          //           ? '[&_svg]:scale-150 [&_svg]:fill-[#F53E3E]'
          //           : ''
          //       }`}
          //     >
          //
          //         <RenderModalRemoveContact
          //           listRemove={listRemove}
          //           pagination={pagination}
          //           countItems={countItems}
          //           isContactInSegment={isContactInSegment}
          //           refetchContact={refetchContact}
          //           setPagination={setPagination}
          //           setListRemove={setListRemove}
          //           handleResetFilter={handleResetFilter}
          //         />
          //
          //     </div>
          //   ) : (
          //     <></>
          //   ),
          cell: ({ row }) => {
            const { original } = row;
            return (
              <div className="w-full flex gap-3 px-1 justify-center">
                {/*<NoticeReminder handleUpdateReminder={handleUpdateReminder} row={original} />*/}
                <CustomToolTips
                  element={
                    <Modal
                      trigger={
                        <button className="hover:text-primary">
                          <RiEdit2Line size={20} color={'#20232C'} />
                        </button>
                      }
                      isCloseIcon={false}
                      className="w-fit max-w-none"
                      titleAlign={'center'}
                      title={t('contactList.titleEditContact')}
                    >
                      <ContactSubmitForm
                        isCreate={false}
                        detailContact={original}
                        handleSubmitSuccess={handleSubmitSuccess}
                      />
                    </Modal>
                  }
                  content={t('common.button.edit')}
                />
                {/*<CustomToolTips*/}
                {/*  element={*/}
                {/*    <RenderModalRemoveContact*/}
                {/*      listRemove={[original]}*/}
                {/*      pagination={pagination}*/}
                {/*      countItems={countItems}*/}
                {/*      isContactInSegment={isContactInSegment}*/}
                {/*      refetchContact={refetchContact}*/}
                {/*      setPagination={setPagination}*/}
                {/*      setListRemove={setListRemove}*/}
                {/*      handleResetFilter={handleResetFilter}*/}
                {/*    />*/}
                {/*  }*/}
                {/*  content={t('common.button.moveToTrash')}*/}
                {/*/>*/}
              </div>
            );
          },
        },
      ] as ColumnDef<TContactItems>[],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [countItems, dataTable, listRemove, phone],
  );
};

// type TRenderModalRemoveContact = {
//   listRemove: TContactItems[];
//   setListRemove: (value: TContactItems[]) => void;
//   handleResetFilter?: () => void;
//   pagination: TPagination;
//   setPagination: (pagination: TPagination) => void;
//   countItems: number;
//   refetchContact: () => void;
//   isContactInSegment?: boolean;
// };
// const RenderModalRemoveContact = ({ ...props }: TRenderModalRemoveContact) => {
//   const {
//     listRemove,
//     pagination,
//     countItems,
//     isContactInSegment = false,
//     setPagination,
//     setListRemove,
//     handleResetFilter,
//     refetchContact,
//   } = props;
//   return (
//     <ModalRemoveContact
//       setListContactRemove={setListRemove}
//       listContactRemove={listRemove}
//       handleResetFilter={handleResetFilter}
//       pagination={pagination}
//       isContactInSegment={isContactInSegment}
//       setPagination={setPagination}
//       count={countItems}
//       refetchContact={refetchContact}
//     />
//   );
// };
