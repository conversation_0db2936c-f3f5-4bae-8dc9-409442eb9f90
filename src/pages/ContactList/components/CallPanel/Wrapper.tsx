import { cn } from '@/lib/utils';
import { useCallContext } from '@/pages/context/CallContext';
import { RiAspectRatioLine, RiCloseLine, RiSubtractLine } from '@remixicon/react';
import { t } from 'i18next';
import { ReactNode, useState } from 'react';
import CollapsePanel from './CollapsePanel';

type PropsWithChild = {
  children: ReactNode;
};

export const ScriptWrapper = ({
  children,
  contentLeft,
}: PropsWithChild & { contentLeft: ReactNode }) => {
  const [collapsed, setCollapsed] = useState(false);
  const { removeContactCall } = useCallContext();

  const Header = ({
    onMinimized,
    isMinimized,
  }: {
    isMinimized: boolean;
    onMinimized: () => void;
  }) => {
    return (
      <div className="flex w-full items-center justify-between">
        {contentLeft}
        <div className="flex items-center">
          <button onClick={onMinimized} className="p-2">
            {isMinimized ? (
              <RiAspectRatioLine color="#515667" size={24} />
            ) : (
              <RiSubtractLine color="#515667" size={24} />
            )}
          </button>
          <button onClick={removeContactCall} className="p-2">
            <RiCloseLine color="#515667" size={24} />
          </button>
        </div>
      </div>
    );
  };

  if (collapsed)
    return (
      <div className="w-[448px] h-[72px] items-center bg-[#E1E2E3] rounded-2xl p-4 flex flex-col justify-between">
        <Header isMinimized={collapsed} onMinimized={() => setCollapsed(false)} />
      </div>
    );
  return (
    <div className="w-[448px] h-[333px] bg-[#E1E2E3] gap-3 rounded-2xl p-4 flex flex-col justify-between">
      <Header isMinimized={collapsed} onMinimized={() => setCollapsed(true)} />
      {children}
    </div>
  );
};

type ContainerProps = {
  timer: number;
  onCall: () => void;
  onHangUp: () => void;
} & PropsWithChild;

export const ContainerWrapper = ({ children, timer = 0, onCall, onHangUp }: ContainerProps) => {
  const [collapsed, setCollapsed] = useState(false);
  const { removeContactCall } = useCallContext();
  const Header = ({
    onMinimized,
    isMinimized,
  }: {
    isMinimized: boolean;
    onMinimized: () => void;
  }) => {
    return (
      <div
        className={cn('flex w-full items-center justify-end text-secondary', collapsed && 'h-full')}
      >
        {isMinimized && (
          <CollapsePanel
            timer={timer}
            title={t('common.calling') + '...'}
            onHangup={onHangUp!}
            onCall={onCall!}
          />
        )}
        <button className="p-2" onClick={onMinimized}>
          {isMinimized ? (
            <RiAspectRatioLine color="#515667" size={24} />
          ) : (
            <RiSubtractLine color="#515667" size={24} />
          )}
        </button>
        <button className="p-2" onClick={removeContactCall}>
          <RiCloseLine color="#515667" size={24} />
        </button>
      </div>
    );
  };

  return (
    <div
      className={cn(
        'w-[288px] items-center bg-[#14161D] rounded-2xl p-4 flex flex-col gap-y-3',
        collapsed ? 'h-[72px]' : 'h-[333px]',
      )}
    >
      <Header isMinimized={collapsed} onMinimized={() => setCollapsed(!collapsed)} />
      <div
        className={cn('flex-1 flex-col flex justify-between', collapsed && 'h-0 overflow-hidden')}
      >
        {children}
      </div>
    </div>
  );
};
