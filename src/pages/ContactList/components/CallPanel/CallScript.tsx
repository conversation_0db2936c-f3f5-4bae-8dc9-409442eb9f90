import { useCallContext } from '@/pages/context/CallContext';
import { ScriptWrapper } from './Wrapper';
import { useEffect, useState } from 'react';
import { TSegmentDetail } from '@/types/contactList';
import SingleSelect from '@/components/SingleSelect';
import { ScrollArea } from '@/components/ui/scroll-area';
import { t } from 'i18next';
import { useMutation } from '@tanstack/react-query';
import { segmentApi } from '@/apis/segmentApi';
import { RiLoader2Line } from '@remixicon/react';
import ScriptTabs, { TScriptTab } from './ScriptTabs';
import QuickNote from './QuickNote';

const CallScript = () => {
  const { contactCall } = useCallContext();
  const [segment, setSegment] = useState<string | null>(null);
  const [segmentShow, setSegmentShow] = useState<TSegmentDetail>();
  const [tab, setTab] = useState<TScriptTab>('script');

  const mutation = useMutation({
    mutationFn: segmentApi.getDetail,
    onSuccess: (data) => setSegmentShow(data),
    onError: (e) => e,
  });

  useEffect(() => {
    if (!contactCall?.segments || contactCall.segments.length <= 0) return;
    setSegment(contactCall?.segments[0].segment?.id.toString() || null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (!segment) return;
    mutation.mutate(segment);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [segment]);

  return (
    <ScriptWrapper contentLeft={<ScriptTabs tabCurrent={tab} setTab={setTab} />}>
      {tab === 'script' && (
        <>
          <div className="bg-card rounded-2xl">
            <SingleSelect
              options={
                contactCall?.segments?.map((segment) => ({
                  value: segment.segment.id.toString(),
                  label: segment.segment.name,
                })) || []
              }
              defaultValue={segment || ''}
              onChange={setSegment}
              placeholder={t('common.segment.title')}
              className={'z-[999999] text-sm'}
            />
          </div>
          <ScrollArea className="w-full text-start relative text-sm h-[249px] bg-card rounded-2xl">
            {mutation.isPending && (
              <div className="absolute w-full h-full bg-black/20 flex items-center justify-center">
                <RiLoader2Line size={20} className="animate-spin" />
              </div>
            )}
            <div className="w-full p-3">{segmentShow?.script_content || t('common.noScript')}</div>
          </ScrollArea>
        </>
      )}
      {tab === 'note' && (
        <QuickNote
          currentNote={contactCall?.note || { id: 0, note: '', date_created: '' }}
          id_contact={contactCall?.id ?? 0}
        />
      )}
    </ScriptWrapper>
  );
};

export default CallScript;
