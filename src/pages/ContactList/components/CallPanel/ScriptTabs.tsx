import { cn } from '@/lib/utils';
import { RiBookOpenLine, RiFileEditLine } from '@remixicon/react';
import { t } from 'i18next';

export type TScriptTab = 'script' | 'note';

type Props = {
  tabCurrent: TScriptTab;
  setTab: (tab: TScriptTab) => void;
};

const tabs = [
  { label: t('common.callScript'), value: 'script', icon: <RiBookOpenLine size={20} /> },
  { label: t('common.quickNote'), value: 'note', icon: <RiFileEditLine size={20} /> },
];

const ScriptTabs = ({ tabCurrent, setTab }: Props) => {
  return (
    <div className="flex items-center gap-2">
      {tabs.map((tab) => (
        <button
          className={cn(
            'py-1 px-2 rounded-full border bg-secondary flex items-center gap-2 text-sm',
            tab.value === tabCurrent && 'text-brand',
          )}
          key={tab.value}
          onClick={() => setTab(tab.value as TScriptTab)}
        >
          {tab.icon}
          {tab.label}
        </button>
      ))}
    </div>
  );
};

export default ScriptTabs;
