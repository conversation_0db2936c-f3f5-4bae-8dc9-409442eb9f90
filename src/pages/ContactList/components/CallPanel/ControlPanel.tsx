import PhoneOffIcon from '@/assets/PhoneOffIcon';
import { CALL_STATUS, TCallStatus } from '@/constants/call';
import { RiPhoneLine } from '@remixicon/react';
import { t } from 'i18next';

type Props = {
  status: TCallStatus;
  onCall: () => void;
  onHangUp: () => void;
};

const ControlPanel = ({ status, onCall, onHangUp }: Props) => {
  switch (status) {
    case CALL_STATUS.FAIL:
      return (
        <div className="flex items-center flex-col gap-2">
          <button onClick={onCall} className="w-9 h-9 rounded-full bg-green-400 p-2">
            <RiPhoneLine size={20} color="white" />
          </button>
          <span className="text-card">{t('common.button.retry')}</span>
        </div>
      );
    case CALL_STATUS.ANSWERED:
    case CALL_STATUS.RINGING:
      return (
        <div className="flex items-center flex-col gap-2">
          <button onClick={onHangUp} className="w-9 h-9 rounded-full bg-[#F53E3E] p-2">
            <PhoneOffIcon />
          </button>
          <span className="text-card">{t('common.button.decline')}</span>
        </div>
      );
    case CALL_STATUS.HANG_UP:
    case CALL_STATUS.CDR:
      return (
        <div className="flex items-center flex-col gap-2">
          <button onClick={onCall} className="w-9 h-9 rounded-full bg-green-400 p-2">
            <RiPhoneLine size={20} color="white" />
          </button>
          <span className="text-card">{t('common.call')}</span>
        </div>
      );
    default:
      return null;
  }
};

export default ControlPanel;
