import { contactApis } from '@/apis/contact';
import { TNoteContact } from '@/types/contactList';
import { RiCheckLine } from '@remixicon/react';
import { useMutation } from '@tanstack/react-query';
import { t } from 'i18next';
import { useEffect, useState } from 'react';

type Props = {
  currentNote: TNoteContact;
  id_contact: number;
};

const QuickNote = ({ currentNote, id_contact }: Props) => {
  const [note, setNote] = useState<{ value: string; isChange: boolean }>({
    value: '',
    isChange: false,
  });

  const [isSaved, setIsSaved] = useState(false);

  useEffect(() => {
    if (!currentNote) return;
    setNote({ value: currentNote.note, isChange: false });
  }, [currentNote]);

  const updateMutation = useMutation({
    mutationFn: contactApis.updateNote,
    onSuccess: () => {
      setIsSaved(true);
    },
  });
  const createMutation = useMutation({
    mutationFn: contactApis.createNote,
    onSuccess: () => {
      setIsSaved(true);
    },
  });

  useEffect(() => {
    if (!isSaved) return;
    const timeOut = setTimeout(() => {
      setIsSaved(false);
    }, 1500);
    return () => {
      clearTimeout(timeOut);
    };
  }, [isSaved]);

  useEffect(() => {
    if (!note.isChange) return;
    const noteTimeOut = setTimeout(() => {
      if (currentNote.id) {
        updateMutation.mutate({
          note: note.value,
          contact_id: id_contact.toString(),
          id: currentNote.id.toString(),
        });
      } else {
        createMutation.mutate({
          note: note.value,
          contact_id: id_contact.toString(),
        });
      }
    }, 1500);
    return () => {
      clearTimeout(noteTimeOut);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [note, currentNote]);

  const handleOnChange = (value: string) => {
    setNote({ value, isChange: true });
  };

  return (
    <div className="relative">
      <textarea
        onChange={(e) => handleOnChange(e.target.value)}
        value={note.value}
        placeholder={t('common.takeNote') + '...'}
        rows={10}
        className="rounded-2xl w-full h-full resize-none text-sm text-tertiary bg-card p-3 overflow-x-hidden outline-none"
      />
      <div className="absolute bottom-2 right-2">
        {isSaved && (
          <div className="absolute bottom-2 right-2 text-xs text-green-500 flex items-center">
            <RiCheckLine size={16} />
            {t('common.saved')}
          </div>
        )}
      </div>
    </div>
  );
};

export default QuickNote;
