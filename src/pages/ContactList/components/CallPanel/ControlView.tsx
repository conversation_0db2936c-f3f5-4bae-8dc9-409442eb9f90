import { CALL_STATUS, TCallStatus } from '@/constants/call';
import { fTimeHH_MM_SS } from '@/utils/helper';
import { t } from 'i18next';

type Props = {
  status: TCallStatus;
  time: number;
};

const ControlView = ({ status, time }: Props) => {
  switch (status) {
    case CALL_STATUS.FAIL:
      return <span className="text-card mt-4">{t('common.failToConnectHotline')}</span>;
    case CALL_STATUS.RINGING:
      return <span className="text-card text-xl mt-4">{t('common.calling') + '...'}</span>;
    case CALL_STATUS.HANG_UP:
      return <span className="text-lg font-medium text-white">{fTimeHH_MM_SS(time)}</span>;
    case CALL_STATUS.ANSWERED:
      return (
        <>
          <span className="text-card text-xl leading-8 tracking-[0.6px] h-[15px]">
            {fTimeHH_MM_SS(time)}
          </span>
          <span className="text-xs text-tertiary">{t('common.recording') + '...'}</span>
        </>
      );
    case CALL_STATUS.CDR:
    default:
      return null;
  }
};
export default ControlView;
