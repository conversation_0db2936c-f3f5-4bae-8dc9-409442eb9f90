import { TUseCalling } from '@/hooks/useCalling';
import CallContainer from './CallContainer';
import CallScript from './CallScript';
import { TCallStatus } from '@/constants/call';

type Props = {
  message: TUseCalling;
  onHangUp: () => void;
  onCall: () => void;
  status: TCallStatus;
};

const CallPanel = ({ message, onCall, onHangUp, status }: Props) => {
  return (
    <div className="flex items-end gap-6 fixed bottom-4 z-[9999] right-4">
      <CallScript />
      <CallContainer status={status} message={message} onHangUp={onHangUp} onCall={onCall} />
    </div>
  );
};
export default CallPanel;
