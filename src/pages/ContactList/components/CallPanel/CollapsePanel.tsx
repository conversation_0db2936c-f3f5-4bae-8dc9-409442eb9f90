import { CALL_STATUS } from '@/constants/call';
import { useCallContext } from '@/pages/context/CallContext';
import { fTimeHH_MM_SS } from '@/utils/helper';
import { RiPhoneLine } from '@remixicon/react';

type Props = {
  timer: number;
  title: string;
  onHangup: () => void;
  onCall: () => void;
};

const CollapsePanel = ({ onCall, onHangup, timer, title }: Props) => {
  const { message } = useCallContext();

  const CallIcon = () => {
    switch (message.status) {
      case CALL_STATUS.CDR:
        return (
          <button
            onClick={onCall}
            className="ml-auto hover:bg-tertiary-foreground p-2 rounded-full"
          >
            <RiPhoneLine className="text-green-500" />
          </button>
        );

      case CALL_STATUS.RINGING:
      case CALL_STATUS.ANSWERED:
        return (
          <button
            onClick={onHangup}
            className="ml-auto hover:bg-tertiary-foreground p-2 rounded-full"
          >
            <RiPhoneLine className="text-red-500" />
          </button>
        );
      case CALL_STATUS.HANG_UP:
      default:
        return null;
    }
  };

  return (
    <div className="flex text-secondary-foreground_crm flex-1 items-center gap-1">
      <span className="text-tertiary">{title}</span>
      {message.status === CALL_STATUS.ANSWERED && (
        <span className="font-semibold">{fTimeHH_MM_SS(timer)}</span>
      )}
      <CallIcon />
    </div>
  );
};
export default CollapsePanel;
