import { useCallContext } from '@/pages/context/CallContext';
import { ContainerWrapper } from './Wrapper';
import ControlView from './ControlView';
import ControlPanel from './ControlPanel';
import { TUseCalling } from '@/hooks/useCalling';
import { useEffect, useState } from 'react';
import { CALL_STATUS, TCallStatus } from '@/constants/call';
import { getSelector, useAppSelector } from '@/hooks/reduxHooks';
import { handleFormatPhone } from '@/utils/helper';

type Props = {
  message: TUseCalling;
  onHangUp: () => void;
  onCall: () => void;
  status: TCallStatus;
};

const CallContainer = ({ onCall, onHangUp, status }: Props) => {
  const { contactCall } = useCallContext();
  const [countUp, setCountUp] = useState(0);
  const { phone } = useAppSelector(getSelector('used_phone'));

  useEffect(() => {
    if (phone.total_call_today >= 10) {
      setCountUp(0);
      return;
    } else {
      switch (status) {
        case CALL_STATUS.RINGING:
          setCountUp(0);
          break;
        case CALL_STATUS.ANSWERED:
          setCountUp(0);
          const timeOut = phone.time_limit * 1000;

          const intervalId = setInterval(() => {
            setCountUp((prev) => prev + 1);
          }, 1000);

          const timeoutId = setTimeout(() => {
            clearInterval(intervalId);
            onHangUp();
          }, timeOut);

          return () => {
            clearInterval(intervalId);
            clearTimeout(timeoutId);
          };
        case CALL_STATUS.CDR:
        case CALL_STATUS.HANG_UP:
        case CALL_STATUS.FAIL:
        default:
          break;
      }

      if (status === CALL_STATUS.CDR) {
        setCountUp(0);
        return;
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [status]);

  return (
    <ContainerWrapper timer={countUp} onCall={onCall} onHangUp={onHangUp}>
      <div className="flex flex-col gap-y-4 items-center justify-center">
        <div className="flex flex-col gap-y-2 items-center">
          <img
            src="https://github.com/shadcn.png"
            alt="avatar"
            className="w-12 h-12 rounded-full"
          />
          <p className="text-sm text-tertiary">{contactCall?.full_name}</p>
          <p className="text-xl text-secondary-foreground_crm font-semibold">
            {handleFormatPhone(contactCall?.phone_number ?? '')}
          </p>
        </div>
        <ControlView time={countUp} status={status} />
      </div>
      <ControlPanel status={status} onCall={onCall} onHangUp={onHangUp} />
    </ContainerWrapper>
  );
};
export default CallContainer;
