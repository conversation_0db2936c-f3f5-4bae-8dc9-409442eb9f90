import { CsvIcon } from '@/assets/CsvIcon';
import { fSize } from '@/utils/number';
import { RiCloseFill } from '@remixicon/react';

type Props = {
  file: File;
  onRemove: () => void;
};

const FileImported = ({ file, onRemove }: Props) => {
  return (
    <div className="flex items-center gap-1 bg-secondary-foreground_crm p-2 rounded-md">
      <CsvIcon />
      <div className="flex flex-col">
        <span className="text-sm font-medium">{file.name}</span>
        <span className="text-secondary text-xs">{fSize(file.size)}</span>
      </div>
      <button onClick={onRemove} className="ml-auto">
        <RiCloseFill />
      </button>
    </div>
  );
};
export default FileImported;
