import { UploadIcon } from '@/assets/UploadIcon';
import { cn } from '@/lib/utils';
import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

type Props = {
  setFile: (file: File | null) => void;
  file: File | null;
};

const UploadContainer = ({ file, setFile }: Props) => {
  const { t } = useTranslation();
  const inputRef = useRef<HTMLInputElement>(null);
  const [dragOver, setDragOver] = useState(false);

  useEffect(() => {
    if (!file && inputRef.current) {
      inputRef.current.value = '';
    }
  }, [file]);

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOver(false);

    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length > 0) {
      setFile(droppedFiles[0]);
    }
  };

  const handleOnChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (selectedFiles && selectedFiles.length > 0) {
      setFile(selectedFiles[0]);
    }
  };

  const onClick = () => {
    inputRef.current?.click();
  };

  return (
    <div
      onClick={onClick}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      className={cn(
        'rounded-md cursor-pointer p-6 bg-secondary-foreground_crm flex flex-col items-center gap-2',
        dragOver && 'bg-brand-foreground',
        'hover:bg-tertiary',
      )}
    >
      <UploadIcon />
      <span className="text-sm">
        {t('contactList.dropFile')}{' '}
        <span className="text-brand underline">{t('contactList.browse')}</span>{' '}
        {t('contactList.fromComputer')}
      </span>
      <input
        type="file"
        className="hidden"
        ref={inputRef}
        onChange={handleOnChange}
        accept=".csv, text/csv, application/vnd.ms-excel, .xls, .xlsx"
      />
    </div>
  );
};

export default UploadContainer;
