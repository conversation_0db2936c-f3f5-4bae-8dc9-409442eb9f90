import { t } from 'i18next';

type Step = 'step_1' | 'step_2' | 'step_3';

type Props = {
  step: Step;
};

const UploadStep = ({ step }: Props) => {
  const obj = (() => {
    switch (step) {
      case 'step_1':
        return {
          title: t('contactList.uploadSteps.step1.title'),
          description: t('contactList.uploadSteps.step1.description'),
        };
      case 'step_2':
        return {
          title: t('contactList.uploadSteps.step2.title'),
          description: t('contactList.uploadSteps.step2.description'),
        };
      case 'step_3':
        return {
          title: t('contactList.uploadSteps.step3.title'),
          description: t('contactList.uploadSteps.step3.description'),
        };
    }
  })();

  return (
    <div className="flex flex-col items-start gap-1 text-sm">
      <span className="font-semibold">{obj.title}</span>
      <span>{obj.description}</span>
    </div>
  );
};

export default UploadStep;
