import { Button } from '@/components/ui/button';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import UploadContainer from './UploadContainer';
import FileImported from './FileImported';
import SelectColFile from './SelectColFile';
import { checkIfExcelFile, exportToExcel } from '@/utils/xlsx';
import { contactData } from '@/constants/contact-list/colums';

type View = 'import' | 'upload';

type Props = {
  setIsOpen: (open: boolean) => void;
};

const UploadPopup = ({ setIsOpen }: Props) => {
  const { t } = useTranslation();
  const [view, setView] = useState<View>('import');
  const [file, setFile] = useState<File | null>(null);

  const toggleView = () => setView((prev) => (prev === 'import' ? 'upload' : 'import'));
  const handleUpload = () => {
    if (!file) return;
    toggleView();
  };
  const handleOnRemove = () => {
    setFile(null);
  };

  if (view === 'upload' && file && checkIfExcelFile(file)) {
    return <SelectColFile onClose={() => setIsOpen(false)} file={file} />;
  }

  return (
    <div className="flex flex-col gap-3">
      <UploadContainer setFile={setFile} file={file} />
      {file && <FileImported onRemove={handleOnRemove} file={file} />}
      <div className="flex items-center gap-3">
        <Button
          disabled={!checkIfExcelFile(file!)}
          onClick={handleUpload}
          size={'lg'}
          className="flex-1 rounded-xl hover:bg-brand"
        >
          {t('contactList.upload')}
        </Button>
        <Button
          onClick={() => exportToExcel(contactData, 'contact-list-template')}
          size={'lg'}
          variant={'primary'}
          className="max-w-[199px] w-full rounded-xl"
        >
          {t('contactList.downloadTemplate')}
        </Button>
      </div>
    </div>
  );
};

export default UploadPopup;
