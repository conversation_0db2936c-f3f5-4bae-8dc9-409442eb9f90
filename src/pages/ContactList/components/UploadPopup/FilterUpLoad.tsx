import SingleSelect from '@/components/SingleSelect';
import { CONTACT_FIELD, LABEL_CONTACT_FIELD } from '@/constants/contact-list';
import { useEffect, useState } from 'react';

type Props = {
  keys: string[];
  setFilterValue: (filter: { [key: string]: string }) => void;
};

const FilterUpLoad = ({ keys, setFilterValue }: Props) => {
  const [checkError, setCheckError] = useState<{ key: string; error: boolean }[]>([]);

  useEffect(() => {
    setCheckError(
      Object.values(CONTACT_FIELD).map((key) => {
        if (key === CONTACT_FIELD.ID) {
          return { key, error: false };
        }
        return { key, error: !keys.includes(key) };
      }),
    );
  }, [keys]);

  const handleOnChange = (selected: string, key: string) => {
    setFilterValue({ [key]: selected });
    setCheckError((prev) => prev.map((item) => (item.key === key ? { key, error: false } : item)));
  };

  return (
    <div className="grid grid-cols-6 gap-4 rounded-md">
      {Object.values(CONTACT_FIELD).map(
        (item, i) =>
          item !== CONTACT_FIELD.ID && (
            <SingleSelect
              key={i}
              placeholder={LABEL_CONTACT_FIELD[item]}
              options={keys?.map((col) => ({ label: col, value: col })) ?? []}
              onChange={(selected) => handleOnChange(selected, item)}
              isError={checkError.find((error) => error.key === item)?.error}
              defaultValue={keys.includes(item) ? item : undefined}
            />
          ),
      )}
    </div>
  );
};
export default FilterUpLoad;
