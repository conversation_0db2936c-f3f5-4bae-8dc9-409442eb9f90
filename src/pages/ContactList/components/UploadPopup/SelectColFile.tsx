import { Button } from '@/components/ui/button';
import { DialogContent } from '@/components/ui/dialog';
import { RiLoader2Line } from '@remixicon/react';
import { useEffect, useReducer, useState } from 'react';
import { useTranslation } from 'react-i18next';
import uploadDataCols from '../columns/UploadDataCols';
import FilterUpLoad from './FilterUpLoad';
import useUploadXlsx from '@/hooks/useUploadXlsx';
import { CONTACT_FIELD } from '@/constants/contact-list';
import { useMutation } from '@tanstack/react-query';
import { contactApis } from '@/apis/contact';
import { toast } from '@/hooks/use-toast';
import { TDataUpload, TUpdateValue, uploadReducer } from '@/hooks/uploadReducer';
import { fDataUpload } from '@/utils/upload';
import { UploadTable } from '../TableUpload';
import { cn } from '@/lib/utils';
import SegmentSelector from '@/components/SegmentSelector';
import UploadStep from './UploadStep';
import useValidateUploadContact from '@/hooks/useValidateUploadContact';
import { useAppDispatch } from '@/hooks/reduxHooks';
import { setRefresh } from '@/store/contactList/contactListSlice';

type Props = {
  onClose: () => void;
  file: File;
};

const SelectColFile = ({ onClose, file }: Props) => {
  const { t } = useTranslation();
  const { keys, data } = useUploadXlsx(file);
  const [filterValue, setFilterValue] = useState<{ [key: string]: string }>({});
  const [segmentId, setSegmentId] = useState<string>('');
  const [state, dispatch] = useReducer(uploadReducer, {
    dataFile: [],
  });
  const [hoveredRow, setHoveredRow] = useState<TDataUpload | null>(null);
  const isUploadErr = useValidateUploadContact(state.dataFile, segmentId);
  const appDispatch = useAppDispatch();

  useEffect(() => {
    dispatch({ type: 'IMPORT_FILE', payload: data });
  }, [data]);

  useEffect(() => {
    dispatch({
      type: 'UPDATE_DATA',
      payload: { initData: data, filter: filterValue },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterValue]);

  const handleFilterError = (keyFilter: CONTACT_FIELD) => {
    dispatch({ type: 'FILTER_ERROR', payload: { keyFilter } });
  };

  const mutation = useMutation({
    mutationFn: contactApis.uploadContact,
    onSuccess: () => {
      toast({ description: t('contactList.uploadSuccess'), status: 'success' });
      appDispatch(setRefresh());
      setSegmentId('');
      onClose();
    },
    onError: (e) => e,
  });

  const handleUpload = (payload: TUpdateValue) => {
    dispatch({ type: 'UPDATE_VALUE', payload });
  };

  const handleApply = async () => {
    if (isUploadErr) return;
    const payloadExcel = fDataUpload(state.dataFile);
    mutation.mutate({ data: payloadExcel, segment: segmentId });
  };

  const onDeleteRow = (idDel: string) => {
    dispatch({ type: 'DELETE_ROW', payload: { idDel } });
  };

  return (
    <DialogContent
      isOverLayer={false}
      isCloseIcon={false}
      className="max-w-[1156px] max-h-[674px] overflow-x-hidden p-4 bg-card gap-6 grid grid-cols-1"
    >
      <div className={cn('gap-3 rounded-md bg-card flex flex-col')}>
        <UploadStep step="step_1" />
        <FilterUpLoad keys={keys} setFilterValue={setFilterValue} />
      </div>
      <Wrapper>
        <UploadStep step="step_2" />
        <UploadTable
          getItemHover={setHoveredRow}
          data={state.dataFile}
          columns={uploadDataCols({
            handleUpload,
            onFilterError: handleFilterError,
            itemHover: hoveredRow,
            onDeleteRow,
          })}
        />
      </Wrapper>
      <Wrapper>
        <UploadStep step="step_3" />
        <div className="flex items-center gap-3">
          <SegmentSelector currentSegment={segmentId} onChange={setSegmentId} />
          <div className="flex gap-[7px] ml-auto">
            <Button
              onClick={onClose}
              size={'lg'}
              className="bg-secondary-foreground_crm text-primary rounded-xl w-[166px]"
            >
              {t('common.button.cancel')}
            </Button>
            <Button
              disabled={isUploadErr || mutation.isPending}
              onClick={handleApply}
              size={'lg'}
              className="rounded-xl w-[166px]"
              variant={isUploadErr ? 'primary' : 'default'}
            >
              {mutation.isPending ? (
                <RiLoader2Line className="animate-spin" />
              ) : (
                t('common.button.apply')
              )}
            </Button>
          </div>
        </div>
      </Wrapper>
    </DialogContent>
  );
};
export default SelectColFile;

const Wrapper = ({ children }: { children: React.ReactNode }) => (
  <div className="flex flex-col gap-3">{children}</div>
);
