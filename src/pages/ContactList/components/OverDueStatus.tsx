import CustomToolTips from '@/components/CustomToolTips';
import { CONTACT_STATUS } from '@/constants/contact-list';
import { useAppDispatch } from '@/hooks/reduxHooks';
import { getPhoneBought } from '@/store/phoneBought/action';
import { updateStatus } from '@/store/phoneBought/phoneBoughtSlice';
import { removePhone } from '@/store/usedPhone/usedPhoneSlice';
import { TPhoneBought } from '@/types/contactList';
import { fDiffMinutes } from '@/utils/helper';
import { RiErrorWarningLine } from '@remixicon/react';
import { Row } from '@tanstack/react-table';
import { t } from 'i18next';
import { useEffect, useState } from 'react';

type Props = {
  row: Row<TPhoneBought>;
};

const OverDueStatus = ({ row }: Props) => {
  const [status, setStatus] = useState<'nor' | 'warn' | 'danger'>('nor');
  const dispatch = useAppDispatch();

  useEffect(() => {
    const fetchState = () => {
      const diffMinutes = fDiffMinutes(row.original.date_expired);

      if (
        row.original.status === CONTACT_STATUS.EXPIRED ||
        row.original.status === CONTACT_STATUS.SPAM ||
        row.original.is_renewal
      ) {
        return;
      }

      if (diffMinutes < 0 && diffMinutes >= -180) {
        dispatch(updateStatus({ id: row.original.id, status: CONTACT_STATUS.EXPIRED }));
        dispatch(removePhone());
        dispatch(getPhoneBought());
      }
    };

    const interval = setInterval(fetchState, 60000); // mỗi phút
    return () => clearInterval(interval);
  }, [dispatch, row]);

  useEffect(() => {
    if (row.original.status === CONTACT_STATUS.SPAM) return;

    const fetchStatus = () => {
      const diffMinutes = fDiffMinutes(row.original.date_expired);
      let newStatus: 'nor' | 'warn' | 'danger' = 'nor';

      if (diffMinutes >= 0 && diffMinutes <= 60) {
        newStatus = 'warn';
      }
      if (diffMinutes >= 0 && diffMinutes <= 30) {
        newStatus = 'danger';
      }
      if (diffMinutes < 0 && !row.original.is_renewal) {
        newStatus = 'danger';
      }

      setStatus(newStatus);
    };

    const interval = setInterval(fetchStatus, 60000); // mỗi phút
    return () => clearInterval(interval);
  }, [row]);

  if (status === 'warn') {
    return (
      <CustomToolTips
        content={t('contactList.dateExpiredWarn')}
        element={<RiErrorWarningLine className="text-[#FBCA24]" size={20} />}
      />
    );
  }

  if (status === 'danger') {
    return (
      <CustomToolTips
        content={t('contactList.dateExpiredDanger')}
        element={<RiErrorWarningLine className="text-[#F53E3E]" size={20} />}
      />
    );
  }

  return null;
};

export default OverDueStatus;
