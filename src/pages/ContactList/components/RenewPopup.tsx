import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, Di<PERSON>Footer, DialogTrigger } from '@/components/ui/dialog';
import { RiErrorWarningLine } from '@remixicon/react';
import { t } from 'i18next';
import { useState } from 'react';

type Props = {
  handleRenew: () => void;
};

const RenewPopup = ({ handleRenew }: Props) => {
  const [openModal, setOpenModal] = useState<boolean>(false);

  return (
    <Dialog open={openModal} onOpenChange={setOpenModal}>
      <DialogTrigger asChild>
        <Button
          variant={'primary'}
          size={'sm'}
          className="rounded-xl flex-shrink-0 w-[132px] border flex items-center justify-center font-medium"
        >
          {t('common.button.reNew')}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[529px] flex flex-col items-center gap-0">
        <RiErrorWarningLine className="text-destructive" size={80} />
        <div className="text-lg font-medium">{t('common.button.reNew')} </div>
        <div className="text-center text-sm mt-2 text-secondary">
          {t('common.contactList.subCancelRenew')}
        </div>
        <DialogFooter className="grid mt-6 grid-cols-2 w-full">
          <Button onClick={() => setOpenModal(!openModal)} variant={'outline'} type="button">
            {t('common.button.cancel')}
          </Button>
          <Button onClick={handleRenew} variant={'delete'} type="button">
            {t('common.button.reNew')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
export default RenewPopup;
