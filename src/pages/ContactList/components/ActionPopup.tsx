import Modal from '@/components/Modal';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

import { TPhoneBought } from '@/types/contactList';
import { RiFlag2Line, RiMore2Fill } from '@remixicon/react';
import { t } from 'i18next';
import ReportModal from './ReportModal';

type Props = {
  phone: TPhoneBought;
};

const ActionPopup = ({ phone }: Props) => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          className="bg-secondary-foreground_crm w-8 rounded-full flex-shrink-0 border-none py-3 px-2"
          variant={'outline'}
          size={'sm'}
        >
          <RiMore2Fill size={20} />
        </Button>
      </PopoverTrigger>
      <PopoverContent align="end" className="w-[171px] p-3 rounded-xl flex flex-col gap-1">
        <Modal
          className="max-w-[529px]"
          trigger={
            <Button
              className="justify-start p-3 rounded-xl text-sm font-medium gap-1 text-secondary"
              variant={'ghost'}
            >
              <RiFlag2Line size={20} />
              {t('common.button.remove')}
            </Button>
          }
        >
          <ReportModal phone={phone} />
        </Modal>
        {/*<CancelRenewPopup phone={phone} />*/}
      </PopoverContent>
    </Popover>
  );
};
export default ActionPopup;
