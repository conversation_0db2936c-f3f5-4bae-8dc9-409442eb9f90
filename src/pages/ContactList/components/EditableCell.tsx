import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CONTACT_FIELD, genderOptions } from '@/constants/contact-list';
import { TDataUpload, TUpdateValue } from '@/hooks/uploadReducer';
import { cn } from '@/lib/utils';
import { Row } from '@tanstack/react-table';
import { useState } from 'react';
import InputEditer from './InputEditer';
import { Calendar } from '@/components/ui/calendar';
import { formatDate } from '@/utils/helper';
import SingleSelect from '@/components/SingleSelect';
import { RiCalendar2Line, RiEditLine } from '@remixicon/react';

type Props = {
  row: Row<TDataUpload>;
  keyValue: CONTACT_FIELD;
  handleUpload: (payload: TUpdateValue) => void;
};

const EditableCell = ({ row, keyValue, handleUpload }: Props) => {
  const [edit, setEdit] = useState<string>(
    typeof row.original[keyValue] === 'string' ? row.original[keyValue] : '',
  );
  const [isOpen, setIsOpen] = useState(false);

  const handleOnChange = (value: string) => {
    setEdit(value);
    handleUpload({ id: Number(row.original.id), keyValue, value });
  };

  const editContainer = () => {
    switch (keyValue) {
      case CONTACT_FIELD.Gender:
        return (
          <SingleSelect
            defaultValue={edit || ''}
            options={genderOptions}
            onChange={handleOnChange}
          />
        );
      case CONTACT_FIELD.DOB:
        const disabledPastDate = (date: Date) => {
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          return date > today;
        };
        return (
          <Calendar
            disabled={disabledPastDate}
            mode="single"
            selected={edit ? new Date(edit) : undefined}
            onSelect={(value) => handleOnChange(formatDate(value?.toDateString() || '', '-'))}
          />
        );
      default:
        return <InputEditer value={edit} onChange={handleOnChange} />;
    }
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <div
          className={cn(
            'cursor-pointer items-center gap-1 whitespace-nowrap line-clamp-1 flex',
            Array.isArray(row.original.error) &&
              row.original.error.includes(keyValue) &&
              'text-red-500',
            (keyValue === CONTACT_FIELD.PhoneNumber || keyValue === CONTACT_FIELD.Name) &&
              !row.original[keyValue] &&
              'text-red-500',
          )}
        >
          {keyValue === CONTACT_FIELD.DOB && <RiCalendar2Line className="w-4 h-4 flex-shrink-0" />}
          <span
            className={cn(
              'flex items-center gap-1',
              keyValue === CONTACT_FIELD.Gender && 'capitalize',
            )}
          >
            {typeof row.original[keyValue] === 'string' && row.original[keyValue]}
            {(row.original.error.includes(keyValue) || !row.original[keyValue]) && (
              <div className="p-1.5">
                <RiEditLine size={20} className="flex-shrink-0" />
              </div>
            )}
          </span>
        </div>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-[302px] p-2">
        <div className="grid gap-1">{editContainer()}</div>
      </PopoverContent>
    </Popover>
  );
};
export default EditableCell;
