import { useEffect, useRef, useState } from 'react';

type Props = {
  value: string;
  onChange: (value: string) => void;
};

const InputEditer = ({ value, onChange }: Props) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [edit, setEdit] = useState<string>(value);

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Enter') {
      onChange(edit.trim());
    }
  };

  useEffect(() => {
    const input = inputRef.current;

    if (input) {
      input.addEventListener('keydown', handleKeyDown);

      return () => {
        input.removeEventListener('keydown', handleKeyDown);
      };
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [edit]);

  return (
    <input
      ref={inputRef}
      className="outline-none border rounded-lg px-2 py-1 text-sm"
      value={edit}
      onChange={(e) => setEdit(e.target.value)}
    />
  );
};
export default InputEditer;
