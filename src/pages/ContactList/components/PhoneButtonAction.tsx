import { Button } from '@/components/ui/button';
import { CONTACT_STATUS } from '@/constants/contact-list';
import { getSelector, useAppSelector } from '@/hooks/reduxHooks';
import { TPhoneBought } from '@/types/contactList';
import { Row } from '@tanstack/react-table';
import { t } from 'i18next';
import RenewPopup from './RenewPopup';

type Props = {
  row: Row<TPhoneBought>;
  handleRenew: () => void;
  handleSelectPhone: () => void;
  isDisabled?: boolean;
};
const PhoneButtonAction = ({ row, handleRenew, handleSelectPhone, isDisabled }: Props) => {
  const { phone } = useAppSelector(getSelector('used_phone'));
  const checked = row.original.phone_number === phone.using_hotline;
  switch (row.original.status) {
    case CONTACT_STATUS.USED:
    case CONTACT_STATUS.AVAILABLE:
      return (
        <Button
          onClick={handleSelectPhone}
          size={'sm'}
          variant={checked ? 'default' : 'outline'}
          className="rounded-xl flex-shrink-0 w-[132px] border flex items-center justify-center font-medium"
          disabled={isDisabled}
        >
          {checked ? t('common.button.current') : t('common.button.select')}
        </Button>
      );
    case CONTACT_STATUS.EXPIRED:
      return <RenewPopup handleRenew={handleRenew} />;
    case CONTACT_STATUS.SPAM:
      return (
        <Button
          size={'sm'}
          disabled={true}
          variant={'outline'}
          className="rounded-xl flex-shrink-0 w-[132px] border flex items-center justify-center font-medium"
        >
          {t('common.button.select')}
        </Button>
      );
    default:
      return null;
  }
};

export default PhoneButtonAction;
