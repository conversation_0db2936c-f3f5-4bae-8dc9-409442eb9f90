import { ErrorIcon } from '@/assets/ErrorIcon';
import { Button } from '@/components/ui/button';
import { DialogClose } from '@/components/ui/dialog';
import { CONTACT_LABEL } from '@/constants/contact-list/label';
import { useAppDispatch } from '@/hooks/reduxHooks';
import { getPhoneBought, removePhoneBought } from '@/store/phoneBought/action';
import { TPhoneBought } from '@/types/contactList';
import { t } from 'i18next';
import { resetUsedPhone } from '@/store/usedPhone/usedPhoneSlice';

type Props = {
  phone: TPhoneBought;
};
const ReportModal = ({ phone }: Props) => {
  const dispatch = useAppDispatch();
  const handleReport = () => {
    dispatch(removePhoneBought({ id: phone.id })).unwrap().then(()=>{
      dispatch(getPhoneBought());
      dispatch(resetUsedPhone());
    });
  };
  return (
    <div className="flex flex-col items-center justify-center gap-4">
      <ErrorIcon />
      <div className="flex flex-col gap-3 text-center w-3/4">
        <span className="text-xl leading-8 tracking-[0.6px]">{CONTACT_LABEL.remove_title}</span>
        <span className="text-sm text-secondary">{CONTACT_LABEL.remove_description}</span>
      </div>
      <div className="grid grid-cols-2 gap-4 w-full">
        <DialogClose className="flex-1 bg-secondary-foreground_crm rounded-md">
          {t('common.button.cancel')}
        </DialogClose>
        <Button
          className="rounded-md bg-[#F53E3E] text-secondary-foreground_crm"
          onClick={handleReport}
          variant={'ghost'}
        >
          {t('common.button.remove')}
        </Button>
      </div>
    </div>
  );
};
export default ReportModal;
