import { useAppSelector } from '@/hooks/reduxHooks';
import { t } from 'i18next';

type Props = {
  totalCredit: string;
};

const UnlockCallFeature = ({ totalCredit }: Props) => {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  //@ts-expect-error
  const { user } = useAppSelector((state) => state.auth);

  return (
    <div className="bg-brand-foreground rounded-2xl py-2 px-4 flex justify-between h-[72px]">
      <div className="font-medium">{t('contactList.unlockCall')}</div>
      <div className="flex flex-col gap-1">
        <Label className="text-destructive" title={t('contactList.credit')} value={totalCredit} />
        <Label className="text-[#27923A]" title={t('contactList.yourCredit')} value={user.credit} />
      </div>
    </div>
  );
};
export default UnlockCallFeature;

const Label = ({
  title,
  value,
  className,
}: {
  title: string;
  value: string;
  className?: string;
}) => (
  <div className="flex items-center gap-1 font-medium justify-end">
    <div>{title}:</div>
    <div className={className}>{value}</div>
  </div>
);
