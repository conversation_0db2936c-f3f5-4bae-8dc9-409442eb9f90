import { useEffect, useState } from 'react';

import { phoneApis } from '@/apis/phoneApis';
import DataTable from '@/components/table/DataTable';
import { Button } from '@/components/ui/button';
import { LABEL } from '@/constants/label';
import { toast } from '@/hooks/use-toast';
import { TPhoneBought } from '@/types/contactList';
import { RiInformation2Fill, RiLoader2Line } from '@remixicon/react';
import { getPhonesColumn } from '../columns/GetPhoneColumn';
import FilterOptions from './FilterOptions';
// import UnlockCallFeature from '../UnlockCallFeature';
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks';
import { Box } from '@/components/Box';
import { useTranslation } from 'react-i18next';
import { ConfirmRental } from '@/pages/ContactList/components/GetPhone/ConfirmRental';
import Modal from '@/components/Modal';
import { choosePhone, getUsedPhone } from '@/store/usedPhone/usedPhoneSlice';
import { TableCell, TableRow } from '@/components/ui/table';
import { setOpenRentPhone } from '@/store/phoneBought/phoneBoughtSlice';
import { TPagination } from '@/types/table';
import { NoData } from '@/components/NoData';

type Props = {
  onCloseModal: () => void;
};

const GetPhoneContainer = ({ onCloseModal }: Props) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [pagination, setPagination] = useState<TPagination>({
    currentPage: 1,
    pageSize: 10,
  });
  const [listPhones, setListPhones] = useState<{
    count: number;
    items: TPhoneBought[];
    loading: boolean;
  }>({
    count: 0,
    items: [],
    loading: false,
  });

  const dispatch = useAppDispatch();
  const [itemsSelected, setItemsSelected] = useState<TPhoneBought[]>([]);
  const [filter, setFilter] = useState<{ [key: string]: unknown }>({});
  const [openModalRental, setOpenModalRental] = useState<boolean>(false);
  const { t } = useTranslation();

  const { items } = useAppSelector((state) => state.phone_bought);

  useEffect(() => {
    const fetchPhone = async () => {
      setListPhones({ ...listPhones, loading: true });
      try {
        const newParams = Object.entries(filter).reduce(
          (acc: Record<string, unknown>, [key, value]) => {
            if (value) {
              acc[key] = value;
            }
            return acc;
          },
          {},
        );
        const res = await phoneApis.getPhones({
          ...newParams,
          status__in: 'available',
          page: pagination.currentPage,
          limit: pagination.pageSize,
        });
        setListPhones({ ...res, loading: false });
      } catch (error) {
        setListPhones({ ...listPhones, loading: false });
        return error;
      }
    };
    fetchPhone();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filter, pagination]);

  const handleBuy = async () => {
    setLoading(true);
    if (itemsSelected.length === 0) {
      return;
    }
    await phoneApis
      .buyPhones(itemsSelected)
      .then(() => {
        dispatch(choosePhone(itemsSelected[0].id))
          .unwrap()
          .then(() => {
            dispatch(getUsedPhone()).unwrap();
          });
        dispatch(setOpenRentPhone(false));
        toast({
          status: 'success',
          description: 'Buy phone successfully',
        });
      })
      .finally(() => {
        setLoading(false);
        setOpenModalRental(false);
      });
    onCloseModal();
  };

  return (
    <>
      <FilterOptions params={filter} setParams={setFilter} />
      <Box className="bg-[#FFF5D6] rounded-2xl p-3 gap-2 items-start justify-start">
        <RiInformation2Fill color="#BB8700" className="w-5 h-5" />
        <div className="text-[#BB8700] text-sm">
          <p className="font-medium">{t('getPhoneNumber.rentPhoneTitle')}</p>
          <ul className="list-disc pl-6 mt-1">
            <li>{t('getPhoneNumber.rentPhoneItem1')}</li>
            <li>{t('getPhoneNumber.rentPhoneItem2')}</li>
            <li>{t('getPhoneNumber.rentPhoneItem3')}</li>
          </ul>
        </div>
      </Box>
      <DataTable
        className="min-h-[484px] max-h-full"
        loading={listPhones.loading}
        getRowsSelected={setItemsSelected}
        isSingle={true}
        data={listPhones.items}
        columns={getPhonesColumn({
          isDisabledCheckbox: items.length >= 1,
        })}
        notfound={
          <TableRow className="hover:bg-transparent">
            <TableCell colSpan={2} className="h-full text-center hover:bg-transparent">
              <NoData className="min-h-[350px]" />
            </TableCell>
          </TableRow>
        }
        pagination={pagination}
        setPagination={setPagination}
        total={listPhones.count}
      />
      {/*<UnlockCallFeature totalCredit={itemsSelected.length.toString()} />*/}
      <div className="flex items-center gap-2 justify-end">
        <Button
          variant={'outline'}
          onClick={onCloseModal}
          className="h-10 rounded-xl text-sm font-medium p-3"
        >
          {LABEL.cancel}
        </Button>
        <Modal
          onOpenChange={setOpenModalRental}
          className="max-w-[636px] w-full min-h-[384px] py-4 px-6"
          openModal={openModalRental}
          trigger={
            <Button
              disabled={itemsSelected.length === 0}
              className="h-10 rounded-xl text-sm font-medium p-3"
              variant={'default'}
            >
              {loading ? (
                <RiLoader2Line className="mx-auto animate-spin" />
              ) : (
                t('contactList.rentNumber')
              )}
            </Button>
          }
        >
          <ConfirmRental
            setOpenModalRental={setOpenModalRental}
            handleBuy={handleBuy}
            itemsSelected={itemsSelected}
          />
        </Modal>
      </div>
    </>
  );
};
export default GetPhoneContainer;
