import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { mobileProviderOptions } from '@/constants/contact-list/label';
import { TFilterPhone } from '@/types/contactList';
import { RiSearchLine } from '@remixicon/react';
import { t } from 'i18next';
import FilterTag from './FilterTag';

type TFilter = Partial<Omit<TFilterPhone, 'page' | 'limit' | 'status__in'>>;

type Props = {
  setParams: (params: TFilter) => void;
  params: TFilter;
};

const FilterOptions: React.FC<Props> = ({ setParams, params }) => {
  const [search, setSearch] = useState<string>('');
  const [isSearch, setIsSearch] = useState<boolean>(false);

  useEffect(() => {
    if (!isSearch) return;
    const timeOut = setTimeout(() => {
      setParams({ ...params, search });
    }, 300);
    return () => clearTimeout(timeOut);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [search]);

  const handleClearFilter = () => {
    setSearch('');
    setParams({});
  };

  const handleClearSearch = () => {
    setSearch('');
    setIsSearch(false);
    setParams({
      ...params,
      search: '',
    });
  };

  const handleRemoveTelecom = (value: string) => {
    setParams({
      ...params,
      telecom_provider__in: params.telecom_provider__in
        ?.split(',')
        .filter((item) => item !== value)
        .join(','),
    });
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex gap-4 items-center">
        <div className="flex flex-1 h-10 border p-2 gap-1 rounded-xl items-center">
          <RiSearchLine size={16} color="#6B7183" />
          <input
            className="flex-1 outline-none h-full text-sm"
            value={search}
            onChange={(e) => {
              setSearch(e.currentTarget.value);
              setIsSearch(true);
            }}
            placeholder={t('placeHolder.searchPhone')}
            type="number"
          />
        </div>

        {/*<MultipleSelect*/}
        {/*  className="rounded-xl"*/}
        {/*  options={mobileProviderOptions}*/}
        {/*  selected={params.telecom_provider__in?.split(',') || []}*/}
        {/*  onChange={(e) => {*/}
        {/*    if (!e.length) {*/}
        {/*      // eslint-disable-next-line @typescript-eslint/no-unused-vars*/}
        {/*      const { telecom_provider__in, ...rest } = params;*/}
        {/*      setParams(rest);*/}
        {/*      return;*/}
        {/*    }*/}
        {/*    setParams({ ...params, telecom_provider__in: e.join(',') });*/}
        {/*  }}*/}
        {/*  placeholder={CONTACT_LABEL.switchboard}*/}
        {/*/>*/}
      </div>

      {(params.search || params.telecom_provider__in) && (
        <div className="flex gap-2">
          {params.search && (
            <FilterTag
              label={t('contactList.phoneNumber')}
              onRemove={handleClearSearch}
              value={params.search}
            />
          )}
          {params.telecom_provider__in && (
            <FilterTag
              options={mobileProviderOptions}
              label={t('contactList.switchboard')}
              value={params.telecom_provider__in.split(',')}
              onRemoveItem={handleRemoveTelecom}
            />
          )}
          <Button onClick={handleClearFilter} variant="ghost" className="text-red-500">
            {t('common.button.clearFilter')}
          </Button>
        </div>
      )}
    </div>
  );
};

export default FilterOptions;
