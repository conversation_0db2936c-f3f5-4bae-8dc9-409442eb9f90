import { Box } from '@/components/Box';
import { useTranslation } from 'react-i18next';
import { RiErrorWarningLine } from '@remixicon/react';
import { Button } from '@/components/ui/button';
import { LABEL } from '@/constants/label';
import { TPhoneBought } from '@/types/contactList';
import { handleFormatPhone } from '@/utils/helper';

interface IConfirmRental {
  itemsSelected: TPhoneBought[];
  setOpenModalRental: (value: boolean) => void;
  handleBuy: () => void;
}

export const ConfirmRental = ({ ...props }: IConfirmRental) => {
  const { itemsSelected, setOpenModalRental, handleBuy } = props;
  const phoneSelect = itemsSelected[0];
  const { t } = useTranslation();
  return (
    <Box className="gap-2" variant={'col-start'}>
      <Box className="gap-0 items-center" variant={'col-start'}>
        <RiErrorWarningLine size={80} color={'#8F5CFF'} />
        <p className="text-lg font-medium">{t('getPhoneNumber.confirm')}</p>
      </Box>
      <div className="mt-2 text-[#515667] text-sm">
        <p>
          {t('getPhoneNumber.youAreAbout')}
          <span className="font-semibold">{handleFormatPhone(phoneSelect.phone_number)}</span>
        </p>
        <br />
        <p>
          <span className="font-semibold">{t('getPhoneNumber.duration')}</span>
          <span>3 hours</span>
        </p>
        <p>
          <span className="font-semibold">{t('getPhoneNumber.limit')}</span>
          <span>10 calls (90s per call)</span>
        </p>
        <br />
        <p className="whitespace-pre-line">
          {t('getPhoneNumber.descriptionRental', { minutes: 30 })}
        </p>
        <br />
        <p>{t('getPhoneNumber.wouldYouLike')}</p>
      </div>
      <Box className="w-full mt-6">
        <Button
          variant={'outline'}
          onClick={() => setOpenModalRental(false)}
          className="flex-1 h-10 rounded-xl text-sm font-medium p-3"
        >
          {LABEL.cancel}
        </Button>
        <Button
          className="flex-1 h-10 py-1 rounded-xl text-sm p-3 font-medium bg-[#8F5CFF] text-white"
          onClick={handleBuy}
        >
          {t('common.button.confirm')}
        </Button>
      </Box>
    </Box>
  );
};
