import Modal from '@/components/Modal';
import { CONTACT_LABEL } from '@/constants/contact-list/label';
import React, { ReactNode, useEffect, useState } from 'react';
import GetPhoneContainer from './GetPhoneContainer';
import { getSelector, useAppSelector } from '@/hooks/reduxHooks';

type Props = {
  trigger: ReactNode;
};

const GetPhoneModal: React.FC<Props> = ({ trigger }: Props) => {
  const [openModal, setOpenModal] = useState<boolean>(false);
  const { openRentPhone } = useAppSelector(getSelector('phone_bought'));
  useEffect(() => {
    if (openRentPhone){
      setOpenModal(openRentPhone);
    }
  }, [openRentPhone]);
  return (
    <Modal
      onOpenChange={setOpenModal}
      title={CONTACT_LABEL.get_phone}
      className="max-w-[920px] w-full h-[calc(100%-20px)] max-h-[884px] p-4"
      openModal={openModal}
      trigger={trigger}
    >
      <GetPhoneContainer onCloseModal={() => setOpenModal(!openModal)} />
    </Modal>
  );
};
export default GetPhoneModal;
