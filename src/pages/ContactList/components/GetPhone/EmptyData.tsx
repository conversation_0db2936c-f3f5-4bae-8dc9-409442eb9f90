import { AddPhoneIcon } from '@/assets/AddPhoneIcon';
import React from 'react';
import { CONTACT_LABEL } from '@/constants/contact-list/label';
import GetPhoneModal from './GetPhoneModal';
import { Button } from '@/components/ui/button';

const EmptyData: React.FC = () => {
  return (
    <div className="w-full h-full flex flex-col items-center justify-center p-6">
      <div className="max-w-[398px] flex-col gap-6 items-center justify-center flex flex-1 mx-auto">
        <AddPhoneIcon />
        <div className="text-center">
          <h1 className="text-xl font-semibold leading-8 tracking-[0.6px]">
            {CONTACT_LABEL.description}
          </h1>
          <h1 className="text-sm text-tertiary-foreground tracking-[0.2px]">
            {CONTACT_LABEL.sub_description}
          </h1>
        </div>
        <GetPhoneModal
          trigger={
            <Button className="w-full py-1 rounded-lg">{CONTACT_LABEL.get_phone_number}</Button>
          }
        />
      </div>
    </div>
  );
};
export default EmptyData;
