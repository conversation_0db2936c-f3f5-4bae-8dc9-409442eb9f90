import { cn } from '@/lib/utils';
import { Option } from '@/types/contactList';
import { RiCloseCircleFill } from '@remixicon/react';

interface FilterTagProps {
  label: string;
  value: string[] | string;
  onRemove?: () => void;
  className?: string;
  options?: Option[];
  onRemoveItem?: (item: string) => void;
}

const FilterTag = ({
  label,
  value,
  onRemove,
  className = '',
  onRemoveItem,
  options,
}: FilterTagProps) => {
  const formatValue = (value: string) => options?.find((item) => item.value === value)?.label;
  return (
    <div className={cn('inline-flex items-center gap-2 p-2 border rounded-xl text-sm', className)}>
      <span className="font-semibold capitalize">{label}:</span>
      {typeof value === 'string' ? (
        <div className="flex items-center bg-secondary text-xs px-1 rounded-lg">
          <span className="font-medium">{value}</span>
          <button
            onClick={onRemove}
            className="inline-flex items-center justify-center rounded-full hover:bg-gray-200 w-4 h-4"
          >
            <RiCloseCircleFill className="w-3 h-3 text-gray-500" />
          </button>
        </div>
      ) : (
        value.map((item) =>
          item ? (
            <div key={item} className="flex items-center bg-secondary text-xs px-1 rounded-lg">
              <span className="font-medium capitalize">{options ? formatValue(item) : item}</span>
              <button
                onClick={() => onRemoveItem && onRemoveItem(item)}
                className="inline-flex items-center justify-center rounded-full hover:bg-gray-200 w-4 h-4"
                aria-label={`Remove ${label} filter`}
              >
                <RiCloseCircleFill className="w-3 h-3 text-gray-500" />
              </button>
            </div>
          ) : null,
        )
      )}
    </div>
  );
};

export default FilterTag;
