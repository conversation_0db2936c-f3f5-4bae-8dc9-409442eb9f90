import React from 'react';

type Props = {
  rightContent?: React.ReactNode;
  title: string;
  subTitle: string;
};

const HeaderModel = ({ rightContent, title, subTitle }: Props) => {
  return (
    <div className="text-left mb-4">
      <div className="flex justify-between items-center">
        <div className="h-[64px]">
          <h3 className="text-2xl font-medium mb-2">{title}</h3>
          <p className="text-base font-normal text-tertiary-foreground tracking-[0.2px]">
            {subTitle}
          </p>
        </div>
        {rightContent}
      </div>
    </div>
  );
};
export default HeaderModel;
