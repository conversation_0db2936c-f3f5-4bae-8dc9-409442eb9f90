import DataTable from '@/components/table/DataTable';
import { columnsPhoneBought } from './columns/ColumnsPhoneBought';
import FilterOptions from './GetPhone/FilterOptions';
import { useEffect, useState } from 'react';
import { TPagination } from '@/types/table';
import { TFilterPhone } from '@/types/contactList';

import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks';
import { searchPhoneBought } from '@/store/phoneBought/action';

type TFilter = Partial<Omit<TFilterPhone, 'status__in'>>;

const ContactContainer = () => {
  const [params, setParams] = useState<TFilter>({ page: 1, limit: 10 });
  const { count, items } = useAppSelector((state) => state.phone_bought);

  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(
      searchPhoneBought(
        Object.entries(params).reduce((acc: Record<string, unknown>, [key, value]) => {
          if (value) {
            acc[key] = value;
          }
          return acc;
        }, {}),
      ),
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params]);

  return (
    <>
      <FilterOptions
        params={params}
        setParams={(value) =>
          setParams({
            ...params,
            search: value.search,
            telecom_provider__in: value.telecom_provider__in,
          })
        }
      />
      <div className="mt-4">
        <DataTable
          className="h-[650px]"
          data={items}
          columns={columnsPhoneBought()}
          total={count}
          pagination={{ pageSize: params.limit || 10, currentPage: params.page || 1 }}
          setPagination={(pagination: TPagination) =>
            setParams({ ...params, page: pagination.currentPage, limit: pagination.pageSize })
          }
        />
      </div>
    </>
  );
};

export default ContactContainer;
