import { Button } from '@/components/ui/button';
import { Di<PERSON>, DialogContent, Di<PERSON>Footer, DialogTrigger } from '@/components/ui/dialog';
import { useAppDispatch } from '@/hooks/reduxHooks';
import { cancelRenewal } from '@/store/phoneBought/action';
import { TPhoneBought } from '@/types/contactList';
import { RiCloseLine, RiErrorWarningLine } from '@remixicon/react';
import { t } from 'i18next';
import { useState } from 'react';

type Props = {
  phone: TPhoneBought;
};

const CancelRenewPopup = ({ phone }: Props) => {
  const [openModal, setOpenModal] = useState<boolean>(false);
  const dispatch = useAppDispatch();
  const handleCancelRenew = async () => {
    dispatch(cancelRenewal({ hotline_id: phone.id, is_renewal: false }));
    setOpenModal(false);
  };
  return (
    <Dialog open={openModal} onOpenChange={setOpenModal}>
      <DialogTrigger asChild>
        <Button
          className="justify-start p-3 rounded-xl text-sm font-medium gap-1 text-red-500 hover:text-red-500"
          variant={'ghost'}
          disabled={!phone.is_renewal}
        >
          <RiCloseLine size={20} />
          {t('common.button.cancelRenew')}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[529px] flex flex-col items-center gap-0">
        <RiErrorWarningLine className="text-destructive" size={80} />
        <div className="text-lg font-medium">{t('common.contactList.cancelRenew')}</div>
        <div className="text-center text-sm mt-2 text-secondary">
          {t('common.contactList.subCancelRenew')}
        </div>
        <DialogFooter className="mt-6 w-full grid grid-cols-2 gap-4">
          <Button variant={'outline'} type="button">
            {t('common.contactList.keepRenew')}
          </Button>
          <Button onClick={handleCancelRenew} variant={'delete'} type="button">
            {t('common.contactList.cancelRenew')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
export default CancelRenewPopup;
