import React, { useMemo } from 'react';
import PersonalInfo from '@/pages/Detail/components/PersonalInfo';
import { TContactDetailResponse, TContactItems } from '@/types/contactList';
import ContactInformation from '@/pages/Detail/components/ContactInformation';
import TimeLine from '@/pages/Detail/components/TimeLine';
// import Reminder from '@/pages/Detail/components/Reminder';
import Note from '@/pages/Detail/components/Note';
import SegmentContainer from '@/pages/Detail/components/SegmentContainer';
import { useQuery } from '@tanstack/react-query';
import { QUERY_KEY } from '@/utils/constants';
import { get } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { useParams } from 'react-router-dom';
import AppLoadIcon from '@/assets/AppLoadIcon';
import Breadcrumb from '@/components/Breadcrumb';

const ContactDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  const {
    data: contactDetailResponse,
    isLoading: loadingContact,
    refetch: refetchDetail,
  } = useQuery({
    queryKey: [QUERY_KEY.CONTACT_DETAIL, id],
    enabled: !!id,
    staleTime: 1000,
    queryFn: () =>
      get({
        endpoint: ENDPOINTS.contact_list + id?.toString() + '/',
      }),
  });

  const detailContact = useMemo(() => {
    return (contactDetailResponse?.data as unknown as TContactDetailResponse)
      ?.data as unknown as TContactItems;
  }, [contactDetailResponse?.data]);

  return (
    <>
      <Breadcrumb endUrl={''} title={detailContact?.full_name ?? ''} />
      {loadingContact ? (
        <AppLoadIcon />
      ) : (
        <>
          <div className="flex gap-6 pt-6">
            <div>
              <div className="p-6 bg-white rounded-2xl w-[448px] border-tertiary border">
                <PersonalInfo
                  detailContact={detailContact}
                  id={id as string}
                  refetchDetail={refetchDetail}
                />
                <ContactInformation detailContact={detailContact} refetchDetail={refetchDetail} />
                <Note detailContact={detailContact} />
              </div>
            </div>
            <div className="px-6 flex-1 h-[606px]">
              <TimeLine idContact={id as string} />
            </div>
          </div>
          <div className="flex gap-6">
            <SegmentContainer id={id as string} detailContact={detailContact} />
            {/*<Reminder*/}
            {/*  id={id as string}*/}
            {/*  detailContact={detailContact}*/}
            {/*  refetchDetail={refetchDetail}*/}
            {/*/>*/}
          </div>
        </>
      )}
    </>
  );
};
export default ContactDetail;
