import React from 'react';
import { cn } from '@/lib/utils';

type ItemInfoProps = {
  className?: string;
  title: string;
  value: React.ReactNode;
  classNameContent?: string;
  classNameTitle?: string;
  isTruncate?: boolean;
};

export const ItemInfo: React.FC<ItemInfoProps> = ({ ...props }: ItemInfoProps) => {
  const { title, value, className, classNameContent, classNameTitle, isTruncate = true } = props;
  return (
    <div className={cn('mb-3 min-w-0', className)}>
      <p className={cn('text-tertiary-foreground text-xs capitalize truncate', classNameTitle)}>
        {title}
      </p>
      <p className={cn('text-tertiary-foreground text-sm mt-1', classNameContent , isTruncate && 'truncate')}>
        {value}
      </p>
    </div>
  );
};
