import React from 'react';
import { RiFacebookCircleFill, RiPhoneLine } from '@remixicon/react';
import { ZaloIcon } from '@/assets/ZaloIcon';
import { formatDateTime } from '@/utils/helper';
import { useTranslation } from 'react-i18next';

type TLeftTimeLine = {
  time_call: string;
  type: string;
};
const LeftTimeLine: React.FC<TLeftTimeLine> = ({ ...props }: TLeftTimeLine) => {
  const { time_call, type } = props;
  const { t } = useTranslation();
  const handleRenderIcon = () => {
    switch (type) {
      case 'outgoing':
        return (
          <>
            <RiPhoneLine color={'#8F5CFF'} size={16} />
            {t('common.call')}
          </>
        );
      case 'zalo':
        return (
          <>
            <ZaloIcon color={'#8F5CFF'} size={16}/>
            {t('common.zaloCall')}
          </>
        );
      case 'facebook':
        return (
          <>
            <RiFacebookCircleFill color={'#8F5CFF'} size={16} />
            {t('common.facebookCall')}
          </>
        );
    }
  };

  return (
    <div className="w-[140px] h-[44px] text-end">
      <p className="text-sm text-brand flex items-center justify-end gap-1 font-medium">
        {handleRenderIcon()}
      </p>
      <p className="text-sm text-secondary">{formatDateTime(time_call, '/', ':')}</p>
    </div>
  );
};
export default LeftTimeLine;
