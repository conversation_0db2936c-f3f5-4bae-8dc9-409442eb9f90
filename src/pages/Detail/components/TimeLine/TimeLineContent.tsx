import React, { useEffect, useRef, useState } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useTranslation } from 'react-i18next';
import { RiPhoneLine } from '@remixicon/react';
import { MissedCallIcon } from '@/assets/MissedCallIcon';
import AudioDuration from '@/components/AudioDuration';

type TTimeLineContent = {
  status: string;
  recording_url: string;
  content: string | undefined;
};
const TimeLineContent: React.FC<TTimeLineContent> = ({ ...props }: TTimeLineContent) => {
  const { status, recording_url, content } = props;
  const { t } = useTranslation();
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [duration, setDuration] = useState<number>(0);

  useEffect(() => {
    const audio = new Audio(recording_url);
    audio.onloadedmetadata = () => {
      setDuration(audio.duration);
    };
    audioRef.current = audio;
  }, [recording_url]);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${String(mins)}'${String(secs).padStart(2, '0')}s`;
  };
  const handleViewStatus = () => {
    switch (status) {
      case 'miss_call':
        return (
          <div className="flex items-center justify-center h-[26px] bg-error rounded-lg w-fit px-1 gap-0.5">
            <MissedCallIcon />
            <p className="text-error-text text-xs">{t('common.missedCall')}</p>
          </div>
        );
      case 'meet_call':
        return (
          <>
            <div className="flex gap-2 items-center mb-2">
              <div className="flex items-center justify-center h-[26px] bg-success rounded-lg w-fit px-1 gap-0.5">
                <RiPhoneLine color={'#205B2B'} size={12} />
                <p className="text-success-text text-xs">{t('common.completedCall')}</p>
              </div>
              <AudioDuration audioUrl={recording_url} isDotOnly={true} />
            </div>
            <p className="text-left text-sm text-secondary">
              {t('common.duration')}: {formatTime(duration)}
            </p>
          </>
        );
      case 'zalo':
        return (
          <>
            <div className="flex items-center justify-center h-[26px] bg-standard rounded-lg mb-2 w-fit px-1 gap-0.5">
              <RiPhoneLine color={'#146BE1'} size={12} />
              <p className="text-standard-text text-xs">{t('common.zaloBroadcast')}</p>
            </div>
            <div className="p-2 rounded-2xl bg-secondary-foreground_crm text-left">
              <p className="mx-2 mb-[4px] text-sm font-normal">{t('common.content')}</p>
              <ScrollArea className="h-[144px] bg-background-foreground rounded-lg text-xs">
                <p className="p-2">{content}</p>
              </ScrollArea>
            </div>
          </>
        );
      case 'facebook':
        return (
          <>
            <div className="flex items-center justify-center h-[26px] bg-standard rounded-lg mb-2 w-fit px-1 gap-0.5">
              <RiPhoneLine color={'#146BE1'} size={12} />
              <p className="text-standard-text text-xs">{t('common.facebookBroadcast')}</p>
            </div>
            <div className="p-2 rounded-2xl bg-secondary-foreground_crm text-left">
              <p className="mx-2 mb-[4px] text-sm font-normal">{t('common.content')}</p>
              <ScrollArea className="h-[144px] bg-background-foreground rounded-lg text-xs">
                <p className="p-2">{content}</p>
              </ScrollArea>
            </div>
          </>
        );
    }
  };

  return <div className="p-3 rounded-2xl border border-boder">{handleViewStatus()}</div>;
};
export default TimeLineContent;
