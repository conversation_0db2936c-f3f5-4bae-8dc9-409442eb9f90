import React, { useEffect, useState } from 'react';
import LeftTimeLine from '@/pages/Detail/components/TimeLine/LeftTimeLine';
import TimeLineContent from '@/pages/Detail/components/TimeLine/TimeLineContent';
import { get } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { TMultiResponse } from '@/types/ResponseApi';
import { ITypeHistoryCall, TContactHistoryItems } from '@/types/contactList';
import { ScrollArea } from '@/components/ui/scroll-area';
import HistoryBar from '@/pages/Detail/components/HistoryBar';
import { HISTORY_CALL_STATUS_ENUM } from '@/utils/constants';
import { checkObject, mergeArraysById } from '@/utils/helper';
import { cn } from '@/lib/utils';
import { RiLoader2Line } from '@remixicon/react';
import dayjs from 'dayjs';
import { NoData } from '@/components/NoData';

type TTimeLineContact = {
  idContact: string;
};
const historyData = [
  { type__in: HISTORY_CALL_STATUS_ENUM.all, label: 'All' },
  { type__in: HISTORY_CALL_STATUS_ENUM.outgoing, label: 'Call' },
  // { type__in: HISTORY_CALL_STATUS_ENUM.zalo, label: 'Zalo' },
  { type__in: HISTORY_CALL_STATUS_ENUM.facebook, label: 'Facebook' },
];
const TimeLine: React.FC<TTimeLineContact> = ({ ...props }: TTimeLineContact) => {
  const { idContact } = props;
  const [loading, setLoading] = useState<boolean>(false);
  const [typeCall, setTypeCall] = useState<ITypeHistoryCall>({
    type__in: HISTORY_CALL_STATUS_ENUM.all,
    label: 'All',
  });
  const checkDiffSocial =
    typeCall.type__in !== HISTORY_CALL_STATUS_ENUM.zalo &&
    typeCall.type__in !== HISTORY_CALL_STATUS_ENUM.facebook;

  const [contactHistory, setContactHistory] = useState<TContactHistoryItems[]>([]);

  const handleCheckParams = (isHistory: boolean) => {
    let params = checkObject({
      page: 1,
      limit: 50,
      order_by: '-date_created',
    });

    if (isHistory && checkDiffSocial) {
      params = { ...params, type__in: typeCall.type__in };
    }
    if (!isHistory && typeCall.type__in !== HISTORY_CALL_STATUS_ENUM.outgoing) {
      params = { ...params, type__in: typeCall.type__in };
    }
    return params;
  };

  const fetchHistory = async () => {
    return get<TContactHistoryItems>({
      endpoint: ENDPOINTS.history_call(idContact),
      params: handleCheckParams(true),
    }).then((res) => (res.data as TMultiResponse<TContactHistoryItems>).data);
  };
  const fetchActivity = async () => {
    return get<TContactHistoryItems>({
      endpoint: ENDPOINTS.activity(idContact),
      params: handleCheckParams(false),
    }).then((res) => (res.data as TMultiResponse<TContactHistoryItems>).data);
  };

  const handleFetchHistory = async () => {
    // const [resHistory, resActivity] = await Promise.all([fetchHistory(), fetchActivity()]);
    let arrHistory: TContactHistoryItems[] = [];
    let arrActivity: TContactHistoryItems[] = [];

    if (checkDiffSocial) {
      arrHistory = (await fetchHistory().then((res) => res?.items)) ?? [];
    }
    if (typeCall.type__in !== HISTORY_CALL_STATUS_ENUM.outgoing) {
      arrActivity = (await fetchActivity().then((res) => res?.items)) ?? [];
    }

    setContactHistory(
      (mergeArraysById(arrHistory, arrActivity) as TContactHistoryItems[]).sort(
        (a, b) => dayjs(b.date_created).valueOf() - dayjs(a.date_created).valueOf(),
      ) as TContactHistoryItems[],
    );
  };

  useEffect(() => {
    setLoading(true);
    handleFetchHistory().finally(() => setLoading(false));
    //   eslint-disable-next-line react-hooks/exhaustive-deps
  }, [typeCall]);

  const listHistory = contactHistory.filter((item) => item.type !== 'email');

  return (
    <>
      <HistoryBar historyData={historyData} typeCall={typeCall} setTypeCall={setTypeCall} />
      {listHistory.length === 0 && !loading ? (
        <NoData className="h-[550px]" />
      ) : (
        <ScrollArea className="h-[550px] pr-6 relative">
          {/*Item*/}
          {loading ? (
            <div className="absolute z-20 flex items-center justify-center w-full h-full m-auto bg-muted/50 top-0 left-0">
              <RiLoader2Line className="animate-spin" size={24} />
            </div>
          ) : (
            <></>
          )}
          {listHistory.map((item) => {
            const { status, time_call, time_activity, type, recording_url, content } = item;
            const statusView: string = status ?? type;
            const classFit = statusView === 'zalo' || statusView === 'facebook' ? 'grow w-fit' : '';
            const timeCall = time_call ?? time_activity;
            return (
              <div className="flex" key={item.id}>
                {/*Left Content*/}
                <LeftTimeLine time_call={timeCall} type={type} />
                {/*End Left Content*/}

                {/*Icon*/}
                <div className="relative last:after:hidden after:absolute after:top-5 after:bottom-2 after:start-3.5 after:w-px after:-translate-x-[0.5px] after:bg-gray-200 dark:after:bg-neutral-700">
                  <div className="relative z-10 size-7 flex justify-center items-center -mt-1.5">
                    <div className="w-[12px] h-[12px] rounded-full bg-avatar" />
                  </div>
                </div>
                {/*End Icon*/}

                {/*Right Content*/}
                <div className={cn('pt-0.5 pb-8 min-w-[112px]', classFit)}>
                  <TimeLineContent
                    status={statusView}
                    recording_url={recording_url}
                    content={content}
                  />
                </div>
                {/*  End Right Content*/}
              </div>
            );
          })}
          {/*End Item*/}
        </ScrollArea>
      )}
    </>
  );
};
export default TimeLine;
