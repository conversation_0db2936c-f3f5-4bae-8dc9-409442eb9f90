import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { ITypeHistoryCall } from '@/types/contactList';
import { cn } from '@/lib/utils';

type THistoryData = {
  historyData: ITypeHistoryCall[];
  typeCall: ITypeHistoryCall;
  setTypeCall: (type: ITypeHistoryCall) => void;
};
const HistoryBar: React.FC<THistoryData> = ({ ...props }: THistoryData) => {
  const { historyData, typeCall, setTypeCall } = props;
  const { t } = useTranslation();

  return (
    <div className="text-left mb-6 flex gap-6 items-center">
      <span className="text-black text-base font-medium">{t('common.history')}</span>
      <div className="flex gap-4 items-center">
        {historyData.map((item) => {
          return (
            <Button
              key={item.type__in}
              onClick={() => setTypeCall(item)}
              className={cn(
                'h-[28px] w-fit rounded-full bg-secondary-foreground_crm hover:bg-secondary-foreground_crm px-2 py-1',
                typeCall.type__in === item.type__in ? 'text-brand' : 'text-secondary ',
              )}
            >
              <span className="font-normal text-sm">{item.label}</span>
            </Button>
          );
        })}
      </div>
    </div>
  );
};
export default HistoryBar;
