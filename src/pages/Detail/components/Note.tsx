import React from 'react';
import { useTranslation } from 'react-i18next';
import { TContactItems } from '@/types/contactList';
import { ScrollArea } from '@/components/ui/scroll-area';
import { handleCheckEmptyValue } from '@/utils/helper';

type TReminderProps = {
  detailContact?: TContactItems;
};

const Note: React.FC<TReminderProps> = ({ ...props }: TReminderProps) => {
  const { t } = useTranslation();
  const { detailContact } = props;
  return (
    <div className="w-full text-left border-t border-tertiary pt-2">
      <div className="flex justify-between items-center mb-2">
        <p className="font-medium text-md text-primary">{t('common.note')}</p>
      </div>
      {detailContact?.note?.note ? (
        <ScrollArea className="h-[135px]">
          <p className='text-tertiary-foreground text-sm mt-1 whitespace-pre-line'>{handleCheckEmptyValue(detailContact?.note?.note ?? '')}</p>
        </ScrollArea>
      ) : (
        <div className="h-[135px]">
          <div className="flex justify-center items-center h-full max-w-full rounded-lg border-border bg-secondary-foreground_crm pl-3 pr-6">
            <p className="truncate font-normal text-secondary text-sm">{t('common.noData')}</p>
          </div>
        </div>
      )}
    </div>
  );
};
export default Note;
