import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FormInput } from '@/components/Form/FormInput';
import { SubmitHandler, useForm } from 'react-hook-form';
import { TContactItems, TErrorReminderDetail, TReminderDetail } from '@/types/contactList';
import { FormArea } from '@/components/Form/FormArea';
import { TimePicker, TimePickerProps } from '@/components/TimePicker';
import {
  checkObject,
  disabledPastDate,
  expectedDate,
  fStringToDate,
  fTotoISOString,
  isOverDue,
} from '@/utils/helper';
import DatePicker from '@/components/DatePicker';
import { zodResolver } from '@hookform/resolvers/zod';
import { formSchemaReminder } from '@/types/validation';
import { toast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { update } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { TBaseResponse, TErrorResponse } from '@/types/ResponseApi';
import { useMutation } from '@tanstack/react-query';
import DeleteReminderPopup from '@/pages/ContactList/components/Reminder/DeleteReminderPopup';
import { reminderApi } from '@/apis/reminder';
import { RiDeleteBin6Line } from '@remixicon/react';

type TReminderProps = {
  id: string;
  detailContact?: TContactItems;
  refetchDetail: () => void;
};

const defaultReminder: TReminderDetail = { date: '', time: '' };
const defaultError: TErrorReminderDetail = { date: '', time: '', common: '' };

const Reminder: React.FC<TReminderProps> = ({ ...props }: TReminderProps) => {
  const { t } = useTranslation();
  const { id, detailContact, refetchDetail } = props;
  const [isShowDelete, setIsShowDelete] = useState(false);
  const [timeReminder, setTimeReminder] = useState<TReminderDetail>(defaultReminder);
  const [errorReminder, setErrorReminder] = useState<TErrorReminderDetail>(defaultError);
  const { control, handleSubmit, reset, watch } = useForm<TContactItems>({
    resolver: zodResolver(formSchemaReminder),
    defaultValues: {},
  });
  const checkOverDue = () => {
    const fDate = `${timeReminder.date} ${timeReminder.time}`;
    const time = fTotoISOString(fDate);
    return isOverDue(time);
  };

  const handleSetForm = () => {
    if (!detailContact) {
      return;
    }
    const updateValues: TContactItems = Object.keys(detailContact).reduce((acc, key) => {
      const k = key as keyof TContactItems;
      (acc[k] as unknown) = (detailContact[k] ?? '') as unknown;
      return acc;
    }, {} as TContactItems);
    const reminder = detailContact?.reminder?.time_reminder;
    const { date, time } = expectedDate(reminder ?? '');
    setTimeReminder({
      ...timeReminder,
      date: reminder ? date : '',
      time: reminder ? time : '',
    });
    reset({
      ...updateValues,
      note_content: detailContact.note?.note ?? '',
    });
  };

  const onSubmit: SubmitHandler<TContactItems> = (values) => {
    if (checkOverDue()) {
      toast({
        status: 'error',
        description: errorReminder.date || errorReminder.time || errorReminder.common,
      });
      return;
    }
    const fDate = `${timeReminder.date} ${timeReminder.time}`;
    const time = fTotoISOString(fDate);
    const dataReminder = checkObject({
      reminder: time
        ? {
            ...detailContact?.reminder,
            note: values.reminder?.note ?? '',
            activity: values.reminder?.activity ?? '',
            time_reminder: time,
          }
        : undefined,
    }) as TContactItems;
    editContactMutation.mutate(dataReminder);
    setErrorReminder(defaultError);
  };

  const handleSubmitSuccessForm = (error: unknown) => {
    if (error === null) {
      toast({
        status: 'success',
        description: t('contactList.yourChangeHasBeenSavedSuccessfully'),
      });
      refetchDetail();
    }
  };

  const editContactMutation = useMutation({
    mutationFn: async (payload: TContactItems): Promise<TBaseResponse<TContactItems>> => {
      return update({
        endpoint: ENDPOINTS.contact_list + id?.toString() + '/',
        params: payload,
      }).then((res) => res.data as TBaseResponse<TContactItems>);
    },
    onSuccess: (res) => handleSubmitSuccessForm(res.error),
    onError: (error: TErrorResponse) => {
      console.log('error', { error });
    },
  });

  const mutationDelete = useMutation({
    mutationFn: reminderApi.remove,
    onSuccess: () => {
      toast({
        status: 'success',
        description: t('common.reminder.delete'),
        duration: 3000,
      });
      refetchDetail();
      setIsShowDelete(false);
      reset({
        reminder: {
          activity: '',
          note: '',
          time_reminder: '',
        },
      });
      setTimeReminder(defaultReminder);
    },
    onError: (error) => error,
  });
  const handleDelete = () => {
    mutationDelete.mutate(detailContact?.reminder?.id.toString() || '');
  };

  const checkDiffReminder = () => {
    if (!detailContact?.reminder) {
      return !(timeReminder.date && timeReminder.time);
    }

    const { activity, note, time_reminder } = detailContact.reminder;
    const { date, time } = expectedDate(time_reminder ?? '');
    const act = watch('reminder.activity');
    const not = watch('reminder.note');

    return (
      activity === act && note === not && date === timeReminder.date && time === timeReminder.time
    );
  };

  useEffect(() => {
    if (!!detailContact) {
      handleSetForm();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [detailContact]);

  useEffect(() => {
    const fDate = `${timeReminder.date} ${timeReminder.time}`;
    const time = fTotoISOString(fDate);
    const isOverDueDate = isOverDue(time);
    if (isOverDueDate) {
      const reminder = detailContact?.reminder?.time_reminder;
      const { date, time } = expectedDate(reminder ?? '');
      if (date !== timeReminder.date || time !== timeReminder.time) {
        setErrorReminder({ date: '', time: '', common: t('common.error.overDue') });
      }
      return;
    }
    setErrorReminder({ date: '', time: '', common: '' });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [detailContact, timeReminder.date, timeReminder.time]);

  const timeDt = timeReminder.time.split(':');
  return (
    <div className="px-6 py-4 mt-6 text-left bg-white rounded-2xl flex-1 border-tertiary border min-h-[275px]">
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="flex justify-between items-center mb-4">
          <p className="font-medium text-lg text-primary">{t('common.reminder.title')}</p>
          <div className="ml-auto flex gap-3 items-center">
            <Button
              type={'submit'}
              disabled={checkDiffReminder()}
              className="border border-secondary bg-secondary w-[74px] h-[40px] flex items-center justify-center text-primary hover:bg-secondary-foreground_crm hover:text-filter"
            >
              <span className="text-base font-medium">{t('common.button.save')}</span>
            </Button>
            {detailContact?.reminder && (
              <Button
                onClick={() => setIsShowDelete(true)}
                type={'button'}
                className="bg-white h-[40px] w-[40px] border border-delete text-delete hover:text-error-text hover:bg-white hover:border-error-text"
              >
                <RiDeleteBin6Line size={20} />
              </Button>
            )}
          </div>
        </div>
        <FormInput
          control={control}
          name={'reminder.activity'}
          className={'bg-white'}
          isRemoveLabel={true}
          classContainer="mb-4"
          placeholder={t('common.activity')}
        />
        <FormArea
          control={control}
          name={'reminder.note'}
          isRemoveLabel={true}
          className="min-h-[75px] h-[75px] bg-white resize-none"
          classContainer="mb-3"
          placeholder={t('common.takeNote')}
        />
      </form>
      <div className="flex gap-4 justify-between">
        <DatePicker
          error={errorReminder.date || Boolean(errorReminder.common)}
          defaultDate={fStringToDate(timeReminder.date)!}
          isHideLabel={true}
          placeholder={t('common.button.date')}
          className="w-full mt-1 bg-white font-normal"
          disableDate={disabledPastDate}
          onChange={(value) => setTimeReminder({ ...timeReminder, date: value })}
        />
        <TimePicker
          error={errorReminder.time || Boolean(errorReminder.common)}
          value={{ hour: timeDt[0] || null, minute: timeDt[1] || null }}
          isHideLabel={true}
          placeholder={t('common.button.time')}
          className="w-full mt-1 bg-white font-normal"
          onChange={(time: TimePickerProps) => {
            const timeValue = `${time.hour}:${time.minute}`;
            setTimeReminder({
              ...timeReminder,
              time: timeValue,
            });
          }}
        />
      </div>
      {errorReminder.common && (
        <div className="mt-1 text-xs text-red-500 font-medium">{errorReminder.common}</div>
      )}
      <DeleteReminderPopup
        loading={mutationDelete.isPending}
        open={isShowDelete}
        onClose={setIsShowDelete}
        onDelete={handleDelete}
      />
    </div>
  );
};
export default Reminder;
