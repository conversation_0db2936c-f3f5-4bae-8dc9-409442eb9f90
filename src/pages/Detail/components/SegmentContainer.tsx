import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { SegmentItem } from '@/pages/Detail/components/SegmentItem';
import { IOptions, TContactCreate, TContactItems } from '@/types/contactList';
import { useMutation } from '@tanstack/react-query';
import { TBaseResponse, TErrorResponse } from '@/types/ResponseApi';
import { update } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { toast } from '@/hooks/use-toast';
import { SelectSearch } from '@/components/SelectSearch';
import { Button } from '@/components/ui/button';
import { ModalSubmitSegment } from '@/components/Segment/ModalSubmitSegment';
import Modal from '@/components/Modal';
import { getSelector, useAppDispatch, useAppSelector } from '@/hooks/reduxHooks';
import { removeSegment } from '@/store/segment/segmentSlice';
import { TSegmentData } from '@/types/segment';

type TSegmentContainer = {
  id: string;
  detailContact?: TContactItems;
};

const SegmentContainer: React.FC<TSegmentContainer> = ({ ...props }: TSegmentContainer) => {
  const { id, detailContact } = props;
  const { allSegments, allDatasetSegments, segmentCreated } = useAppSelector(
    getSelector('segment'),
  );
  const { t } = useTranslation();
  const [contact, setContact] = useState<TContactItems>();
  const dispatch = useAppDispatch();
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const processedSegmentRef = useRef<string | null>(null);

  useEffect(() => {
    if (detailContact) {
      setContact(detailContact);
    }
  }, [detailContact]);

  const handleUpdateSegment = (values: IOptions[]) => {
    const newSegmentIds = values.map((item) => item.value.toString()).sort();
    const currentSegmentIds = (
      contact?.segments.map((segment) => segment.segment.id.toString()) ?? []
    ).sort();

    const hasChanges =
      newSegmentIds.length !== currentSegmentIds.length ||
      newSegmentIds.some((id, index) => id !== currentSegmentIds[index]);

    if (hasChanges) {
      setContact(
        (prev) =>
          ({
            ...prev,
            segments: values.map((item) => ({
              segment: { id: parseInt(item.value), name: item.label, color: item.color },
            })),
          }) as TContactItems,
      );
      updateNewSegment(newSegmentIds);
    }
  };

  const handleRemoveSegment = (values: TContactItems) => {
    const segment_ids = values.segments.map((item) => item.segment.id.toString());
    updateNewSegment(segment_ids);
    setContact(values);
  };

  const handleUpdateSuccess = (error: unknown) => {
    if (error === null) {
      toast({
        status: 'success',
        description: t('contactList.yourChangeHasBeenSavedSuccessfully'),
      });
      // refetchDetail();
    }
  };
  const editContactMutation = useMutation({
    mutationFn: async (payload: TContactCreate): Promise<TBaseResponse<TContactItems>> => {
      return update({
        endpoint: ENDPOINTS.contact_list + id?.toString() + '/',
        params: payload,
      }).then((res) => res.data as TBaseResponse<TContactItems>);
    },
    onSuccess: (res) => {
      handleUpdateSuccess(res.error);
      dispatch(removeSegment());
    },
    onError: (error: TErrorResponse) => {
      console.log('error', { error });
    },
  });

  const updateNewSegment = useCallback(
    (segment_ids: string[]) => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }

      updateTimeoutRef.current = setTimeout(() => {
        editContactMutation.mutate({
          segment_ids,
        });
      }, 300);
    },
    [editContactMutation],
  );

  const handleAddMoreSegment = useCallback(() => {
    if (!segmentCreated || !contact) {
      return;
    }

    const newSegmentId = parseInt(segmentCreated.id);
    const existingSegments = contact.segments ?? [];

    const segmentExists = existingSegments.some((segment) => segment.segment.id === newSegmentId);

    if (!segmentExists) {
      const newSegment = {
        segment: {
          id: newSegmentId,
          name: segmentCreated.name,
          color: segmentCreated.color,
        },
      };

      const updatedSegments = [newSegment, ...existingSegments];

      const allSegmentIds = updatedSegments.map((item) => item.segment.id.toString());

      setContact((prev: TContactItems | undefined): TContactItems | undefined => {
        if (!prev) {
          return prev;
        }
        return {
          ...prev,
          segments: updatedSegments,
        };
      });

      updateNewSegment(allSegmentIds);
    }
  }, [segmentCreated, contact, updateNewSegment]);
  useEffect(() => {
    if (segmentCreated && segmentCreated.id !== processedSegmentRef.current) {
      processedSegmentRef.current = segmentCreated.id;
      handleAddMoreSegment();
    } else if (!segmentCreated) {
      processedSegmentRef.current = null;
    }
  }, [segmentCreated, handleAddMoreSegment]);

  useEffect(() => {
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
    };
  }, []);

  const defaultSelect =
    contact?.segments
      ?.filter(
        (segment, index, array) =>
          // Remove duplicates by keeping only the first occurrence of each segment ID
          array.findIndex((s) => s.segment.id === segment.segment.id) === index,
      )
      ?.map((item) => ({
        value: item.segment.id.toString(),
        label: item.segment.name,
        color: item.segment.color,
      })) ?? [];

  const isDataset = detailContact?.datatype === 'DATASET';

  const handleConvertSegment = (segmentList: TSegmentData) => {
    return segmentList.items.map((item) => {
      return {
        value: item.id.toString(),
        label: item.name,
        color: item.color,
      };
    });
  };

  return (
    <div className="px-6 py-5 mt-6 text-left bg-white rounded-2xl w-[448px] min-h-[275px] border-tertiary border">
      <div className="flex justify-between mb-4">
        <p className="font-medium text-lg text-primary">{t('common.segment.titleMulti')}</p>
        <Modal
          className="w-[850px] max-w-none"
          titleAlign={'center'}
          title={t('segment.createSegment')}
          trigger={
            <Button type={'button'} variant={'cancel'}>
              {t('common.button.createNew')}
            </Button>
          }
        >
          <ModalSubmitSegment isShowResult={false} />
        </Modal>
      </div>
      <div className="flex flex-wrap gap-2 mb-3">
        {contact?.segments
          ?.filter(
            (segment, index, array) =>
              array.findIndex((s) => s.segment.id === segment.segment.id) === index,
          )
          ?.map((segment) => {
            return (
              <SegmentItem
                key={segment.segment.id}
                segmentName={segment.segment.name}
                segmentId={segment.segment.id}
                detailContact={contact}
                color={segment.segment.color}
                setDetailContact={(value) => {
                  handleRemoveSegment(value);
                }}
              />
            );
          })}
      </div>
      <SelectSearch
        options={handleConvertSegment(isDataset ? allDatasetSegments : allSegments)}
        defaultValues={defaultSelect}
        isHideSearch={true}
        onSelect={handleUpdateSegment}
        placeholderInput={t('placeHolder.segment')}
        placeholder={t('placeHolder.selectSegment')}
        className="w-full"
      />
    </div>
  );
};
export default SegmentContainer;
