import { useTranslation } from 'react-i18next';
import { TContactItems } from '@/types/contactList';
import React from 'react';
import { Button } from '@/components/ui/button';
import Modal from '@/components/Modal';
import { Avatar } from '@/components/Avatar/Avatar';
import { formatDate } from '@/utils/helper';
import { ContactSubmitForm } from '@/components/ContactList/ContactSubmitForm';
import CustomToolTips from '@/components/CustomToolTips';

type TPersonalInfoProps = {
  id: string;
  detailContact?: TContactItems;
  refetchDetail: () => void;
};

const PersonalInfo: React.FC<TPersonalInfoProps> = ({ ...props }: TPersonalInfoProps) => {
  const { detailContact, refetchDetail } = props;
  const { t } = useTranslation();
  return (
    <div className="w-full text-left">
      <p className="mb-4 font-medium text-md text-primary">
        {t('contactDetail.personalInformation')}
      </p>
      <div className="flex gap-1.5 justify-between items-center">
        <div className="flex gap-1.5 justify-between items-center w-[270px]">
          <Avatar
            uid={detailContact?.fb_uid ?? ''}
            name={detailContact?.full_name ?? ''}
            className={'w-[64px] h-[64px] text-2xl'}
          />
          <div className="w-[190px]">
            <CustomToolTips
              element={
                <p className="text-lg text-primary font-semibold py-1 px-1 min-w-[128px] truncate">
                  {detailContact?.full_name}
                </p>
              }
              content={detailContact?.full_name}
            />
            <div className="flex gap-2 items-center py-1 px-1 text-sm text-tertiary-foreground">
              <span>
                {detailContact?.dob ? (
                  formatDate(detailContact?.dob ?? '', '/')
                ) : (
                  <>{t('common.dob')}: -</>
                )}
              </span>
              <div className="w-[4px] h-[4px] bg-tertiary-foreground rounded-full"></div>
              <span className="capitalize">
                {detailContact?.gender ?? <>{t('common.gender')}: -</>}
              </span>
            </div>
          </div>
        </div>
        <div className="flex items-center justify-center gap-2 pb-6 w-[122px]">
          <Modal
            trigger={
              <Button
                type={'button'}
                className="border border-secondary bg-secondary w-[74px] h-[40px] flex items-center justify-center text-primary hover:bg-secondary-foreground_crm hover:text-filter"
              >
                <span className="text-base font-medium">{t('common.button.edit')}</span>
              </Button>
            }
            isCloseIcon={false}
            className="w-fit max-w-none"
            titleAlign={'center'}
            title={t('contactList.titleEditContact')}
          >
            <ContactSubmitForm
              isCreate={false}
              detailContact={detailContact}
              handleSubmitSuccess={refetchDetail}
            />
          </Modal>

          {/*<DeleteContactDetail id={id} />*/}
        </div>
      </div>
    </div>
  );
};

export default PersonalInfo;

// type TDeleteContactDetail = {
//   id: string;
// };

// const DeleteContactDetail = ({ ...props }: TDeleteContactDetail) => {
//   const { id } = props;
//   const { t } = useTranslation();
//   const navigate = useNavigate();
//
//   const handleRemoveSuccess = () => {
//     toast({
//       status: 'success',
//       description: t('contactList.contactMovedToTrash'),
//     });
//     navigate(`${ROOT_PATH}/${ROOT_ROUTE.contact['']}`);
//   };
//   const deleteListContactMutation = useMutation({
//     mutationFn: async () => {
//       return remove({
//         endpoint: ENDPOINTS.contact_list + id + '/',
//       }).then((res) => res.data);
//     },
//     onSuccess: handleRemoveSuccess,
//     onError: (error: TErrorResponse) => {
//       console.log('error', { error });
//     },
//   });
//
//   return (
//     <Modal
//       isCloseIcon={false}
//       className="w-fit max-w-none"
//       titleAlign={'center'}
//       trigger={
//         <Button className="bg-white h-[40px] w-[40px] border border-delete text-delete hover:text-error-text hover:bg-white hover:border-error-text">
//           <RiDeleteBin6Line size={20} />
//         </Button>
//       }
//       title={
//         <>
//           <div className="m-auto w-fit">
//             <RiErrorWarningLine size={80} color={'#F53E3E'} />
//           </div>
//           <p className="text-xl text-primary font-semibold text-center">
//             {t('contactList.titleDeleteContact')}
//           </p>
//         </>
//       }
//     >
//       <div className="flex flex-col gap-4 w-[529px] justify-center items-center">
//         <p className="text-sm text-center text-secondary">
//           {t('contactList.deleteContactDescription')}
//         </p>
//         <div className="flex gap-4 w-full">
//           <DialogClose className="flex-1">
//             <div className="flex items-center justify-center flex-1 h-[40px] font-medium w-full bg-secondary-foreground_crm text-base text-filter rounded-xl hover:bg-secondary-foreground_crm hover:text-tertiary-foreground">
//               {t('common.button.cancel')}
//             </div>
//           </DialogClose>
//
//           <Button
//             disabled={deleteListContactMutation.isPending}
//             onClick={() => deleteListContactMutation.mutate()}
//             variant={'delete'}
//             className="flex-1 h-[40px] w-full"
//           >
//             {deleteListContactMutation.isPending ? (
//               <RiLoader2Line className="mx-auto animate-spin" />
//             ) : (
//               t('common.button.remove')
//             )}
//           </Button>
//         </div>
//       </div>
//     </Modal>
//   );
// };
