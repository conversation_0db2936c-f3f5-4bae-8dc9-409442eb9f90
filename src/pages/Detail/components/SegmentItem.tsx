import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { RiCloseLine } from '@remixicon/react';
import { TContactItems } from '@/types/contactList';
import { TagFillIcon } from '@/assets/TagFillIcon';

type TSegment = {
  segmentName: string;
  segmentId: number;
  segmentType?: string;
  detailContact?: TContactItems;
  setDetailContact: (value: TContactItems) => void;
  color?: string;
};
export const SegmentItem: React.FC<TSegment> = ({ ...props }: TSegment) => {
  const { segmentName, detailContact, color, setDetailContact } = props;
  const handleRemoveSegment = () => {
    if (detailContact) {
      const detailSegmentIds = detailContact?.segment_ids ?? [];
      // Remove segment in list segment ui view
      const listSegmentRemove = detailContact.segments.filter(
        (item) => item.segment.id.toString() !== props.segmentId.toString(),
      );

      // Get list segment id to remove in api
      const segment_ids = detailContact.segments
        .filter((item) => item.segment.id.toString() === props.segmentId.toString())
        .map((item) => item.segment.id.toString());

      setDetailContact({
        ...detailContact,
        segments: listSegmentRemove,
        segment_ids: detailSegmentIds.concat(segment_ids),
      });
    }
  };
  return (
    <div className="flex items-center justify-center w-fit h-[26px] text-center p-1 rounded-xl truncate bg-secondary-foreground_crm">
      {color && <TagFillIcon color={color} />}
      <p className="ml-[5px] truncate font-normal text-secondary text-sm">{segmentName}</p>
      <Button
        className="h-[24px] right-1.5 top-0 p-0 m-0 bg-transparent hover:bg-transparent"
        type={'button'}
        onClick={() => handleRemoveSegment()}
      >
        <RiCloseLine color={'#515667'} size={20} />
      </Button>
    </div>
  );
};
