import { useTranslation } from 'react-i18next';
import { TContactCreate, TContactItems } from '@/types/contactList';
import React from 'react';
import { ItemInfo } from '@/pages/Detail/components/ItemInfo';
import { handleCheckEmptyValue, handleFormatPhone } from '@/utils/helper';
import { useCallContext } from '@/pages/context/CallContext';
import { RiPhoneLine } from '@remixicon/react';
import { reminderApi } from '@/apis/reminder';

type TContactInformationProps = {
  detailContact?: TContactItems;
  refetchDetail: () => void;
};

const ContactInformation: React.FC<TContactInformationProps> = ({
  ...props
}: TContactInformationProps) => {
  const { detailContact, refetchDetail } = props;
  const { t } = useTranslation();
  const { setContactCall } = useCallContext();
  const handleRemoveReminder = (contact: TContactCreate) => {
    const targetDate = new Date(contact.reminder?.time_reminder || '');
    const now = new Date();
    const timeDifference = targetDate.getTime() - now.getTime();
    const TEN_MINUTES = 10 * 60 * 1000;
    if (timeDifference <= TEN_MINUTES) {
      reminderApi.remove(contact.reminder?.id.toString() || '').then(() => {
        refetchDetail();
      });
    }
    setContactCall(contact);
  };

  return (
    <div className="w-full text-left border-t border-tertiary mt-2 pt-2">
      <p className="mb-4 font-medium text-md text-primary">
        {t('contactDetail.contactInformation')}
      </p>
      <div className="flex justify-between gap-4">
        {detailContact?.datatype === 'AUDIENCE' && (
          <button
            onClick={() => handleRemoveReminder(detailContact as TContactCreate)}
            className="flex flex-1 justify-between gap-1  text-left group"
          >
            <ItemInfo
              className="flex-1 min-w-0"
              title={t('common.phoneNumber')}
              value={
                <>
                  {handleCheckEmptyValue(handleFormatPhone(detailContact?.phone_number ?? ''))}{' '}
                  <RiPhoneLine color={'#27923A'} className="" />
                </>
              }
              classNameContent="flex gap-1 items-center group-hover:text-success-hover group-hover:cursor-pointer [&_svg]:group-hover:scale-125 [&_svg]:duration-300"
            />
          </button>
        )}

        <ItemInfo
          className="flex-1 min-w-0"
          title={t('common.email')}
          value={handleCheckEmptyValue(detailContact?.email) ?? ''}
        />
      </div>
      <ItemInfo
        className="w-full"
        title={t('common.address')}
        isTruncate={false}
        value={handleCheckEmptyValue(detailContact?.person_address) ?? ''}
      />
      <ItemInfo
        className="w-full"
        title={t('common.province')}
        value={handleCheckEmptyValue(detailContact?.person_province) ?? ''}
      />
      <ItemInfo
        className="w-full"
        title={t('common.company')}
        isTruncate={false}
        value={handleCheckEmptyValue(detailContact?.company_name) ?? ''}
      />
      <ItemInfo
        className="w-full"
        title={t('common.position')}
        value={handleCheckEmptyValue(detailContact?.position) ?? ''}
      />
    </div>
  );
};

export default ContactInformation;
