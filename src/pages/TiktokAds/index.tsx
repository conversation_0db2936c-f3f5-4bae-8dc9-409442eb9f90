import EmptyTiktokDataView from './views/EmptyTiktokDataView';
import ContainerTiktokAds from './components/ContainerTiktokAds';
import Breadcrumb from '@/components/Breadcrumb';
import HeaderTiktok from '@/pages/TiktokAds/components/HeaderTiktok';
import { useTiktokContext } from '@/pages/TiktokAds/context/TiktokAuthContext';

const TiktokAdsPage = () => {
  const {  isAccountSelected } = useTiktokContext();
  return (
    <div className="w-full h-full flex flex-col">
      <Breadcrumb />

      {!isAccountSelected ? (
        <>
          <HeaderTiktok />
          <EmptyTiktokDataView />
        </>
      ) : (
        <ContainerTiktokAds />
      )}
    </div>
  );
};

export default TiktokAdsPage;
