import React, { useEffect, useState } from 'react';
// import { useTranslation } from 'react-i18next';
import DataTable from '@/components/table/DataTable';
import { ColumnDef } from '@tanstack/react-table';
import { ICustomAudienceResponse, ITiktokHistoryItem } from '@/types/tiktok';
import historyTiktokCol from '@/pages/TiktokAds/components/Column/historyTiktokCol';
import { TPagination } from '@/types/table';
import { Box } from '@/components/Box';
import { useQuery } from '@tanstack/react-query';
import { PAGE_SIZE, QUERY_KEY } from '@/utils/constants';
import { get } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { useTranslation } from 'react-i18next';

interface IHistoryTiktok {
  jobId: number;
  totalContact: number;
}

const HistoryTiktok: React.FC<IHistoryTiktok> = ({ jobId, totalContact }) => {
  const { t } = useTranslation();
  const [historyTiktokAds, setHistoryTiktokAds] = useState<ICustomAudienceResponse>({
    count: 0,
    items: [],
  });
  const [pagination, setPagination] = useState<TPagination>({
    currentPage: 1,
    pageSize: PAGE_SIZE,
  });
  const { data: historyResponse, isLoading: loadingContactSegment } = useQuery({
    enabled: !!jobId,
    queryKey: [QUERY_KEY.TIKTOK_HISTORY, pagination],
    queryFn: () =>
      get<ICustomAudienceResponse>({
        endpoint: ENDPOINTS.custom_audience.history(jobId),
        params: {
          page: pagination.currentPage,
          limit: pagination.pageSize,
        },
      }),
  });

  useEffect(() => {
    if (!!historyResponse?.data){
      const dataHistory = historyResponse?.data as ICustomAudienceResponse
      setHistoryTiktokAds(dataHistory)
    }

  }, [historyResponse?.data]);

  console.log({ historyResponse });
  console.log({ loadingContactSegment });

  return (
    <div>
      <DataTable
        data={[]}
        columns={historyTiktokCol() as ColumnDef<ITiktokHistoryItem>[]}
        total={0}
        loading={false}
        pagination={pagination}
        setPagination={setPagination}
        className="max-h-[352px] max-w-[618px]"
      />
      <Box className="mb-1 mt-3">
        <p>{t('common.totalSegments')}</p>
        <p>20</p>
      </Box>
      <Box>
        <p>{t('common.totalContacts')}</p>
        <span>{totalContact}</span>
      </Box>
    </div>
  );
};

export default HistoryTiktok;
