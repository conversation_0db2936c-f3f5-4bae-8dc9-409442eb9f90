import { formatDateTime } from '@/utils/helper';
import { ColumnDef } from '@tanstack/react-table';
import { t } from 'i18next';
import { ITiktokHistoryItem } from '@/types/tiktok';

const historyTiktokCol = (): ColumnDef<ITiktokHistoryItem>[] => {
  return [
    {
      accessorKey: 'date_time',
      header: () => (
        <div className="text-left w-full">
          {t('common.date')} & {t('common.time')}
        </div>
      ),
      cell: ({ row }) => {
        return (
          <div className="text-left">{formatDateTime(row.original.date_completed, '/', ':')}</div>
        );
      },
      size: 130,
    },
    {
      accessorKey: 'segment_added',
      header: () => <div className="text-left w-full">{t('segmentLog.recentSegmentAdded')}</div>,
      cell: ({ row }) => {
        return <div className="text-left font-medium">{row.original.segment_info.name}</div>;
      },
      size: 250,
    },
    {
      accessorKey: 'contacts',
      header: () => <div className="text-right w-full">{t('common.contacts')}</div>,
      cell: ({ row }) => {
        const status = row.original.status;
        switch (status) {
          case 'COMPLETED':
            return (
              <div className="text-right font-medium text-big360Color-success-500">
                +{Number(row.original.records).toLocaleString()}
              </div>
            );
          case 'FAILED':
            return <div className="text-right font-medium text-big360Color-danger-500">0</div>;
          default:
            return <div className="text-right font-medium">0</div>;
        }
      },
      size: 100,
    },
  ];
};

export default historyTiktokCol;
