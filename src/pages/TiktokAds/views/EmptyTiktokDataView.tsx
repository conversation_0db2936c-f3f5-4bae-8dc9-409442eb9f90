import ConnectModal from '@/components/ConnectModal';
import { useMutation } from '@tanstack/react-query';
import SelectAccountModal from '@/components/SelectAccountModal';
import { useState } from 'react';
import { tiktokOauthApi } from '@/apis/tiktokOauth';
import { useTiktokContext } from '@/pages/TiktokAds/context/TiktokAuthContext';
import { useTranslation } from 'react-i18next';
import SelectAccountPopup from '@/pages/TiktokAds/components/SelectAccountPopup';
import { ROOT_ROUTE } from '@/constants/router';

const EmptyTiktokDataView = () => {
  const { t } = useTranslation();
  const [showModal, setShowModal] = useState(false);

  const { loading, isAccountSelected, isLogin } = useTiktokContext();
  const mutation = useMutation({
    mutationFn: tiktokOauthApi.getTiktokOauthUrl,
    onSuccess: (data) => {
      window.location.href = data?.data?.auth_link ?? '/';
    },
  });
  const handleLogin = () => mutation.mutate(ROOT_ROUTE.tiktok['']);

  return (
    <div className="flex flex-col items-center gap-6 my-32">
      {!isLogin && (
        <ConnectModal
          disabled={mutation.isSuccess}
          onConnect={handleLogin}
          value={'Tiktok'}
          loading={mutation.isPending}
        />
      )}
      {!isAccountSelected && isLogin && (
        <SelectAccountModal
          disabled={mutation.isSuccess}
          onSelect={() => setShowModal(true)}
          value={'Tiktok Ads'}
          loading={loading}
          buttonText={t('common.button.selectTiktokAccount')}
        />
      )}
      {showModal && <SelectAccountPopup open={showModal} setOpen={setShowModal} />}
    </div>
  );
};

export default EmptyTiktokDataView;
