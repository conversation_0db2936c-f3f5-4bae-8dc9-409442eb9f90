import useCalling, { default_user_call, TUseCalling } from '@/hooks/useCalling';
import { TContactCreate } from '@/types/contactList';
import { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import CallPanel from '../ContactList/components/CallPanel';
import { getSelector, useAppDispatch, useAppSelector } from '@/hooks/reduxHooks';
import { getPhoneBought } from '@/store/phoneBought/action';
import { toast } from '@/hooks/use-toast';
import { Trans, useTranslation } from 'react-i18next';
import { useFBContext } from '@/pages/Facebook/context/FbAuthContext';
import { fDiffMinutes } from '@/utils/helper';
import { getUsedPhone } from '@/store/usedPhone/usedPhoneSlice';

type TCallContext = {
  setContactCall: (item: TContactCreate) => void;
  removeContactCall: () => void;
  contactCall: TContactCreate | null;
  message: TUseCalling;
};

const CallContext = createContext<TCallContext>({
  setContactCall: () => {},
  removeContactCall: () => {},
  contactCall: null,
  message: default_user_call,
});

const CallProvider = ({ children }: { children: ReactNode }) => {
  const dispatch = useAppDispatch();
  const [contactCall, setContactCall] = useState<TContactCreate | null>(null);
  const { listPages } = useFBContext();
  const isSelect = listPages.find((item) => item.selected);
  const { t } = useTranslation();

  const { handleHangup, data, click2call, handleRecall, status } = useCalling();
  const { phone } = useAppSelector(getSelector('used_phone'));

  const isEnableToCall = phone.total_call_today <= 10 && phone.time_limit > 0;

  const [message, setMessage] = useState<TUseCalling>(default_user_call);

  useEffect(() => {
    if (!data) {
      return;
    }
    setMessage(data);
  }, [data]);

  const handleSelectContact = (item: TContactCreate) => {
    const diffMinutes = phone.date_expired ? fDiffMinutes(phone.date_expired) : 0;
    if (diffMinutes < 0) {
      setContactCall(null);
      dispatch(getPhoneBought());
      dispatch(getUsedPhone());
      toast({ description: t('contactList.expiredPhone'), status: 'error' });
      return;
    }
    if (isEnableToCall) {
      setContactCall(item);
      click2call(item.phone_number || '');
    } else if (!isSelect) {
      setContactCall(null);
      toast({ description: t('contactList.mustSelectHotLine'), status: 'error' });
    } else {
      setContactCall(null);
      toast({
        status: 'error',
        description: (
          <Trans
            id="limitAlert.description"
            name={'limitAlert.description'}
            i18nKey="limitAlert.description"
            values={{
              min: phone.total_call_today,
              max: 10,
              name: '',
              content: `calling time${phone.total_call_today > 1 ? 's' : ''}`,
            }}
            components={{ bold: <span className="font-bold" /> }}
          />
        ),
      });
    }
  };

  const contextValue: TCallContext = {
    setContactCall: handleSelectContact,
    removeContactCall: () => {
      setContactCall(null);
      handleHangup();
    },
    contactCall,
    message,
  };

  return (
    <CallContext.Provider value={contextValue}>
      {children}
      {contactCall && isEnableToCall && (
        <CallPanel
          status={status}
          message={message}
          onHangUp={handleHangup}
          onCall={() => handleRecall(contactCall.phone_number || '')}
        />
      )}
    </CallContext.Provider>
  );
};

const useCallContext = (): TCallContext => useContext(CallContext);

export { CallProvider, useCallContext };
