import { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { TSegment, TSegmentData } from '@/types/segment';
import { segmentApi } from '@/apis/segmentApi';
import { toast } from '@/hooks/use-toast';
import { useTranslation } from 'react-i18next';
import { get } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { MAX_LIMIT } from '@/utils/constants';
import { TMultiResponse } from '@/types/ResponseApi';

export type SegmentState = {
  count: number;
  items: TSegment[];
  loading: boolean;
  createSegment: (payload: { [key: string]: unknown }) => void;
  addSegment: (newSegment: TSegment) => void;
};

export const SegmentContext = createContext<SegmentState>({
  count: 0,
  items: [],
  loading: false,
  createSegment: () => {},
  addSegment: () => {},
});

const SegmentProvider = ({ children }: { children: ReactNode }) => {
  const [segment, setSegment] = useState<TSegmentData>({
    count: 0,
    items: [],
  });
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (segment.items.length === 0) {
      const fetchSegments = async () => {
        setLoading(true);
        try {
          // const res = await segmentApi.getList();
          // setSegment(res as TSegmentData);
          // setLoading(false);
          let hasMore = true;
          let currentPage = 1;
          let allSegmentData: TSegmentData = { count: 0, items: [] };
          while (hasMore) {
            const res = await get<TSegment>({
              endpoint: ENDPOINTS.segments.getList,
              params: {
                page: currentPage,
                limit: MAX_LIMIT,
                order_by: '-date_created',
              },
            });

            const resData = (res.data as TMultiResponse<TSegment>).data;
            allSegmentData = {
              count: resData?.count ?? 0,
              items: [...allSegmentData.items, ...(resData?.items ?? ([] as TSegment[]))],
            };
            if (
              (resData?.items?.length && resData?.items?.length < MAX_LIMIT) ||
              resData?.items?.length === 0
            ) {
              hasMore = false;
              setSegment(allSegmentData);
              setLoading(false);
            } else {
              currentPage++;
            }
          }
        } catch (error) {
          setLoading(false);
          return error;
        }
      };
      fetchSegments();
    }
  }, [segment.items.length]);

  const createSegment = async (payload: { [key: string]: unknown }) => {
    setLoading(true);
    try {
      const newItem = await segmentApi.createSegment(payload);
      setSegment({ count: segment.count + 1, items: [newItem, ...segment.items] });
      setLoading(false);
      toast({
        status: 'success',
        description: t('segment.segmentCreateSuccess'),
      });
    } catch (error) {
      setLoading(false);
      return error;
    }
  };

  const addSegment = (newSegment: TSegment) => {
    setSegment({ count: segment.count + 1, items: [newSegment, ...segment.items] });
  };

  return (
    <SegmentContext.Provider
      value={{
        count: segment?.count ?? 0,
        items: segment?.items ?? [],
        loading: loading,
        createSegment,
        addSegment,
      }}
    >
      {children}
    </SegmentContext.Provider>
  );
};

const useSegmentContext = (): SegmentState => useContext(SegmentContext);

export { SegmentProvider, useSegmentContext };
