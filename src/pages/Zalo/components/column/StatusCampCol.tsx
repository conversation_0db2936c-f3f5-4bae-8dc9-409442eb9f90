import { STATUS_CAMPAIGN } from '@/types/zalo';

type Props = {
  status: STATUS_CAMPAIGN;
};

const StatusCampCol = ({ status }: Props) => {
  const statusColor = (status: STATUS_CAMPAIGN) => {
    switch (status) {
      case 'completed':
        return 'bg-[#E0F8E3] text-[#205B2B]';
      case 'pending':
        return 'bg-[#FFFCEB] text-[#D98206]';
      case 'processing':
        return 'bg-[#EEF9FF] text-[#146BE1]';
    }
  };

  return (
    <div className={`text-xs capitalize text-center w-20 rounded-full p-1 ${statusColor(status)}`}>
      {status}
    </div>
  );
};
export default StatusCampCol;
