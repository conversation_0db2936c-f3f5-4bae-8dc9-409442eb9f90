import { TCampaignZalo } from '@/types/zalo';
import { ColumnDef } from '@tanstack/react-table';
import ActionCampaignCel from '../cel/ActionCampaignCel';
import { formatDateTime } from '@/utils/helper';
import { t } from 'i18next';
import StatusCampCol from './StatusCampCol';

const campaignCol = (): ColumnDef<TCampaignZalo>[] => {
  return [
    {
      accessorKey: 'name',
      header: () => <div>{t('common.zaloAds.campaign')}</div>,
      cell: ({ row }) => <div className="text-sm font-semibold">{row.original.name}</div>,
      size: 200,
    },
    {
      accessorKey: 'template',
      header: () => <div>{t('common.zaloAds.template')}</div>,
      cell: ({ row }) => (
        <div className="text-sm text-secondary">{row.original.template.template_name}</div>
      ),
      size: 200,
    },
    {
      accessorKey: 'segment',
      header: () => <div>{t('common.zaloAds.segment')}</div>,
      cell: ({ row }) => <div className="text-sm text-secondary">{row.original.segment.name}</div>,
      size: 200,
    },
    {
      accessorKey: 'total_contact',
      header: () => <div>{t('common.zaloAds.totalContact')}</div>,
      cell: ({ row }) => <div className="text-sm text-secondary">{row.original.total_contact}</div>,
      enableSorting: true,
      size: 160,
    },
    {
      accessorKey: 'status',
      header: () => <div>{t('common.zaloAds.status')}</div>,
      cell: ({ row }) => <StatusCampCol status={row.original.status} />,
      size: 160,
    },
    {
      accessorKey: 'date_created',
      header: () => <div>{t('common.zaloAds.dateCreated')}</div>,
      cell: ({ row }) => (
        <div className="text-sm text-secondary">
          {formatDateTime(row.original.date_created, '/', ':')}
        </div>
      ),
      enableSorting: true,
      size: 144,
    },
    {
      accessorKey: 'send_time',
      header: () => <div>{t('common.zaloAds.sendTime')}</div>,
      cell: ({ row }) => (
        <div className="text-sm text-secondary">
          {formatDateTime(row.original.send_time, '/', ':')}
        </div>
      ),
      enableSorting: true,
      size: 144,
    },
    {
      accessorKey: 'success',
      header: () => <div>{t('common.zaloAds.success')}</div>,
      cell: ({ row }) => <div className="text-sm text-secondary">{row.original.success}</div>,
      size: 110,
    },
    {
      accessorKey: 'failure',
      header: () => <div>{t('common.zaloAds.failure')}</div>,
      cell: ({ row }) => <div className="text-sm text-secondary">{row.original.failure}</div>,
      size: 110,
    },
    {
      accessorKey: 'action',
      size: 136,
      header: () => null,
      cell: ({ row }) => <ActionCampaignCel row={row.original} />,
      meta: { sticky: 'right' },
    },
  ];
};
export default campaignCol;
