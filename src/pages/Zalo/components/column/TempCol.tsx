import { Template } from '@/types/zalo';
import { formatDateTime } from '@/utils/helper';
import { RiBuildingLine, RiFileMarkedLine, RiTimeLine } from '@remixicon/react';
import { ColumnDef } from '@tanstack/react-table';
import TempStatusCel from '../cel/TempStatusCel';
import { t } from 'i18next';
import DeleteTempCel from '../cel/DeleteTempCel';
import { Link } from 'react-router-dom';
import { ROOT_PATH, ROOT_ROUTE } from '@/constants/router';

type Props = {
  onRemove: (id: string) => void;
};

export const tempCol = ({ onRemove }: Props): ColumnDef<Template>[] => {
  return [
    {
      accessorKey: 'template_id',
      header: () => null,
      cell: ({ row }) => (
        <div className="flex flex-col gap-3">
          <Link
            to={ROOT_PATH + '/' + ROOT_ROUTE.zalo.zns + '/' + row.original.template_id}
            className="flex items-center gap-1 capitalize hover:text-brand font-medium"
          >
            <RiFileMarkedLine size={20} />
            {row.original.template_name}
          </Link>
          <div className="text-sm text-secondary font-medium">
            {t('common.zaloAds.znsId')}: {row.original.template_id}
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'date_created',
      header: () => null,
      cell: ({ row }) => (
        <div className="flex flex-col gap-3">
          <div className="flex items-center gap-1">
            <RiBuildingLine size={20} /> {t('common.zaloAds.znsTemplateName')}
          </div>
          <div className="flex items-center gap-1">
            <RiTimeLine size={20} />
            {formatDateTime(row.original.date_created, '/', ':')}
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'status',
      header: () => null,
      cell: ({ row }) => <TempStatusCel status={row.original.status} />,
    },
    {
      accessorKey: 'action',
      header: () => null,
      cell: ({ row }) => <DeleteTempCel temp={row.original} onRemove={onRemove} />,
      size: 32,
    },
  ];
};
