import { ZaloTemplate } from '@/constants/zalo/validate';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';

import { Control } from 'react-hook-form';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import { t } from 'i18next';

type Temp = Omit<ZaloTemplate, 'table' | 'actionButton' | 'logo' | 'isAcceptTerm' | 'contentKeys'>;

type Props = {
  control: Control<ZaloTemplate>;
  name: keyof Temp;
  label?: string;
};

const TextareaLabel = ({ control, name, label }: Props) => {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className="flex flex-col gap-1 items-start">
          {label && (
            <FormLabel>
              {label} <span className="text-red-500">*</span>
            </FormLabel>
          )}
          <FormControl>
            <Textarea
              className={cn(control._formState.errors[name] && 'border-red-500')}
              {...field}
              placeholder={t('common.zaloAds.thanksForUse')}
            />
          </FormControl>
          <FormMessage className="mt-2" />
        </FormItem>
      )}
    />
  );
};
export default TextareaLabel;
