import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { SelectContent, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ZaloTemplate } from '@/constants/zalo/validate';
import { Select } from '@radix-ui/react-select';
import { t } from 'i18next';
import { Control } from 'react-hook-form';

type Props = {
  control: Control<ZaloTemplate>;
};

const SelectType = ({ control }: Props) => {
  const FLabelItem = ({ name }: { name: string }) => (
    <FormLabel className="capitalize">
      {name}
      <span className="text-red-500">*</span>
    </FormLabel>
  );

  return (
    <div>
      <FormField
        control={control}
        name="template_type"
        render={({ field }) => (
          <FormItem className="flex flex-col items-start gap-1">
            <FLabelItem name={t('common.zaloAds.type')} />
            <Select onValueChange={field.onChange}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select button type" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {/* {actionButtonOptions[buttonType].map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))} */}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};
export default SelectType;
