import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { ZaloTemplate } from '@/constants/zalo/validate';
import { t } from 'i18next';
import { UseFormGetValues } from 'react-hook-form';

type Props = {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  getValue: UseFormGetValues<ZaloTemplate>;
};

const ConfirmSendPopup = ({ open, getValue, onClose, onConfirm }: Props) => {
  const errors = getValue('keyError');
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogTrigger asChild></DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t('common.zaloAds.confirmParamType')}</DialogTitle>
          <DialogDescription>{t('common.zaloAds.confirmParamTypeDesc')}</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {errors?.map((error, index) => (
            <div key={index} className="text-yellow-500">
              - {error}
            </div>
          ))}
        </div>
        <DialogFooter>
          <Button
            size={'lg'}
            variant={'outline'}
            onClick={onClose}
            className="px-3 py-1 rounded-xl"
          >
            {t('common.button.cancel')}
          </Button>
          <Button size={'lg'} onClick={onConfirm} className="px-3 py-1 rounded-xl">
            {t('common.button.confirm')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
export default ConfirmSendPopup;
