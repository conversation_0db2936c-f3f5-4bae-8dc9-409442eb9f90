import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { paramFields } from '@/constants/zalo';
import { ZaloTemplate } from '@/constants/zalo/validate';
import { cn } from '@/lib/utils';
import { RiAddLine, RiErrorWarningLine } from '@remixicon/react';
import { t } from 'i18next';
import { useCallback, useEffect, useMemo } from 'react';
import { Control, UseFormSetValue, useWatch } from 'react-hook-form';

type Temp = Omit<ZaloTemplate, 'table' | 'actionButton' | 'images' | 'logo' | 'keyError'>;

type Props = {
  control: Control<ZaloTemplate>;
  name: keyof Temp;
  label?: string;
  setFormValue: UseFormSetValue<ZaloTemplate>;
};

const ContextTemplate = ({ control, name, label, setFormValue }: Props) => {
  const value = useWatch({ control, name: name });

  const options = useMemo(
    () =>
      paramFields.map((option) => ({
        label: option.label,
        value: option.value,
      })),
    [],
  );

  const getErrors = useCallback((text: string) => {
    return text
      .split(' ')
      .filter((word) => word.includes('<') && word.includes('>'))
      .map((word) => word.slice(1, word.length - 1))
      .filter((key) => !paramFields.some((param) => param.value === key));
  }, []);

  const addField = useCallback(
    (fieldValue: string) => {
      const newValue = value + ' ' + `<${fieldValue}>` + ' ';
      setFormValue(name, newValue, { shouldValidate: true });
    },
    [value, setFormValue, name],
  );

  const handleTextChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const newValue = e.target.value;
      setFormValue(name, newValue, { shouldValidate: true });
    },
    [setFormValue, name],
  );

  // Update errors
  useEffect(() => {
    const errors = getErrors(value);
    if (errors.length > 0) {
      setFormValue('keyError', errors, { shouldValidate: true });
    }
  }, [value, getErrors, setFormValue]);

  // Memoize errors for display
  const errors = useMemo(() => getErrors(value), [value, getErrors]);

  return (
    <div className="w-full flex flex-col gap-2">
      {label && (
        <div className="flex items-center gap-1">
          {label} <span className="text-red-500">*</span>
        </div>
      )}

      <div className="flex items-center justify-start gap-2 flex-wrap">
        <span className="flex-shrink-0 text-sm">{t('common.zaloAds.addKey')}:</span>
        {options.map((option) => (
          <Button
            onClick={() => addField(option.value)}
            className="py-0.5 gap-1 px-3 rounded-xl"
            size="sm"
            type="button"
            key={option.value}
          >
            <RiAddLine size={12} /> {option.label}
          </Button>
        ))}
      </div>

      <Textarea
        className={cn(
          'focus-visible:outline-none',
          control._formState.errors[name]
            ? 'border-red-500'
            : 'focus:shadow-medium focus:border-primary-crm ',
        )}
        onChange={handleTextChange}
        value={value}
        placeholder={t('common.zaloAds.thanksForUse')}
      />

      {errors.length > 0 && (
        <>
          <div className="text-yellow-500 text-xs flex items-center justify-start gap-1">
            <RiErrorWarningLine size={16} />
            {t('common.zaloAds.errorKey')}
          </div>
          {errors.map((error, index) => (
            <div key={index} className="text-yellow-500 text-start text-xs">
              - {error}
            </div>
          ))}
        </>
      )}
    </div>
  );
};

export default ContextTemplate;
