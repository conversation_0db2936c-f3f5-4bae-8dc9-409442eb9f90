import { RiInbox2Line } from '@remixicon/react';
import { t } from 'i18next';

const EmptyData = () => {
  return (
    <div className="flex items-center justify-center flex-col gap-6 mt-52">
      <div className="h-20 w-20 rounded-full flex items-center justify-center bg-secondary-foreground_crm">
        <RiInbox2Line size={40} />
      </div>
      <div className="flex flex-col items-center">
        <span className="font-medium leading-[26px]">{t('common.zaloAds.noData')}</span>
        <span className="text-sm text-secondary">{t('common.zaloAds.subNoData')}</span>
      </div>
    </div>
  );
};
export default EmptyData;
