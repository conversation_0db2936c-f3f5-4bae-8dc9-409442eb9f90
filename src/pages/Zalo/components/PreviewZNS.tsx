import Wrapper from './Wrapper';
import { Button } from '@/components/ui/button';
import { t } from 'i18next';
import { cn } from '@/lib/utils';
import { actionButton } from '@/constants/zalo';
import { ZaloTemplate } from '@/constants/zalo/validate';
import { useState } from 'react';
import { Switch } from '@/components/ui/switch';
import { useFormContext } from 'react-hook-form';

const PreviewZNS = () => {
  const [mode, setMode] = useState<'dark' | 'light'>('light');
  const darkModeToggle = () => setMode(mode === 'dark' ? 'light' : 'dark');
  const form = useFormContext<ZaloTemplate>();

  const handleCreateGotoBusiness = () => {};
  const getCostButton = (btnType: 'main' | 'sub', type: string) => {
    return actionButton.find((option) => option.value === type)?.price[btnType];
  };

  const subButton = form.watch('actionButton.sub');
  const mainButton = form.watch('actionButton.main');
  const content = form.watch('content');
  const processText = (text: string) => {
    const parts = text.split(/(<[^>]+>)/g);

    return (
      <div className="whitespace-pre-wrap font-normal text-sm">
        {parts.map((part, index) => {
          if (part.startsWith('<') && part.endsWith('>')) {
            const content = part.slice(1, -1);
            return (
              <strong key={index} className="whitespace-pre-wrap">
                &lt;{content}&gt;
              </strong>
            );
          }
          return <span key={index}>{part}</span>;
        })}
      </div>
    );
  };

  return (
    <Wrapper
      isRequired={false}
      className={cn('w-[330px] pt-6 px-3 sticky top-2')}
      variants={'secondary'}
      title={t('common.zaloAds.previewZns')}
      // rightContent={<LanguageSwitcher />}
    >
      <div className="flex items-center text-sm justify-between">
        {t('common.zaloAds.darkMode')}
        <Switch checked={mode === 'dark'} onCheckedChange={darkModeToggle} />
      </div>
      <div
        className={cn(
          'space-y-2 bg-card text-start p-3 rounded-xl text-sm font-medium',
          mode === 'dark' && 'bg-[#02111e] text-white',
        )}
      >
        {form.getValues(`logo.${mode}`) && form.watch(`logo.${mode}.local_url`) && (
          <img
            className="w-2/3 h-24 object-cover rounded-md"
            src={form.watch(`logo.${mode}.local_url`)}
            alt="light"
          />
        )}
        <div className="space-y-2 text-sm">
          <h2 className="font-medium">{form.watch('title')}</h2>
          <div className="overflow-auto text-sm">{processText(content)}</div>
        </div>
        {form.watch('table').map((item, index) => (
          <TextLabel
            key={index}
            label={item.title + ':'}
            value={item.value ? item.value : item.content}
          />
        ))}
        <Button
          size={'lg'}
          onClick={handleCreateGotoBusiness}
          className="w-full bg-purple-600 hover:bg-purple-700"
        >
          {mainButton.label || t('common.zaloAds.goToBusinessPage')}
        </Button>
        {subButton && (
          <Button className="w-full" size={'lg'} variant={'secondary'}>
            {subButton?.label}
          </Button>
        )}
      </div>
      <TextLabel className="justify-between" label={'Customized ZNS:'} value={'$0'} />
      {mainButton.type && (
        <TextLabel
          className="justify-between"
          label={t('common.zaloAds.mainButton')}
          value={'$' + getCostButton('main', mainButton.type)}
        />
      )}
      {subButton?.type && (
        <TextLabel
          className="justify-between"
          label={t('common.zaloAds.subButton')}
          value={'$' + getCostButton('sub', subButton.type)}
        />
      )}
      <div className="space-y-2 pt-2 border-t">
        <TextLabel className="justify-between" label={'Estimated Cost:'} value={'$0'} />
      </div>
    </Wrapper>
  );
};

export default PreviewZNS;

const TextLabel = ({
  label,
  value,
  className,
}: {
  label: string;
  value: string;
  className?: string;
}) => {
  return (
    <div className={cn('flex text-sm font-medium gap-1', className)}>
      <span>{label}</span>
      <span>{value}</span>
    </div>
  );
};
