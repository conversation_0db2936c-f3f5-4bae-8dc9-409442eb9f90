import { Input } from '@/components/ui/input';
import { getTypeValue } from '@/constants/zalo';
import { ZaloTemplate } from '@/constants/zalo/validate';
import { get } from 'lodash';
import { useFormContext } from 'react-hook-form';
import FLabelItem from './FLabelItem';
import { useEffect, useState } from 'react';
import { regex } from '@/constants/regex';

type Props = {
  buttonType: 'main' | 'sub';
};

const InputRegex = ({ buttonType }: Props) => {
  const form = useFormContext<ZaloTemplate>();
  const [inputValue, setValue] = useState('');
  const [isChange, setIsChange] = useState(false);
  const type = getTypeValue(form.getValues(`actionButton.${buttonType}.type`));

  useEffect(() => {
    const validateInput = () => {
      if (!isChange) return;
      let error: string | undefined;
      if (type === 'phone') {
        error = regex.phone.test(inputValue) ? undefined : 'Invalid phone number';
      } else if (type === 'link') {
        error = regex.url.test(inputValue) ? undefined : 'Invalid URL';
      }

      if (error) {
        form.setError(`actionButton.${buttonType}.value`, {
          type: 'manual',
          message: error,
        });
      } else {
        form.clearErrors(`actionButton.${buttonType}.value`);
        form.setValue(`actionButton.${buttonType}.value`, inputValue);
      }
    };

    validateInput();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [inputValue, type, buttonType]);

  return (
    <div className="flex flex-col gap-1 items-start">
      <FLabelItem name={type || ''} />
      <Input
        className={form.formState.errors?.actionButton?.[buttonType]?.value ? 'border-red-500' : ''}
        value={inputValue}
        onChange={(e) => {
          setIsChange(true);
          setValue(e.currentTarget.value);
        }}
      />
      <div className="text-xs text-red-500">
        {get(form.formState.errors, `actionButton.${buttonType}.value.message`)}
      </div>
    </div>
  );
};

export default InputRegex;
