import { Button } from '@/components/ui/button';
import { FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { actionButtonOptions, getTypeValue } from '@/constants/zalo';
import { ZaloTemplate } from '@/constants/zalo/validate';
import { cn } from '@/lib/utils';
import { RiAddLine, RiSubtractLine } from '@remixicon/react';
import { t } from 'i18next';
import { useEffect, useState } from 'react';
import { get } from 'lodash';
import { Control, UseFormSetValue, useWatch } from 'react-hook-form';
import FLabelItem from './FLabelItem';

type Props = {
  control: Control<ZaloTemplate>;
  setValue: UseFormSetValue<ZaloTemplate>;
};

const ActionButtonSelect = ({ control, setValue }: Props) => {
  const subButton = useWatch({
    control: control,
    name: 'actionButton.sub',
  });
  const [fButton, setFButton] = useState<('main' | 'sub')[]>(['main']);

  useEffect(() => {
    if (subButton) setFButton(['main', 'sub']);
    return;
  }, [subButton]);

  const handleRemoveButton = () => {
    setValue('actionButton.sub', undefined);
    setFButton(['main']);
  };

  return (
    <div className="flex flex-col gap-4 text-sm [&_input]:text-sm [&_input]:h-10 [&_input]:rounded-xl [&_input]:p-3">
      {fButton.map((buttonType) => (
        <div key={buttonType} className="space-y-3">
          <FormField
            control={control}
            name={`actionButton.${buttonType}.type`}
            render={({ field }) => (
              <>
                <div className="text-sm font-medium capitalize flex items-center justify-between">
                  {buttonType} {t('common.zaloAds.button')}
                  <Button
                    className={cn(
                      'text-red-500 hover:text-red-500 p-1',
                      buttonType === 'main' && 'hidden',
                    )}
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={handleRemoveButton}
                  >
                    <RiSubtractLine size={20} />
                  </Button>
                </div>
                <FormItem className="flex flex-col items-start gap-1">
                  <FLabelItem name={t('common.zaloAds.type')} />
                  <Select value={field.value} onValueChange={field.onChange}>
                    <FormControl>
                      <SelectTrigger
                        className={
                          get(control._formState.errors, `actionButton.${buttonType}.type`) &&
                          'border-red-500'
                        }
                      >
                        <SelectValue placeholder={t('placeHolder.selectTypeBtn')} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {actionButtonOptions[buttonType].map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              </>
            )}
          />
          <div className="pl-4 border-l flex flex-col gap-2">
            <FormField
              control={control}
              name={`actionButton.${buttonType}.label`}
              render={({ field }) => (
                <FormItem className="flex flex-col items-start gap-1">
                  <FLabelItem name={t('common.zaloAds.label')} />
                  <FormControl>
                    <Input
                      className={cn(
                        get(control._formState.errors, `actionButton.${buttonType}.label`)
                          ? 'border-red-500'
                          : 'focus:shadow-medium focus:border-primary-crm',
                      )}
                      placeholder="Enter button label"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name={`actionButton.${buttonType}.value`}
              render={({ field }) => (
                <FormItem className="flex flex-col items-start gap-1">
                  <FLabelItem
                    name={
                      getTypeValue(control._getWatch(`actionButton.${buttonType}.type`)) || 'Value'
                    }
                  />
                  <FormControl>
                    <Input
                      className={cn(
                        get(control._formState.errors, `actionButton.${buttonType}.value`)
                          ? 'border-red-500'
                          : 'focus:shadow-medium focus:border-primary-crm',
                      )}
                      placeholder="Enter button label"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
      ))}
      {fButton.length < 2 && (
        <Button
          type="button"
          variant="outline"
          size="sm"
          className="w-fit bg-[#F0F0F0] rounded-md border-none text-sm font-medium px-2 py-1"
          onClick={() => setFButton([...fButton, 'sub'])}
        >
          <RiAddLine />
          {t('common.zaloAds.addButton')}
        </Button>
      )}
    </div>
  );
};
export default ActionButtonSelect;
