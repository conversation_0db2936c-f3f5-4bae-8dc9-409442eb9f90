import { RiInformationLine } from '@remixicon/react';
import { t } from 'i18next';

const Label = () => {
  return (
    <>
      <div className="flex items-center">
        {item(t('common.zaloAds.parameter'), false)}
        <RiInformationLine size={16} />
      </div>
      <div className="w-full grid grid-cols-3 gap-2">
        {item(t('common.zaloAds.paraName'), true)}
        {item(t('common.zaloAds.paraType'), true)}
        {item(t('common.zaloAds.paraValue'), true)}
      </div>
    </>
  );
};
export default Label;

const item = (value: string, show: boolean) => (
  <div className="flex items-center gap-1">
    {value}
    {show && <span className="text-red-500">*</span>}
  </div>
);
