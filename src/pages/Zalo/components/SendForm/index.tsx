import SetupParameter from './SetupParameter';
import Label from './Label';
import { TZaloTempBeforeSchema } from '@/constants/zalo/validate';
import { Textarea } from '@/components/ui/textarea';
import { t } from 'i18next';
import { Checkbox } from '@/components/ui/checkbox';
import { SubmitHandler, useFormContext } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { RiArrowLeftLine, RiArrowRightLine, RiLoader2Line } from '@remixicon/react';
import { zaloApi } from '@/apis/zaloApi';
import { useContext, useState } from 'react';
import { OauthContext } from '../../Context/OauthContext';
import { customForm } from '@/constants/zalo/form';
import ContentParam from './ContentParam';
import { toast } from '@/hooks/use-toast';
import { FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { cn } from '@/lib/utils';
import { TERM_URL } from '@/constants/zalo';
import { useNavigate } from 'react-router-dom';
import { ROOT_PATH, ROOT_ROUTE } from '@/constants/router';

type Props = {
  onBack: () => void;
  disableSend?: boolean;
  isUpdate?: boolean;
};

const SendForm = ({ onBack, disableSend = false, isUpdate = false }: Props) => {
  const navigate = useNavigate();
  const { data: auth } = useContext(OauthContext);
  const [loading, setLoading] = useState(false);
  const form = useFormContext<TZaloTempBeforeSchema>();
  const ZNS_ROUTER = `${ROOT_PATH}/${ROOT_ROUTE.zalo.zns}`;

  const handleCreate = async (data: TZaloTempBeforeSchema) => {
    if (!auth?.oa_id) return;
    try {
      setLoading(true);
      const res = await zaloApi.createTemplate(auth.oa_id, customForm(data));
      toast({ status: 'success', description: res.message });
      onBack();
      navigate(ZNS_ROUTER);
      setLoading(false);
    } catch (error) {
      setLoading(false);
      return error;
    }
  };

  const handleUpdate = async (data: TZaloTempBeforeSchema) => {
    if (!auth) return;
    try {
      setLoading(true);
      const res = await zaloApi.updateTemplateDetail({
        oa_id: auth.oa_id,
        payload: customForm(data),
      });
      toast({ status: 'success', description: res.message });
      setLoading(false);
      onBack();
      navigate(ZNS_ROUTER);
    } catch (error) {
      setLoading(false);
      return error;
    }
  };

  const onClick: SubmitHandler<TZaloTempBeforeSchema> = async (data) => {
    if (isUpdate) {
      await handleUpdate(form.watch());
    } else {
      await handleCreate(data);
    }
  };

  return (
    <form onSubmit={form.handleSubmit(onClick)} className="flex flex-col gap-4">
      <Label />
      <SetupParameter control={form.control} />
      <ContentParam control={form.control} />
      <FormField
        control={form.control}
        name="note"
        render={({ field, fieldState }) => (
          <FormItem className="flex flex-col gap-2 items-start">
            <span className="text-sm font-medium">{t('common.zaloAds.note')}</span>
            <FormControl>
              <Textarea
                {...field}
                placeholder={t('common.zaloAds.enterNote')}
                className={cn(
                  'h-[137px] resize-none outline-none',
                  fieldState.error
                    ? 'border-red-500'
                    : 'focus:shadow-medium focus:border-primary-crm',
                )}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="isAcceptTerm"
        render={({ field, fieldState }) => (
          <FormItem className="flex flex-col gap-2 items-start">
            <div className="flex items-center space-x-2">
              <FormControl>
                <Checkbox
                  className={cn(fieldState.error && 'border-red-500')}
                  id="terms"
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <label
                htmlFor="terms"
                className="text-sm cursor-pointer font-medium leading-none text-secondary"
              >
                {t('common.zaloAds.term')}
                <a className="text-[#2C9EFF] underline" target="_blank" href={TERM_URL}>
                  {t('common.zaloAds.termAndCondition')}
                </a>
              </label>
            </div>
            <FormMessage className="text-red-500 text-xs" />
          </FormItem>
        )}
      />
      <div className="flex justify-between items-center">
        <Button
          onClick={onBack}
          className="p-3 rounded-xl"
          size={'lg'}
          variant={'ghost'}
          type={'button'}
        >
          <RiArrowLeftLine />
          {t('common.zaloAds.back')}
        </Button>
        <Button
          disabled={disableSend}
          className="p-3 rounded-xl bg-primary-crm hover:bg-primary-crm"
          type={'submit'}
          size={'lg'}
        >
          {loading ? (
            <RiLoader2Line size={16} className="animate-spin" />
          ) : (
            <>
              {t('common.zaloAds.complete')}
              <RiArrowRightLine />
            </>
          )}
        </Button>
      </div>
    </form>
  );
};
export default SendForm;
