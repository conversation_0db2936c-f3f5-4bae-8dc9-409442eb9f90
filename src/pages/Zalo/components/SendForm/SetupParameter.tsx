import { FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { getParamField, paramFieldsOptions } from '@/constants/zalo';
import { TZaloTempBeforeSchema } from '@/constants/zalo/validate';
import { cn } from '@/lib/utils';
import { t } from 'i18next';
import { Control, useFieldArray } from 'react-hook-form';
type Props = {
  control: Control<TZaloTempBeforeSchema>;
};

const SetupParameter = ({ control }: Props) => {
  const { fields } = useFieldArray({ name: 'table', control });

  return (
    <div className="flex flex-col gap-3">
      {fields.map((field, index) => (
        <div key={field.id} className="grid grid-cols-3 gap-2 items-start">
          <FormField
            control={control}
            name={`table.${index}.title`}
            render={({ field }) => (
              <FormItem className="col-span-1">
                <FormControl>
                  <Input className="bg-secondary" disabled {...field} />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name={`table.${index}.content`}
            render={({ field, fieldState }) => (
              <FormItem className={cn('flex flex-col items-start')}>
                <FormControl>
                  <Select
                    disabled={Boolean(field.value)}
                    onValueChange={field.onChange}
                    value={field.value}
                  >
                    <SelectTrigger
                      className={cn(
                        'disabled:bg-secondary-foreground_crm',
                        fieldState.error && 'border-red-500',
                      )}
                    >
                      <SelectValue placeholder={t('common.zaloAds.selectType')} />
                    </SelectTrigger>
                    <SelectContent>
                      {paramFieldsOptions.map((option) => (
                        <SelectItem key={option.label} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name={`table.${index}.value`}
            render={({ field, fieldState }) => (
              <FormItem className="flex flex-col items-start gap-1 col-span-1">
                <FormControl>
                  <Input
                    className={cn(
                      fieldState.error
                        ? 'border-red-500'
                        : 'focus:shadow-medium focus:border-primary-crm',
                    )}
                    maxLength={getParamField(fields[index].content)?.maxLength}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      ))}
    </div>
  );
};
export default SetupParameter;
