import { FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { getParamField, paramFieldsOptions } from '@/constants/zalo';
import { TContentKeys, TZaloTempBeforeSchema } from '@/constants/zalo/validate';
import { cn } from '@/lib/utils';
import { t } from 'i18next';
import { useEffect } from 'react';
import { Control, useFieldArray } from 'react-hook-form';

type Props = {
  control: Control<TZaloTempBeforeSchema>;
};

const ContentParam = ({ control }: Props) => {
  const { fields, replace } = useFieldArray({
    name: 'contentKeys',
    control: control,
  });

  const content = control._getWatch('content');

  useEffect(() => {
    const extractedKeys: string[] = content
      .split(' ')
      .filter((word: string) => word.includes('<') && word.includes('>'));
    const uniqueKeys = [...new Set(extractedKeys)];
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //@ts-expect-error
    const newFields: TContentKeys[] = uniqueKeys.map((item) => ({
      key: item,
      type: getParamField(item.replace(/[<>]/g, ''))?.value || '',
    }));
    replace(newFields);
  }, [content, replace]);

  return (
    <div className="flex flex-col gap-1">
      {fields.map((field, index) => (
        <div key={field.id} className="grid grid-cols-3 gap-2 items-start">
          <FormField
            control={control}
            name={`contentKeys.${index}.key`}
            render={({ field }) => (
              <FormItem className="col-span-1">
                <FormControl>
                  <Input
                    className="bg-secondary focus:shadow-medium focus:border-primary-crm"
                    disabled
                    {...field}
                  />
                </FormControl>
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name={`contentKeys.${index}.type`}
            render={({ field: { onChange, value }, fieldState }) => {
              return (
                <FormItem className={cn('flex flex-col items-start')}>
                  <FormControl>
                    <Select disabled={Boolean(value)} onValueChange={onChange} value={value}>
                      <SelectTrigger
                        className={cn(
                          'disabled:bg-secondary-foreground_crm',
                          fieldState.error && 'border-red-500',
                        )}
                      >
                        <SelectValue placeholder={t('common.zaloAds.selectType')} />
                      </SelectTrigger>
                      <SelectContent>
                        {paramFieldsOptions.map((option) => (
                          <SelectItem key={option.label} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              );
            }}
          />
          <FormField
            control={control}
            name={`contentKeys.${index}.value`}
            render={({ field, fieldState }) => (
              <FormItem className={cn('flex flex-col items-start')}>
                <FormControl>
                  <Input
                    className={cn(
                      fieldState.error
                        ? 'border-red-500'
                        : 'focus:shadow-medium focus:border-primary-crm',
                    )}
                    maxLength={
                      getParamField(control._getWatch(`contentKeys.${index}.type`))?.maxLength
                    }
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      ))}
    </div>
  );
};

export default ContentParam;
