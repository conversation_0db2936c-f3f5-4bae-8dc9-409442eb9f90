type Props = {
  value: string;
};

const ContentLabel = ({ value }: Props) => {
  const processText = () => {
    const words = value.split(' ');
    return words.map((word, index) => {
      if (word.startsWith('<') && word.endsWith('>')) {
        const content = word.slice(1, -1);
        return (
          <strong key={index}>
            {' '}
            {'<'}
            {content}
            {'>'}{' '}
          </strong>
        );
      }
      return word + ' ';
    });
  };

  return <div className="text-sm">{processText()}</div>;
};
export default ContentLabel;
