import { zalo<PERSON><PERSON> } from '@/apis/zaloApi';
import { ZaloTemplate } from '@/constants/zalo/validate';
import { RiImageAddLine, RiLoader2Line } from '@remixicon/react';
import { t } from 'i18next';
import { useContext, useEffect, useRef, useState } from 'react';
import { Control, useController } from 'react-hook-form';
import { OauthContext } from '../Context/OauthContext';
import { cn } from '@/lib/utils';

type Props = {
  variant: 'light' | 'dark';
  control: Control<ZaloTemplate>;
};

type LogoType = {
  type: string;
  media_id: string;
  local_url: string;
};

const ACCEPTED_IMAGE_TYPES = {
  mimeTypes: 'image/jpeg, image/png',
  mimeArray: ['image/jpeg', 'image/png'],
};

const AddImageButton = ({ variant, control }: Props) => {
  const { data: auth } = useContext(OauthContext);
  const inputRef = useRef<HTMLInputElement>(null);
  const text = t(variant === 'light' ? 'common.button.lightMode' : 'common.button.darkMode');
  const [image, setImage] = useState<File>();
  const [previewUrl, setPreviewUrl] = useState<string>();
  const [loading, setLoading] = useState(false);

  const {
    field: { onChange, value },
  } = useController<ZaloTemplate>({
    name: `logo.${variant}`,
    control,
  });

  useEffect(() => {
    if (!image || !auth?.oa_id) return;

    const fetchData = async () => {
      setLoading(true);
      try {
        const res = await zaloApi.uploadImage(auth.oa_id, {
          file: image,
        });

        const logoData: LogoType = {
          type: 'IMAGE',
          media_id: res.media_id,
          local_url: URL.createObjectURL(image),
        };

        onChange(logoData);
        setPreviewUrl(logoData.local_url);
        setLoading(false);
      } catch (error) {
        setLoading(false);
        return error;
      }
    };

    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [auth?.oa_id, image]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!ACCEPTED_IMAGE_TYPES.mimeArray.includes(file.type)) {
      alert('Please upload only JPEG or PNG images');
      return;
    }

    setImage(file);
  };

  const handleClick = () => {
    inputRef.current?.click();
  };

  return (
    <div
      className={cn(
        'relative h-[140px] w-full group rounded-2xl bg-card overflow-hidden',
        variant === 'dark' && 'bg-gray-900 text-white group',
      )}
    >
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/20">
          <RiLoader2Line className="animate-spin" />
        </div>
      )}
      <div className="absolute inset-0 px-4 py-3">
        <button
          type="button"
          onClick={handleClick}
          className={cn(
            'text-xs w-full h-full flex inset-0 bg-card opacity-40 rounded-md p-4 border-dashed border text-secondary items-center justify-center gap-1',
            variant === 'dark' && 'bg-gray-700 text-white',
            image && 'hidden group-hover:flex',
          )}
        >
          <RiImageAddLine size={16} />
          {text}
        </button>
      </div>
      {(previewUrl || (value as LogoType)?.local_url) && (
        <img
          src={previewUrl || (value as LogoType)?.local_url}
          alt="Preview"
          className="w-full h-full object-cover rounded-lg"
        />
      )}
      <input
        ref={inputRef}
        type="file"
        accept={ACCEPTED_IMAGE_TYPES.mimeTypes}
        onChange={handleFileChange}
        className="hidden"
      />
    </div>
  );
};

export default AddImageButton;
