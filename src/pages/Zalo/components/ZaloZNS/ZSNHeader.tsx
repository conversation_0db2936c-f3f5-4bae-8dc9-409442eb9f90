import TabMotion from '@/components/TabMotion';
import { t } from 'i18next';
import { useState } from 'react';

const ZSNHeader = () => {
  const options = [t('common.zaloAds.templateZns'), t('common.zaloAds.sendZns')];
  const [selected, setSelected] = useState(options[0]);

  return (
    <div className="mt-2 flex items-center justify-between">
      <TabMotion options={options} value={selected} onChange={setSelected} />
    </div>
  );
};
export default ZSNHeader;
