import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { ZaloTemplate } from '@/constants/zalo/validate';
import { cn } from '@/lib/utils';
import { Control } from 'react-hook-form';

type Temp = Omit<ZaloTemplate, 'table' | 'actionButton' | 'logo' | 'isAcceptTerm' | 'contentKeys'>;

type Props = {
  control: Control<ZaloTemplate>;
  name: keyof Temp;
  label?: string;
};
const InputRegister = ({ control, name, label }: Props) => {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className="flex flex-col items-start">
          {label && (
            <FormLabel className="mb-1 font-medium text-sm">
              {label} <span className="text-red-500">*</span>
            </FormLabel>
          )}
          <FormControl>
            <Input
              className={cn(
                'text-sm h-10 p-3 rounded-xl',
                control._formState.errors[name]
                  ? 'border-red-500'
                  : 'focus:shadow-medium focus:border-primary-crm',
              )}
              {...field}
            />
          </FormControl>
          <FormMessage className="mt-2" />
        </FormItem>
      )}
    />
  );
};
export default InputRegister;
