import { Dialog, DialogContent } from '@/components/ui/dialog';

const ZaloConnect = () => {
  return (
    <Dialog open={true}>
      <DialogContent className="sm:max-w-[425px] bg-blue-500 border-none">
        <div className="flex flex-col items-center justify-center py-10">
          <div className="relative w-24 h-24 mb-4">
            {/* Simplified Zalo-like logo */}
            <svg viewBox="0 0 100 100" className="absolute inset-0 w-full h-full text-white">
              <path
                fill="currentColor"
                d="M50 95C74.8528 95 95 74.8528 95 50C95 25.1472 74.8528 5 50 5C25.1472 5 5 25.1472 5 50C5 74.8528 25.1472 95 50 95Z"
              />
              <path
                fill="#2962FF"
                d="M68 38H54V34C54 31.7909 55.7909 30 58 30H64C66.2091 30 68 31.7909 68 34V38Z"
              />
              <path
                fill="#2962FF"
                d="M32 62H46V66C46 68.2091 44.2091 70 42 70H36C33.7909 70 32 68.2091 32 66V62Z"
              />
              <path fill="#2962FF" d="M32 38H46V58H32V38Z" />
              <path fill="#2962FF" d="M54 42H68V62H54V42Z" />
            </svg>
            {/* Animated loading spinner */}
            <svg
              className="absolute inset-0 w-full h-full animate-spin"
              viewBox="0 0 100 100"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle
                className="opacity-25"
                cx="50"
                cy="50"
                r="45"
                stroke="currentColor"
                strokeWidth="8"
                fill="none"
              />
              <circle
                className="opacity-75"
                cx="50"
                cy="50"
                r="45"
                stroke="currentColor"
                strokeWidth="8"
                strokeLinecap="round"
                fill="none"
                strokeDasharray="180"
                strokeDashoffset="180"
              />
            </svg>
          </div>
          <p className="text-white text-xl font-semibold animate-pulse">Connecting to Zalo</p>
        </div>
      </DialogContent>
    </Dialog>
  );
};
export default ZaloConnect;
