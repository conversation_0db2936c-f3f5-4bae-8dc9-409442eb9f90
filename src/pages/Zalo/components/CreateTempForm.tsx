import { t } from 'i18next';
import { Button } from '@/components/ui/button';
import Wrapper from '../components/Wrapper';
import TableSchemaEditor from '@/components/TableSchemaEditor';
import ActionButtonSelect from './ActionButtonSelect';
import { SubmitHandler, useFormContext } from 'react-hook-form';
import { ZaloTemplate } from '@/constants/zalo/validate';
import { useState } from 'react';
import { RiArrowLeftLine } from '@remixicon/react';
import { RiArrowRightLine } from 'react-icons/ri';
import UploadImage from './UploadImage';
import ContextTemplate from './ContextTemplate';
import ConfirmSendPopup from './ConfirmSendPopup';
import InputRegister from './InputRegister';
import { useNavigate } from 'react-router-dom';
import { ROOT_PATH, ROOT_ROUTE } from '@/constants/router';

type Props = {
  onClick: () => void;
};

const CreateTempForm = ({ onClick }: Props) => {
  const form = useFormContext<ZaloTemplate>();
  const [openDialog, setOpenDialog] = useState(false);
  const navigate = useNavigate();

  const onSubmit: SubmitHandler<ZaloTemplate> = (data) => {
    if (data.keyError && data.keyError.length > 0) {
      setOpenDialog(true);
      return;
    }
    onClick();
  };

  return (
    <>
      <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col gap-4">
        <InputRegister
          label={t('common.zaloAds.znsTemplateName')}
          control={form.control}
          name="template_name"
        />
        <InputRegister label={t('common.zaloAds.titleKey')} control={form.control} name="title" />
        <UploadImage control={form.control} getValues={form.getValues} />
        <ContextTemplate
          label={t('common.zaloAds.contentKey')}
          name="content"
          control={form.control}
          setFormValue={form.setValue}
        />
        <Wrapper isRequired={false} title={t('common.zaloAds.tableKey')}>
          <TableSchemaEditor control={form.control} />
        </Wrapper>
        <Wrapper
          title={t('common.zaloAds.actionButton')}
          subTtile={t('common.zaloAds.roleActionButton')}
        >
          <ActionButtonSelect setValue={form.setValue} control={form.control} />
        </Wrapper>
        <div className="flex justify-between items-center">
          <Button
            onClick={() => navigate(`${ROOT_PATH}/${ROOT_ROUTE.zalo['']}`)}
            className="p-3 rounded-xl"
            size={'lg'}
            variant={'ghost'}
            type={'button'}
          >
            <RiArrowLeftLine />
            {t('common.zaloAds.back')}
          </Button>
          <Button
            className="p-3 rounded-xl bg-primary-crm hover:bg-primary-crm"
            type={'submit'}
            size={'lg'}
          >
            {t('common.zaloAds.next')}
            <RiArrowRightLine />
          </Button>
        </div>
        <ConfirmSendPopup
          open={openDialog}
          onClose={() => setOpenDialog(!openDialog)}
          onConfirm={() => {
            setOpenDialog(!openDialog);
            onClick();
          }}
          getValue={form.getValues}
        />
      </form>
    </>
  );
};

export default CreateTempForm;
