import { cn } from '@/lib/utils';

type Props = {
  variants?: 'primary' | 'secondary';
  children: React.ReactNode;
  className?: string;
  title: string;
  isRequired?: boolean;
  subTtile?: string;
  rightContent?: React.ReactNode;
};

const Wrapper = ({
  children,
  className,
  variants = 'primary',
  title,
  isRequired = true,
  subTtile,
  rightContent,
}: Props) => {
  return (
    <div
      className={cn(
        'border rounded-2xl p-4 flex flex-col gap-3',
        variants === 'secondary' && 'bg-secondary',
        className,
      )}
    >
      <div className="font-medium leading-[26px] flex items-center justify-between">
        <div className="flex flex-col text-start">
          <div className="flex items-center gap-1">
            {title}
            {isRequired && <span className="text-red-500">*</span>}
          </div>
          {subTtile && <span className="text-secondary text-sm">{subTtile}</span>}
        </div>
        {rightContent}
      </div>
      {children}
    </div>
  );
};
export default Wrapper;
