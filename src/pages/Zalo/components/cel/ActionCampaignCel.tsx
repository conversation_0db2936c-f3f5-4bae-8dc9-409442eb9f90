import Modal from '@/components/Modal';
import { Button } from '@/components/ui/button';
import { TCampaignZalo } from '@/types/zalo';
import { RiDeleteBin6Line, RiSendPlane2Line } from '@remixicon/react';
import SendCampaignPopup from '../Campaign/SendCampaignPopup';
import DeleteCampPopup from '../Campaign/DeleteCampPopup';
import UpdateCampPopup from '../Campaign/UpdateCampPopup';

type Props = {
  row: TCampaignZalo;
};

const ActionCampaignCel = ({ row }: Props) => {
  return (
    <div className="flex items-center gap-2">
      <Modal
        trigger={
          <Button variant={'ghost'} className="h-8 w-8 hover:bg-secondary">
            <RiSendPlane2Line size={20} />
          </Button>
        }
        className="max-w-[529px]"
      >
        <SendCampaignPopup />
      </Modal>
      <UpdateCampPopup row={row} />
      <Modal
        trigger={
          <Button variant={'ghost'} className="h-8 w-8 hover:bg-secondary">
            <RiDeleteBin6Line size={20} />
          </Button>
        }
        className="max-w-[529px]"
      >
        <DeleteCampPopup />
      </Modal>
    </div>
  );
};

export default ActionCampaignCel;
