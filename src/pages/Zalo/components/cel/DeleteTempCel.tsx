import { ErrorIcon } from '@/assets/ErrorIcon';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog';
import { cn } from '@/lib/utils';
import { Template } from '@/types/zalo';
import { RiDeleteBin6Line, RiLoader2Line } from '@remixicon/react';
import { t } from 'i18next';
import { useContext, useState } from 'react';
import { OauthContext } from '../../Context/OauthContext';
import { zaloApi } from '@/apis/zaloApi';
import { toast } from '@/hooks/use-toast';

type Props = {
  temp: Template;
  onRemove: (id: string) => void;
};

const DeleteTempCel = ({ temp, onRemove }: Props) => {
  const { data: auth } = useContext(OauthContext);
  const [openModal, setOpenModal] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  const handleDelete = async () => {
    setLoading(true);
    try {
      if (!auth?.oa_id) return;
      const res = await zaloApi.deleteTemplate(auth.oa_id, temp.template_id);
      toast({ status: 'success', description: res.message });
      onRemove(temp.template_id);
      setOpenModal(false);
    } catch (error) {
      return error;
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={openModal} onOpenChange={setOpenModal}>
      <DialogTrigger asChild>
        <Button disabled={temp.status !== 'REJECT'} variant="ghost">
          <RiDeleteBin6Line
            className={cn('text-red-400', temp.status === 'REJECT' && 'text-red-500')}
            size={20}
          />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[529px]">
        <DialogHeader>
          <DialogDescription className="flex text-primary-crm flex-col justify-center items-center gap-2 w-full">
            <ErrorIcon />
            <span className="text-lg font-medium">{t('common.zaloAds.deleteTemplate')}</span>
            <span className="text-center text-sm text-secondary">
              {t('common.zaloAds.subDeleteTemplate')}
            </span>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="grid grid-cols-2 gap-4">
          <DialogClose className="w-full">
            <Button className="w-full" type="button" variant={'cancel'}>
              {t('common.button.cancel')}
            </Button>
          </DialogClose>
          <Button onClick={handleDelete} type="button" variant={'delete'}>
            {loading ? <RiLoader2Line className="animate-spin" /> : t('common.button.delete')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
export default DeleteTempCel;
