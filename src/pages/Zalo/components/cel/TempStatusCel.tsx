import { cn } from '@/lib/utils';
import { TEMPLATE_STATUS } from '@/types/zalo';
import { t } from 'i18next';

type Props = {
  status: TEMPLATE_STATUS;
};

const TempStatusCel = ({ status }: Props) => {
  return (
    <span
      className={cn(
        'inline-flex items-center rounded-md p-1 text-xs font-medium bg-gray-100 text-gray-500',
        {
          'bg-[#E0F8E3] text-[#205B2B]': status === 'ENABLE',
          'bg-[#FFFCEB] text-[#D98206]': status === 'PENDING_REVIEW',
          'bg-[#EEF9FF] text-[#146BE1]': status === 'DISABLE',
        },
      )}
    >
      {status ?? t('common.invalid')}
    </span>
  );
};

export default TempStatusCel;
