import { t } from 'i18next';
import InfoTemp from './InfoTemp';
import ImagePreview from './ImagePreview';
import TextLabel from './TextLabel';
import { useFormContext } from 'react-hook-form';
import { TZaloTempBeforeSchema } from '@/constants/zalo/validate';
import { TEMPLATE_STATUS } from '@/types/zalo';
import { Button } from '@/components/ui/button';
import { RiEditLine } from '@remixicon/react';

type Props = {
  status: TEMPLATE_STATUS;
  onClickEdit: () => void;
};

const PreviewDetail = ({ status, onClickEdit }: Props) => {
  const { getValues } = useFormContext<TZaloTempBeforeSchema>();
  return (
    <div className="border rounded-2xl flex-1 p-6 flex flex-col gap-6">
      <div className="flex items-center justify-between border-b pb-2">
        <div className="text-start font-medium">{t('common.zaloAds.tempName')}</div>
        {status === 'REJECT' && (
          <Button
            type="button"
            onClick={onClickEdit}
            className="rounded-md"
            variant={'outline'}
            size={'sm'}
          >
            <RiEditLine size={20} />
            {t('common.button.edit')}
          </Button>
        )}
      </div>
      <InfoTemp />
      <ImagePreview />
      <TextLabel label={t('common.zaloAds.contentKey')} value={getValues('content')} />
      <TextLabel label={t('common.zaloAds.note')} value={getValues('note')} />
    </div>
  );
};
export default PreviewDetail;
