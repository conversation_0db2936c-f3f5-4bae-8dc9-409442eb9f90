import { t } from 'i18next';
import { useFormContext } from 'react-hook-form';
import { TZaloTempBeforeSchema } from '@/constants/zalo/validate';

const InfoTemp = () => {
  const formMethod = useFormContext<TZaloTempBeforeSchema>();

  return (
    <table className="w-fit">
      <tbody>
        <TR label={t('common.zaloAds.type')} value={t('common.zaloAds.previewZns')} />
        <TR
          label={t('common.zaloAds.znsTemplateName')}
          value={formMethod.getValues('template_name')}
        />
        <TR label={t('common.zaloAds.titleKey')} value={formMethod.getValues('title')} />
      </tbody>
    </table>
  );
};

export default InfoTemp;

const TR = ({ label, value }: { label: string; value: string }) => (
  <tr className="text-sm">
    <td className="text-start font-normal text-secondary">{label}:</td>
    <td className="text-start pl-8 font-medium">{value}</td>
  </tr>
);
