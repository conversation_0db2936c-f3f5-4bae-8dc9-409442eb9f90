import { ZaloTemplate } from '@/constants/zalo/validate';
import { Control, UseFormGetValues } from 'react-hook-form';
import Wrapper from './Wrapper';
import AddImageButton from './AddImageButton';
import { t } from 'i18next';
import { RiErrorWarningLine } from '@remixicon/react';
import ErrorLabel from '@/components/ErrorLabel';

type Props = {
  control: Control<ZaloTemplate>;
  getValues: UseFormGetValues<ZaloTemplate>;
};

const UploadImage = ({ control }: Props) => {
  const error = control._formState.errors.logo;
  return (
    <Wrapper variants="secondary" title={t('common.zaloAds.addLogoOrImage')}>
      <div className="flex items-center gap-2 text-sm text-secondary">
        <RiErrorWarningLine className="flex-shrink-0" size={16} />
        {t('common.zaloAds.upload')}
      </div>
      <div className="flex gap-3">
        <AddImageButton control={control} variant="light" />
        <AddImageButton control={control} variant="dark" />
      </div>
      <ErrorLabel content={(error?.dark || error?.light) && t('common.zaloAds.imageRequired')} />
    </Wrapper>
  );
};

export default UploadImage;
