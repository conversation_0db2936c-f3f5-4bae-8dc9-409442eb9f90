import { cn } from '@/lib/utils';
import { BodyComponent } from '@/types/zalo';

type Props = {
  body: BodyComponent;
};

const BodyPreview = ({ body }: Props) => {
  switch (Object.keys(body)[0]) {
    case 'TITLE':
      return <h2 className="font-semibold">{body.TITLE?.value}</h2>;
    case 'PARAGRAPH':
      return <p className="text-sm">{body.PARAGRAPH?.value}</p>;
    case 'TABLE':
      return (
        <div className="flex flex-col">
          {body.TABLE?.rows.map((item, index) => (
            <div key={index} className={cn('flex text-sm font-medium gap-1')}>
              <span>{item.title}</span>
              <span>{item.value}</span>
            </div>
          ))}
        </div>
      );
  }
};
export default BodyPreview;
