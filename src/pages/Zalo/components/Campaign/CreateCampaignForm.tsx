import { DialogClose, DialogFooter } from '@/components/ui/dialog';
import { TZaloCampaign, zaloCampaignSchema } from '@/constants/zalo/validate';
import { zodResolver } from '@hookform/resolvers/zod';
import { <PERSON><PERSON><PERSON><PERSON>, SubmitHandler, useForm } from 'react-hook-form';
import InputRegister from './InputRegister';
import { ZL_CAMPAIGN_KEY } from '@/constants/zalo';
import { t } from 'i18next';
import { Button } from '@/components/ui/button';
import SelectRegister from './SelectRegister';
import { useSegmentContext } from '@/pages/context/SegmentContext';
import { Dispatch, useCallback, useContext, useEffect, useState } from 'react';
import { OauthContext } from '../../Context/OauthContext';
import { zaloApi } from '@/apis/zaloApi';
import { RiLoader2Line } from '@remixicon/react';
import PreviewZNSCampaign from './PreviewZNS';
import ScheduleCampaign from './ScheduleCampaign';
import { toast } from '@/hooks/use-toast';
import { CampaignAction } from '@/hooks/campaignReducer';
import { TemplateContext } from '../../Context/TemplateContext';

type Props = {
  onCloseModal: () => void;
  defaultValue?: TZaloCampaign;
  dispatch?: Dispatch<CampaignAction>;
};

const CreateCampaignForm = ({ onCloseModal, defaultValue, dispatch }: Props) => {
  const { items } = useSegmentContext();
  const { data: auth } = useContext(OauthContext);
  const { getTemp, option } = useContext(TemplateContext);
  const [loading, setLoading] = useState(false);

  const formMethods = useForm<TZaloCampaign>({
    resolver: zodResolver(zaloCampaignSchema),
    defaultValues: defaultValue || {
      campaign_name: '',
    },
  });

  useEffect(() => {
    if (!defaultValue) return;
    formMethods.reset(defaultValue);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultValue]);

  const segmentOptions = items.map((item) => ({
    label: item.name,
    value: item.id.toString(),
  }));

  const getSegment = useCallback(() => {
    return items.find((item) => item.id.toString() === formMethods.watch('segment'));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formMethods.watch('segment')]);

  const tempCurrent = useCallback(() => {
    return getTemp(formMethods.watch('template'));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formMethods.watch('template')]);

  const onSubmit: SubmitHandler<TZaloCampaign> = async (data) => {
    const payload = {
      name: data.campaign_name,
      expected_amount: 100,
      template_id: Number(data.template),
      segment_id: Number(data.segment),
    };
    const finalPayload = data.isSendDate ? { ...payload, send_time: data.sendDate } : payload;
    setLoading(true);
    try {
      const res = await zaloApi.createCampaign(auth?.oa_id || '', finalPayload);
      dispatch?.({ type: 'ADD_CAMPAIGN', payload: res.data });
      onCloseModal();
      toast({
        title: res.message,
        status: 'success',
      });
      setLoading(false);
    } catch (error) {
      setLoading(false);
      return error;
    }
  };

  return (
    <FormProvider {...formMethods}>
      <form onSubmit={formMethods.handleSubmit(onSubmit)} className="flex flex-col gap-4">
        <div className="flex items-center gap-4">
          <div className="flex-1 flex flex-col gap-4">
            <InputRegister
              control={formMethods.control}
              name={ZL_CAMPAIGN_KEY.CAMPAIGN_NAME}
              label={t('common.zaloAds.campaign')}
              placeholder={t('common.zaloAds.enterCampaign')}
            />
            <SelectRegister
              control={formMethods.control}
              name={ZL_CAMPAIGN_KEY.TEMPLATE}
              options={option}
              label={t('common.zaloAds.template')}
              placeholder={t('common.zaloAds.chooseTemplate')}
            />
            <SelectRegister
              control={formMethods.control}
              name={ZL_CAMPAIGN_KEY.SEGMENT}
              options={segmentOptions}
              label={t('common.zaloAds.segment')}
              placeholder={t('common.zaloAds.chooseSegment')}
            />
            <div className="flex flex-col">
              <Label
                label={t('common.zaloAds.totalContact')}
                value={getSegment()?.contact_quantity.toString() || ''}
              />
              <Label
                label={t('common.zaloAds.expectedAmount')}
                value={tempCurrent()?.price.toString() || ''}
              />
            </div>
            <ScheduleCampaign control={formMethods.control} />
          </div>
          <div className="w-[330px]">
            <PreviewZNSCampaign data={tempCurrent()?.data} />
          </div>
        </div>
        <DialogFooter className="grid grid-cols-2 w-full gap-4 [&_button]:w-full">
          <DialogClose>
            <Button variant={'outline'} type="button">
              {t('common.button.cancel')}
            </Button>
          </DialogClose>
          <Button type="submit">
            {loading ? (
              <RiLoader2Line size={20} className="animate-spin" />
            ) : (
              t('common.button.continue')
            )}
          </Button>
        </DialogFooter>
      </form>
    </FormProvider>
  );
};

export default CreateCampaignForm;

const Label = ({ label, value }: { label: string; value: string }) => {
  return (
    <div className="flex items-center">
      <div className="w-[140px] text-sm p-2 text-secondary">{label}:</div>
      <div className="col-span-2 p-2 font-medium">{value}</div>
    </div>
  );
};
