import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { TZaloCampaign } from '@/constants/zalo/validate';
import { cn } from '@/lib/utils';
import { Option } from '@/types/zalo';
import { RiInformationLine } from '@remixicon/react';
import { t } from 'i18next';
import { Control } from 'react-hook-form';

type Props = {
  control: Control<TZaloCampaign>;
  name: keyof TZaloCampaign;
  label?: string;
  placeholder?: string;
  options: Option[];
  className?: string;
  disabled?: boolean;
};

const SelectRegister = ({
  control,
  name,
  label,
  placeholder,
  options,
  className,
  disabled = false,
}: Props) => {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field, fieldState }) => (
        <FormItem className={cn('flex flex-col items-start gap-1', className)}>
          {label && <FormLabel>{label}</FormLabel>}
          <Select
            value={field.value?.toString()}
            onValueChange={field.onChange}
            disabled={disabled}
          >
            <FormControl>
              <SelectTrigger
                className={cn(
                  fieldState.error && 'border-red-500',
                  disabled && 'opacity-50 cursor-not-allowed',
                )}
              >
                <SelectValue placeholder={placeholder} />
              </SelectTrigger>
            </FormControl>
            {options.length === 0 ? (
              <SelectContent className="h-[94px]">
                <div className="w-full h-full flex flex-col items-center justify-center gap-0.5">
                  <RiInformationLine size={24} />
                  {t('common.zaloAds.noData')}
                </div>
              </SelectContent>
            ) : (
              <SelectContent>
                {options.map((option) => (
                  <SelectItem key={option.value} value={option.value} disabled={disabled}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            )}
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default SelectRegister;
