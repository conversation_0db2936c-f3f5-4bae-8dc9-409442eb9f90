import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useState } from 'react';
import CreateCampaignForm from './CreateCampaignForm';
import { RiEdit2Line } from '@remixicon/react';
import { t } from 'i18next';
import { TCampaignZalo } from '@/types/zalo';

type Props = {
  row: TCampaignZalo;
};

const UpdateCampPopup = ({ row }: Props) => {
  const [open, setOpen] = useState(false);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant={'ghost'} className="h-8 w-8 hover:bg-secondary">
          <RiEdit2Line size={20} />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-[850px] w-full">
        <DialogHeader>
          <DialogTitle> {t('common.zaloAds.updateCampaign')}</DialogTitle>
        </DialogHeader>
        <CreateCampaignForm
          defaultValue={{
            campaign_name: row.name,
            template: row.template.id,
            segment: row.segment.id,
            isSendDate: Boolean(row.send_time),
            sendDate: row.send_time,
          }}
          onCloseModal={() => setOpen(!open)}
        />
      </DialogContent>
    </Dialog>
  );
};
export default UpdateCampPopup;
