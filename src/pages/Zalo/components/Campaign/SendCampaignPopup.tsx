import { Button } from '@/components/ui/button';
import { DialogClose } from '@/components/ui/dialog';
import { RiSendPlaneLine } from '@remixicon/react';
import { t } from 'i18next';

const SendCampaignPopup = () => {
  return (
    <div className="flex flex-col gap-4 justify-center items-center">
      <RiSendPlaneLine size={80} className="text-brand" />
      <span className="text-lg font-medium">{t('common.zaloAds.sendCampaign')}</span>
      <span className="text-center text-sm text-secondary">
        {t('common.zaloAds.subSendCampaign')}
      </span>
      <div className="w-full grid grid-cols-2 [&_button]:col-span-1 gap-4">
        <DialogClose className="w-full">
          <Button variant={'outline'} className="w-full">
            {t('common.button.cancel')}
          </Button>
        </DialogClose>
        <Button>{t('common.button.send')}</Button>
      </div>
    </div>
  );
};
export default SendCampaignPopup;
