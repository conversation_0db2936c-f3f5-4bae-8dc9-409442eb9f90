import CustomToolTips from '@/components/CustomToolTips';
import DatePicker from '@/components/DatePicker';
import { TimePicker } from '@/components/TimePicker';
import { Checkbox } from '@/components/ui/checkbox';
import { FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { TZaloCampaign } from '@/constants/zalo/validate';
import { cn } from '@/lib/utils';
import { disabledPastDate, formatDateTime, fTotoISOString, parseCustomDate } from '@/utils/helper';
import { RiInformation2Line } from '@remixicon/react';
import { t } from 'i18next';
import { useEffect, useState } from 'react';
import { Control, useFormContext, useWatch } from 'react-hook-form';

type Props = {
  control: Control<TZaloCampaign>;
};

const ScheduleCampaign = ({ control }: Props) => {
  const [sendTime, setSendTime] = useState<{ date: string; time: string }>({ date: '', time: '' });
  const isScheduleEnabled = useWatch({ control, name: 'isSendDate' });
  const { setValue, clearErrors, getValues } = useFormContext<TZaloCampaign>();

  useEffect(() => {
    if (!isScheduleEnabled) return;
    const currentSendTime = fTotoISOString(`${sendTime.date} ${sendTime.time}`);
    if (currentSendTime === '') return;
    setValue('sendDate', currentSendTime);
    clearErrors('isSendDate');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sendTime, isScheduleEnabled]);

  useEffect(() => {
    if (!isScheduleEnabled) {
      setSendTime({ date: '', time: '' });
      clearErrors('isSendDate');
      setValue('sendDate', '');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isScheduleEnabled]);

  const fSendTime = formatDateTime(getValues('sendDate') || '', '/', ':');
  const [defaultDate, defaultTime] = fSendTime.split(' ');

  return (
    <FormField
      control={control}
      name="isSendDate"
      render={({ field, formState }) => (
        <FormItem className="flex flex-col w-full">
          <div className="flex flex-row items-center space-x-2">
            <FormControl>
              <Checkbox checked={field.value} onCheckedChange={field.onChange} id="terms" />
            </FormControl>
            <label
              htmlFor="terms"
              className="text-sm flex items-center gap-1 font-medium text-secondary leading-none cursor-pointer"
            >
              {t('common.zaloAds.scheduleCampaign')}
            </label>
            <CustomToolTips
              content={t('common.zaloAds.scheduleNotice')}
              element={<RiInformation2Line className="cursor-pointer" size={16} />}
              className="z-50"
            />
          </div>

          {isScheduleEnabled && (
            <div className="grid grid-cols-3 gap-4 mt-4">
              <DatePicker
                disableDate={disabledPastDate}
                className={cn('col-span-2', formState.errors.isSendDate && 'border-red-500')}
                onChange={(date) => setSendTime({ ...sendTime, date })}
                defaultDate={defaultDate ? parseCustomDate(defaultDate) : undefined}
              />
              <TimePicker
                onChange={(time) =>
                  setSendTime({ ...sendTime, time: `${time.hour}:${time.minute}` })
                }
                className={cn('col-span-1', formState.errors.isSendDate && 'border-red-500')}
                value={{
                  hour: defaultTime?.split(':')[0] || null,
                  minute: defaultTime?.split(':')[1] || null,
                }}
              />
            </div>
          )}
          <FormMessage className="text-red-500 text-xs" />
        </FormItem>
      )}
    />
  );
};
export default ScheduleCampaign;
