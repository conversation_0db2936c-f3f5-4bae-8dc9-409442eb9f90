import { cn } from '@/lib/utils';
import Wrapper from '../Wrapper';
import { useState } from 'react';
import { t } from 'i18next';
import { Switch } from '@/components/ui/switch';
import { TemplateData } from '@/types/zalo';
import { Button } from '@/components/ui/button';
import BodyPreview from './BodyPreview';
import { RiInbox2Line } from '@remixicon/react';

type Props = {
  data: TemplateData | undefined;
};

const PreviewZNSCampaign = ({ data }: Props) => {
  const [mode, setMode] = useState<'dark' | 'light'>('light');
  const darkModeToggle = () => setMode(mode === 'dark' ? 'light' : 'dark');

  const body = data?.layout.body.components;
  const footer = data?.layout.footer.components[0];

  return (
    <Wrapper
      isRequired={false}
      className={cn('mt-6 w-[330px] pt-6 px-3 sticky top-2')}
      variants={'secondary'}
      title={'Preview ZNS'}
    >
      {!data ? (
        <div className="w-[298px] h-[317px] gap-4 rounded-xl flex-col mx-auto bg-card flex items-center justify-center">
          <div className="bg-secondary rounded-full p-5">
            <RiInbox2Line size={40} />
          </div>
          <div className="font-medium">{t('common.zaloAds.noData')}</div>
          <div className="w-[226px] text-center text-sm text-secondary">
            {t('common.zaloAds.subNoData')}
          </div>
        </div>
      ) : (
        <>
          <div className="flex items-center text-sm justify-between">
            {t('common.zaloAds.darkMode')}
            <Switch checked={mode === 'dark'} onCheckedChange={darkModeToggle} />
          </div>
          <div
            className={cn(
              'space-y-2 bg-card text-start p-3 rounded-xl text-sm font-medium',
              mode === 'dark' && 'bg-[#02111e] text-white',
            )}
          >
            <div className="flex flex-col gap-4">
              {body?.map((item, index) => <BodyPreview key={index} body={item} />)}
            </div>
            <p className="text-sm"> {t('common.zaloAds.appreciate')}</p>
            {footer?.BUTTONS.items.map((item, index) => (
              <Button type="button" key={index} size={'lg'} className="w-full bg-brand ">
                {item.title}
              </Button>
            ))}
          </div>
        </>
      )}
    </Wrapper>
  );
};
export default PreviewZNSCampaign;
