import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { RiSlideshow3Line } from '@remixicon/react';
import { t } from 'i18next';
import CreateCampaignForm from './CreateCampaignForm';
import { Dispatch, useState } from 'react';
import { CampaignAction } from '@/hooks/campaignReducer';

type Props = {
  dispatch?: Dispatch<CampaignAction>;
};

const CreateNew = ({ dispatch }: Props) => {
  const [open, setOpen] = useState(false);
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="bg-primary-crm p-3 items-center hover:bg-primary-crm rounded-xl text-secondary-foreground_crm flex gap-2 h-10">
          <RiSlideshow3Line size={20} />
          {t('common.zaloAds.createCampaign')}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-[850px] w-full">
        <DialogHeader>
          <DialogTitle> {t('common.zaloAds.createCampaign')}</DialogTitle>
        </DialogHeader>
        <CreateCampaignForm dispatch={dispatch} onCloseModal={() => setOpen(!open)} />
      </DialogContent>
    </Dialog>
  );
};
export default CreateNew;
