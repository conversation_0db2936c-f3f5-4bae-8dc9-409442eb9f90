import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input, InputProps } from '@/components/ui/input';
import { TZaloCampaign } from '@/constants/zalo/validate';
import { cn } from '@/lib/utils';
import { Control } from 'react-hook-form';

type Props = InputProps & {
  control: Control<TZaloCampaign>;
  name: keyof TZaloCampaign;
  label?: string;
  placeholder?: string;
};

const InputRegister = ({ control, name, label, placeholder, ...props }: Props) => {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className="flex flex-col items-start">
          {label && (
            <FormLabel className="mb-1 font-medium text-sm">
              {label} <span className="text-red-500">*</span>
            </FormLabel>
          )}
          <FormControl>
            <Input
              placeholder={placeholder}
              className={cn(
                'text-sm h-10 p-3 rounded-xl',
                control._formState.errors[name] && 'border-red-500',
              )}
              {...props}
              {...field}
              value={typeof field.value === 'boolean' ? String(field.value) : field.value}
            />
          </FormControl>
          <FormMessage className="mt-2" />
        </FormItem>
      )}
    />
  );
};

export default InputRegister;
