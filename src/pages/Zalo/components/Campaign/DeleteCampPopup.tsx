import { Button } from '@/components/ui/button';
import { DialogClose } from '@/components/ui/dialog';
import { RiErrorWarningLine } from '@remixicon/react';
import { t } from 'i18next';

const DeleteCampPopup = () => {
  return (
    <div className="flex flex-col gap-4 justify-center items-center">
      <RiErrorWarningLine size={80} className="text-red-500" />
      <span className="text-lg font-medium">{t('common.zaloAds.deleteCamp')}</span>
      <span className="text-center text-sm text-secondary">
        {t('common.zaloAds.subDeleteCamp')}
      </span>
      <div className="w-full grid grid-cols-2 [&_button]:col-span-1 gap-4">
        <DialogClose className="w-full">
          <Button variant={'outline'} className="w-full">
            {t('common.button.cancel')}
          </Button>
        </DialogClose>{' '}
        <Button variant={'delete'}>{t('common.button.delete')}</Button>
      </div>
    </div>
  );
};
export default DeleteCampPopup;
