import DateRangePicker from '@/components/DateRanger/DateRangerPicker';
import { MultipleSelect } from '@/components/MultipleSelect';
import SearchBar from '@/components/SearchBar';
import { ZALO_ROUTER } from '@/constants/router';
import { templateStatus } from '@/constants/zalo';
import { TFilterZalo } from '@/types/zalo';
import { RiLayoutLeftLine, RiNotificationBadgeLine } from '@remixicon/react';
import { t } from 'i18next';
import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

type Props = {
  setFilterPayload: (filterPayload: TFilterZalo) => void;
  filterPayload: TFilterZalo;
  rightContext?: React.ReactNode;
};
const FIlterTemplate = ({ filterPayload, setFilterPayload, rightContext }: Props) => {
  const [search, setSearch] = useState<string>('');
  const [isSearch, setIsSearch] = useState<boolean>(false);

  useEffect(() => {
    if (!isSearch) return;
    const timeOut = setTimeout(() => {
      setFilterPayload({ ...filterPayload, search });
    }, 300);
    return () => clearTimeout(timeOut);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [search]);

  return (
    <div className="flex my-6 items-center justify-between gap-3">
      <SearchBar
        className="flex-1"
        value={search}
        setSearchQuery={(value) => {
          setSearch(value);
          setIsSearch(true);
        }}
        placeholder={t('common.zaloAds.searchTemplate')}
      />
      <MultipleSelect
        key={'status'}
        icon={<RiNotificationBadgeLine size={16} />}
        title={t('common.status')}
        isHideSearch={true}
        options={templateStatus}
        selected={filterPayload.status_in?.split(',') || []}
        onChange={(e) => {
          if (!e.length) {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { status_in, ...rest } = filterPayload;
            setFilterPayload(rest);
            return;
          }
          setFilterPayload({ ...filterPayload, status_in: e.join(',') });
        }}
        placeholder={t('common.status')}
        className="min-w-[140px]"
      />
      <DateRangePicker
        className="max-w-[200px] w-full h-10"
        placeholder={t('common.facebookAds.pushDate')}
        onChange={(date) => {
          if (!date.from || !date.to) return;
          setFilterPayload({
            ...filterPayload,
            date_created_from: new Date(date.from || '').toISOString(),
            date_created_to: new Date(date.to || '').toISOString(),
          });
        }}
        onClear={() => {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { date_created_from, date_created_to, ...rest } = filterPayload;
          setFilterPayload(rest);
        }}
      />
      {rightContext || (
        <Link
          to={ZALO_ROUTER.createTemplate}
          className="bg-primary-crm p-3 items-center hover:bg-primary-crm rounded-xl text-secondary-foreground_crm flex gap-2 h-10"
        >
          <RiLayoutLeftLine size={20} />
          {t('common.zaloAds.createNewTemplate')}
        </Link>
      )}
    </div>
  );
};

export default FIlterTemplate;
