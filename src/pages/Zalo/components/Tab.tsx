import i18n from '@/config-translation';
import { ROOT_PATH, ROOT_ROUTE } from '@/constants/router';
import { cn } from '@/lib/utils';
import { RiLayoutLeftLine, RiSlideshow3Line } from '@remixicon/react';
import { Link, useLocation } from 'react-router-dom';

const routers = [
  {
    path: `${ROOT_PATH}/${ROOT_ROUTE.zalo.zns}`,
    name: i18n.t('common.zaloAds.templateZns'),
    icon: <RiLayoutLeftLine />,
  },
  {
    path: `${ROOT_PATH}/${ROOT_ROUTE.zalo.campaign}`,
    name: i18n.t('common.zaloAds.campaign'),
    icon: <RiSlideshow3Line />,
  },
];

const Tabs = () => {
  const { pathname } = useLocation();
  return (
    <div className="flex items-center border-b border-brand">
      {routers.map((item) => {
        const isActive = pathname === item.path;
        return (
          <Link
            to={item.path}
            className={cn(
              'py-4 pl-2 pr-[40px] flex items-center gap-1 relative text-base leading-[26px]',
              isActive ? 'font-medium text-brand' : 'text-tertiary',
            )}
            key={item.path}
          >
            {item.icon}
            {item.name}
            {isActive && <div className="absolute w-full h-0.5 bg-brand bottom-0 right-0 left-0" />}
          </Link>
        );
      })}
    </div>
  );
};

export default Tabs;
