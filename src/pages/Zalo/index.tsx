import { Outlet } from 'react-router-dom';
import Header from './components/Header';

import { Button } from '@/components/ui/button';
import { RiLogoutBoxLine } from '@remixicon/react';
import { t } from 'i18next';
import { useContext } from 'react';
import { RiAccountCircle2Line } from 'react-icons/ri';
import Tabs from './components/Tab';
import ZaloConnect from './components/ZaloConnect';
import { OauthContext } from './Context/OauthContext';
import EmptyAuth from './Views/EmptyAuth';

const ZaloPage = () => {
  const { isLogin, loading, data, logout } = useContext(OauthContext);
  if (loading) return <ZaloConnect />;

  return (
    <>
      <Header />
      {isLogin ? (
        <>
          <div className="flex items-center justify-between">
            <div className="max-w-[205px] px-4 py-3 w-full h-11 rounded-2xl border text-[16px] tracking-wide flex items-center gap-1 justify-start">
              <RiAccountCircle2Line className="flex-shrink-0" size={20} />{' '}
              {data?.oa_name || t('common.zaloAds.zaloAccount')}
            </div>
            <Button
              onClick={logout}
              className="bg-secondary px-3 py-1 rounded-md"
              variant={'ghost'}
              size={'lg'}
            >
              <RiLogoutBoxLine size={20} />
              {t('common.button.logout')}
            </Button>
          </div>
          <Tabs />
          <Outlet />
        </>
      ) : (
        <EmptyAuth />
      )}
    </>
  );
};
export default ZaloPage;
