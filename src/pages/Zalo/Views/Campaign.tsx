import { TFilterZalo } from '@/types/zalo';
import FIlterTemplate from '../components/FIlterTemplate';
import { useContext, useEffect, useReducer, useState } from 'react';
import CreateNew from '../components/Campaign/CreateNew';
import campaignReducer, { initialState } from '@/hooks/campaignReducer';
import { OauthContext } from '../Context/OauthContext';
import { zaloApi } from '@/apis/zaloApi';
import DataTable from '@/components/table/DataTable';
import campaignCol from '../components/column/CampaignCol';

const Campaign = () => {
  const [state, dispatch] = useReducer(campaignReducer, initialState);
  const { data: auth } = useContext(OauthContext);
  const [filterParams, setFilterParams] = useState<TFilterZalo>({
    page: 1,
    limit: 10,
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!auth) return;
    const fetchData = async () => {
      setLoading(true);
      try {
        const res = await zaloApi.getListCampaign(auth?.oa_id || '', filterParams);
        dispatch({ type: 'GET_LIST', payload: res });
        setLoading(false);
      } catch (error) {
        setLoading(false);
        return error;
      }
    };
    fetchData();
  }, [auth, filterParams]);

  return (
    <div className="flex flex-col">
      <FIlterTemplate
        setFilterPayload={setFilterParams}
        filterPayload={filterParams}
        rightContext={<CreateNew dispatch={dispatch} />}
      />
      <DataTable
        pagination={{ currentPage: filterParams.page, pageSize: filterParams.limit }}
        loading={loading}
        data={state.items}
        columns={campaignCol()}
        total={state.count}
        setPagination={(pagination) =>
          setFilterParams({
            ...filterParams,
            limit: pagination.pageSize,
            page: pagination.currentPage,
          })
        }
      />
    </div>
  );
};
export default Campaign;
