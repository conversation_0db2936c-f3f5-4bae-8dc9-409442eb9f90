import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  RiAddCircleFill,
  RiCheckboxCircleLine,
  RiExternalLinkLine,
  RiEyeLine,
} from '@remixicon/react';
import { LoadImage } from '@/assets/LoadImage';
import { cn } from '@/lib/utils';
import { ZALO_HOW_TO_CONNECT } from '@/utils/constants';
import Modal from '@/components/Modal';

const HowToUse: React.FC = () => {
  const { t } = useTranslation();
  return (
    <div>
      <div className="text-left mb-6">
        <div className="flex justify-between items-center">
          <div className="flex-1 h-[64px]">
            <h3 className="text-2xl font-medium mb-2">{t('howToConnect.title')}</h3>
            <p className="text-base font-normal text-tertiary-foreground tracking-[0.2px]">
              {t('howToConnect.description')}
            </p>
          </div>
        </div>
      </div>
      <div className="w-full max-w-[872px] px-6 mt-12 mx-auto">
        <StepContent
          step={1}
          content={
            <p>
              <span>{t('howToConnect.createZaloAccount')}</span>
              <StepLink
                link={ZALO_HOW_TO_CONNECT.createZCA}
                title={t('howToConnect.seeHowToCreate')}
                icon={<RiExternalLinkLine size={16} />}
              />
            </p>
          }
        />
        <StepContent
          step={2}
          content={
            <p>
              <span>{t('howToConnect.linkAppIdToZaloCloudAccount')}</span>
              <StepLink
                link={ZALO_HOW_TO_CONNECT.appIdToZCA}
                title={t('howToConnect.seeHowToLink')}
                icon={<RiExternalLinkLine size={16} />}
              />
            </p>
          }
        />
        <StepContent
          step={3}
          imgLink={'/zalo-step3.png'}
          content={
            <>
              <p>
                <span>{t('howToConnect.accessTo')}</span>
                <StepLink
                  link={ZALO_HOW_TO_CONNECT.zaloDev}
                  title={ZALO_HOW_TO_CONNECT.zaloDev}
                  icon={<RiExternalLinkLine size={16} />}
                />
              </p>
              <p className="min-h-[20px]">
                <span className="mr-1">{t('howToConnect.atYourVAccount')}</span>
                <p className="inline-flex items-center gap-1 relative font-medium pb-0 h-auto bg-transparent text-primary-crm hover:bg-transparent text-sm pl-[20px] mr-1">
                  <RiAddCircleFill
                    size={16}
                    color={'#20232C'}
                    className="absolute top-[2px] left-0"
                  />
                  {t('howToConnect.addNewApp')}
                </p>
                <span>{t('howToConnect.toStart')}</span>
              </p>
            </>
          }
        />
        <StepContent
          step={4}
          imgLink={'/zalo-step4.png'}
          content={<span>{t('howToConnect.fillRequired')}</span>}
        />
        <StepContent
          step={5}
          imgLink={'/zalo-step5.png'}
          content={
            <>
              <p>
                <span>{t('howToConnect.afterCreated')}</span>
              </p>
              <p>{t('howToConnect.searchForSkylink')}</p>
            </>
          }
        />
        <StepContent
          step={6}
          imgLink={'/zalo-step6.png'}
          content={
            <>
              <Content
                title={t('howToConnect.goTo')}
                content={t('howToConnect.domainAuthentication')}
                icon={<RiCheckboxCircleLine size={16} color={'#20232C'} />}
              />
              <Content
                title={t('howToConnect.pasteDomain')}
                content={'crmdevbe.big360.ai'}
                extraContent={
                  <>
                    <span>{t('howToConnect.andClick')}</span>&nbsp;
                    <span className="font-medium text-primary-crm">
                      {t('howToConnect.authetication')}
                    </span>
                  </>
                }
              />
            </>
          }
        />
        <StepContent
          step={7}
          imgLink={'/zalo-step7.png'}
          content={
            <>
              <Content
                title={t('howToConnect.popUpVerify')}
                content={t('howToConnect.verifyNow')}
              />
            </>
          }
        />
        <StepContent
          step={8}
          imgLink={'/zalo-step8.png'}
          content={
            <>
              <Content
                className="block"
                extraContent={
                  <>
                    <p>{t('howToConnect.copyTheText')}</p>
                    <p>
                      <span className="font-medium text-primary-crm">{t('howToConnect.note')}</span>{' '}
                      <span className="">{t('howToConnect.mayTake')}</span>
                    </p>
                  </>
                }
              />
            </>
          }
        />
        <StepContent
          step={9}
          imgLink={'/zalo-step9.png'}
          content={
            <>
              <Content title={t('howToConnect.afterVerify')} />
            </>
          }
        />
        <StepContent
          step={10}
          imgLink={'/zalo-step10.png'}
          content={
            <Content
              className="block"
              extraContent={
                <>
                  <p>
                    <span>{t('howToConnect.goTo')}</span>{' '}
                    <span className="font-medium text-primary-crm">Webhook</span>{' '}
                    <span>{t('howToConnect.andPasteUrl')}</span>
                    <StepLink
                      link={ZALO_HOW_TO_CONNECT.webHook}
                      title={ZALO_HOW_TO_CONNECT.webHook}
                    />
                  </p>
                  <p>
                    <span>{t('howToConnect.copy')}</span>{' '}
                    <span className="font-medium text-primary-crm">
                      {t('howToConnect.oaSecretKey')}
                    </span>{' '}
                    <span>{t('howToConnect.andSendToBig360')}</span>
                  </p>
                </>
              }
            />
          }
          extraContent={
            <Content
              className="block"
              extraContent={
                <>
                  <span>{t('howToConnect.big360Will')}</span>{' '}
                  <span className="font-medium text-primary-crm">
                    {t('howToConnect.secretKey')}
                  </span>{' '}
                  <span>{t('howToConnect.toWebHook')}</span>{' '}
                  <span className="font-medium text-primary-crm">
                    {t('howToConnect.changeTemplate')}
                  </span>{' '}
                  <span>{t('howToConnect.and')}</span>{' '}
                  <span className="font-medium text-primary-crm">
                    {t('howToConnect.userReceived')}
                  </span>
                  <PreviewImage imgLink={'/zalo-step10b.png'} />
                </>
              }
            />
          }
        />
        <StepContent
          step={11}
          imgLink={'/zalo-step11.png'}
          content={
            <>
              <Content
                className="block"
                extraContent={
                  <>
                    <p>
                      <span>{t('howToConnect.accessTo')}</span>
                      <span className="font-medium text-primary-crm">
                        {t('howToConnect.crm360Zalo')}
                      </span>{' '}
                      <span>{t('howToConnect.clickConnectTo')}</span>
                      <span className="font-medium text-primary-crm">
                        {t('howToConnect.zaloAccount')}
                      </span>{' '}
                      <span>{t('howToConnect.permission')}</span>
                    </p>
                    <p>
                      <span>{t('howToConnect.inCase')}</span>
                      <span className="font-medium text-primary-crm">
                        {t('howToConnect.linkOfficial')}
                      </span>{' '}
                      <span>{t('howToConnect.descriptionBig360')}</span>
                    </p>
                    {/*  */}
                    <p className='mt-2'>
                      <span>{t('howToConnect.productEnv')}</span>
                      <StepLink
                        link={ZALO_HOW_TO_CONNECT.prodEnv}
                        title={ZALO_HOW_TO_CONNECT.prodEnv}
                      />
                    </p>
                    <p className='mt-2'>
                      <span>{t('howToConnect.sandBoxEnv')}</span>
                      <StepLink
                        link={ZALO_HOW_TO_CONNECT.devEnv}
                        title={ZALO_HOW_TO_CONNECT.devEnv}
                      />
                    </p>
                    <p className='mt-2'>
                      <span>
                        {t('howToConnect.integrateZaloCLoud')}
                      </span>
                    </p>
                    <p className='mt-2'>
                       <span className="font-medium text-primary-crm">
                        {t('howToConnect.noteStep11')}
                      </span>
                    </p>

                    {/*  */}
                  </>
                }
              />
            </>
          }
        />
      </div>
    </div>
  );
};
export default HowToUse;

type TStepContentProps = {
  step: number;
  content: React.ReactNode;
  imgLink?: string;
  extraContent?: React.ReactNode;
};
const StepContent: React.FC<TStepContentProps> = ({ ...props }: TStepContentProps) => {
  const { step, content, imgLink, extraContent } = props;
  const { t } = useTranslation();
  return (
    <div className="text-left my-6">
      <p className="text-sm font-medium text-primary-crm mb-2">
        {t('howToConnect.step', { step })}
      </p>
      <div className="text-sm text-secondary">{content}</div>
      {imgLink && <PreviewImage imgLink={imgLink} />}
      {extraContent && <div className="text-sm text-secondary mt-2">{extraContent}</div>}
    </div>
  );
};

type TStepLinkProps = {
  link: string;
  title: string;
  icon?: React.ReactNode;
};
const StepLink: React.FC<TStepLinkProps> = ({ ...props }: TStepLinkProps) => {
  const { link, title, icon } = props;
  return (
    <a
      href={link}
      target={'_blank'}
      className="relative text-info ml-3 inline-flex items-center before:absolute before:bottom-0 before:right-0 before:w-full before:h-[1px] before:bg-info"
    >
      {title}
      {icon && <span className="ml-1">{icon}</span>}
    </a>
  );
};

type TContentProps = {
  title?: string;
  content?: React.ReactNode;
  icon?: React.ReactNode;
  extraContent?: React.ReactNode;
  className?: string;
};

const Content: React.FC<TContentProps> = ({ ...props }: TContentProps) => {
  const { title, content, icon, extraContent, className } = props;
  return (
    <p className={cn('flex', className)}>
      {title && <span>{title}</span>}
      {content && (
        <p className="flex items-center font-medium gap-1 p-0 h-auto bg-transparent text-primary-crm hover:bg-transparent text-sm mx-1">
          {icon}
          {content}
        </p>
      )}
      {extraContent}
    </p>
  );
};

type TPreviewImageProps = {
  imgLink: string;
};

const PreviewImage: React.FC<TPreviewImageProps> = ({ ...props }: TPreviewImageProps) => {
  const { imgLink } = props;

  const imageLoad = useMemo(() => {
    if (!imgLink) {
      return <></>;
    }
    return <LoadImage url={imgLink} className='rounded-lg'/>;
  }, [imgLink]);

  return (
    <div className="mt-4">
      <Modal
        trigger={
          <button className="relative group hover:text-primary">
            {imageLoad}
            <div className="absolute top-0 rounded-lg justify-center items-center bg-overlay w-full h-full hidden group-hover:flex">
              <div className="flex items-center justify-center w-[40px] h-[40px] rounded-full bg-overlay-box-icon">
                <RiEyeLine size={24} color="#FDFDFD" />
              </div>
            </div>
          </button>
        }
        className="max-w-none w-[75%] h-fit p-1"
        isCloseIcon={false}
      >
        <div className="min-h-[300px]">{imageLoad}</div>
      </Modal>
    </div>
  );
};
