import { t } from 'i18next';
import CreateTempForm from '../components/CreateTempForm';
import PreviewZNS from '../components/PreviewZNS';
import { useEffect, useState } from 'react';
import SendForm from '../components/SendForm';
import { FormProvider, useForm } from 'react-hook-form';
import {
  initialTemplate,
  ZaloTemplate,
  zaloTemplateSchema,
  TZaloTempBeforeSchema,
  zaloTempBeforeSchema,
  initialTemplateBefore,
} from '@/constants/zalo/validate';
import { zodResolver } from '@hookform/resolvers/zod';

const CreateTemplateZNS = () => {
  const [view, setView] = useState<'form' | 'send'>('form');
  const formMethods = useForm<ZaloTemplate>({
    resolver: zodResolver(zaloTemplateSchema),
    defaultValues: initialTemplate,
  });

  const sendFormMethods = useForm<TZaloTempBeforeSchema>({
    resolver: zodResolver(zaloTempBeforeSchema),
    defaultValues: initialTemplateBefore,
  });

  useEffect(() => {
    if (view === 'send') {
      sendFormMethods.reset(formMethods.getValues());
    }
  }, [formMethods, sendFormMethods, view]);

  const toggle = () => setView(view === 'form' ? 'send' : 'form');
  const currentMethods = view === 'form' ? formMethods : sendFormMethods;

  return (
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //@ts-expect-error
    <FormProvider {...currentMethods}>
      <div className="flex gap-6">
        <div className="flex-1">
          {view === 'form' ? (
            <h6 className="font-medium leading-[26px] text-start my-6">
              {t('common.zaloAds.createContent')}
            </h6>
          ) : (
            <div className="flex flex-col gap-1 items-start mb-4 mt-6">
              <span className="font-medium">{t('common.zaloAds.sendForAppoval')}</span>
              <span className="text-sm text-secondary">{t('common.zaloAds.description')}</span>
            </div>
          )}
          {view === 'form' && <CreateTempForm onClick={toggle} />}
          {view === 'send' && <SendForm onBack={toggle} />}
        </div>
        <div className="col-span-1">
          <PreviewZNS />
        </div>
      </div>
    </FormProvider>
  );
};

export default CreateTemplateZNS;
