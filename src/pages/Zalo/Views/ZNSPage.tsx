import { zalo<PERSON><PERSON> } from '@/apis/zaloApi';
import { useContext, useEffect, useState, useCallback } from 'react';
import { OauthContext } from '../Context/OauthContext';
import { Template, TFilterZalo } from '@/types/zalo';
import DataTable from '@/components/table/DataTable';
import { tempCol } from '../components/column/TempCol';
import FIlterTemplate from '../components/FIlterTemplate';
import { cn } from '@/lib/utils';

const ZNSPage = () => {
  const { data: auth } = useContext(OauthContext);
  const [templates, setTemplates] = useState<{
    count: number;
    items: Template[];
    loading: boolean;
  }>({ count: 0, items: [], loading: false });

  const [filterParams, setFilterParams] = useState<TFilterZalo>({
    page: 1,
    limit: 10,
  });

  const fetchData = useCallback(async () => {
    if (!auth?.oa_id) return;

    setTemplates((prev) => ({ ...prev, loading: true }));
    const removeEmpty = Object.fromEntries(
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      Object.entries(filterParams).filter(([_, v]) => v != null && v !== ''),
    );
    try {
      const res = await zaloApi.getListTemp(auth.oa_id, removeEmpty);
      setTemplates({
        count: res.count,
        items: res.items,
        loading: false,
      });
    } catch (error) {
      setTemplates((prev) => ({ ...prev, loading: false }));
      return error;
    }
  }, [auth?.oa_id, filterParams]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleDelete = async (id: string) => {
    setTemplates((prev) => {
      return {
        ...prev,
        items: prev.items.filter((item) => item.template_id !== id),
        count: prev.count - 1,
      };
    });
  };

  const handlePaginationChange = useCallback(
    (pagination: { currentPage: number; pageSize: number }) => {
      setFilterParams((prev) => ({
        ...prev,
        page: pagination.currentPage,
        limit: pagination.pageSize,
      }));
    },
    [],
  );

  return (
    <>
      <FIlterTemplate setFilterPayload={setFilterParams} filterPayload={filterParams} />
      <DataTable
        containerClassName="border-none rounded-none"
        className={cn(
          '[&_thead]:hidden border-none [&_tr:last-child]:border-b [&_tr:first-child]:border-t-0 [&_tr]:border-b rounded-none',
          templates.count === 1 && '[&_tr:last-child]:border-t-0',
        )}
        data={templates.items}
        columns={tempCol({
          onRemove: handleDelete,
        })}
        loading={templates.loading}
        total={templates.count}
        pagination={{
          pageSize: filterParams.limit || 10,
          currentPage: filterParams.page || 1,
        }}
        setPagination={handlePaginationChange}
      />
    </>
  );
};

export default ZNSPage;
