import { zalo<PERSON><PERSON> } from '@/apis/zalo<PERSON>pi';
import { useMutation } from '@tanstack/react-query';
import { ROOT_PATH, ZALO_ROUTER } from '@/constants/router';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { RiQuestionLine } from '@remixicon/react';
import ConnectModal from '@/components/ConnectModal';

const EmptyAuth = () => {
  const { t } = useTranslation();
  const mutation = useMutation({
    mutationFn: zaloApi.getZaloOauthURL,
    onSuccess: (data) => {
      window.location.href = data;
    },
    onError: (error) => error,
  });

  return (
    <div className="mt-52 flex flex-col items-center w-full h-full gap-6 relative">
      <ConnectModal
        onConnect={() => mutation.mutate('https://dev.big360.ai/crm360/zalo/zns')}
        value={'Zalo'}
        loading={mutation.isPending}
      />
      <Link
        to={`${ROOT_PATH}/${ZALO_ROUTER.howToConnect}`}
        className="flex gap-1 items-center text-info border-b border-info"
      >
        <RiQuestionLine className="flex-shrink-0" size={16} color="#2C9EFF" />{' '}
        {t('howToConnect.title')}
      </Link>
    </div>
  );
};
export default EmptyAuth;
