import { Template } from '@/types/zalo';
import { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { OauthContext } from './OauthContext';
import { zaloApi } from '@/apis/zaloApi';

interface TemplateState {
  count: number;
  items: Template[];
}

export interface TTemplateContext {
  items: Template[];
  option: Array<{ label: string; value: string }>;
  getTemp: (id: string) => Template | undefined;
}

const defaultContextValue: TTemplateContext = {
  items: [],
  option: [],
  getTemp: () => undefined,
};

const TemplateContext = createContext<TTemplateContext>(defaultContextValue);

export const TemplateContextProvider = ({ children }: { children: ReactNode }) => {
  const [state, setState] = useState<TemplateState>({
    count: 0,
    items: [],
  });

  const { data: auth } = useContext(OauthContext);

  useEffect(() => {
    if (!auth?.oa_id) return;

    const fetchData = async () => {
      try {
        const res = await zaloApi.getListTemp(auth.oa_id);
        setState(res);
      } catch (error) {
        setState({ count: 0, items: [] });
        console.error('Error fetching templates:', error);
        return error;
      }
    };

    fetchData();
  }, [auth?.oa_id]);

  const getTemp = (id: string) => {
    return state.items.find((item) => item.id.toString() === String(id));
  };

  const option = state.items.reduce(
    (acc, item) => {
      if (item.status === 'ENABLE') {
        acc.push({
          label: item.template_name,
          value: item.id.toString(),
        });
      }
      return acc;
    },
    [] as Array<{ label: string; value: string }>,
  );

  const contextValue: TTemplateContext = {
    items: state.items,
    option,
    getTemp,
  };

  return <TemplateContext.Provider value={contextValue}>{children}</TemplateContext.Provider>;
};

export { TemplateContext };
