import { zalo<PERSON><PERSON> } from '@/apis/zaloApi';
import { useMutation } from '@tanstack/react-query';
import { createContext, useEffect, useState } from 'react';

export type TOauth = {
  oa_name: string;
  oa_id: string;
  oa_avatar: string;
  oa_status: boolean;
};

type TOauthZaloContext = {
  isLogin: boolean;
  logout: () => void;
  loading: boolean;
  data: TOauth | null;
};

const defaultOauthZaloContext: TOauthZaloContext = {
  isLogin: false,
  logout: () => {},
  loading: false,
  data: null,
};

const OauthContext = createContext<TOauthZaloContext>(defaultOauthZaloContext);

const OauthProvider = ({ children }: { children: React.ReactNode }) => {
  const [isLogin, setIsLogin] = useState<boolean>(false);
  const [data, setData] = useState<TOauth | null>(null);

  const mutation = useMutation({
    mutationFn: zaloApi.getListOauth,
    onSuccess: (data) => {
      if (data.items.length > 0) {
        setData(data.items[0]);
        setIsLogin(true);
        return;
      }
      setIsLogin(false);
    },
  });

  useEffect(() => {
    mutation.mutate();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleLogout = async () => {
    if (!data) return;
    try {
      await zaloApi.logoutZalo(data?.oa_id);
      setData(null);
      setIsLogin(false);
    } catch (error) {
      return error;
    }
  };

  return (
    <OauthContext.Provider
      value={{
        isLogin,
        logout: handleLogout,
        loading: mutation.isPending,
        data: data,
      }}
    >
      {children}
    </OauthContext.Provider>
  );
};

export { OauthProvider, OauthContext };
