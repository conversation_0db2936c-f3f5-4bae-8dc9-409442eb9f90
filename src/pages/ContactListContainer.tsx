import ToolBar from '@/components/ContactList/ToolBar';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import Breadcrumb from '@/components/Breadcrumb';
import { TabsContainer } from '@/components/TabsContainer';
import ContactList from '@/pages/ContactList';
import { RiContactsBook2Line, RiContactsBookLine } from '@remixicon/react';
import { DrawerWrap } from '@/components/DrawerWrap';
import { NotiContainer } from '@/components/Notification/NotiContainer';
import { writeFile } from '@/utils/xlsx';
import { getSelector, useAppDispatch, useAppSelector } from '@/hooks/reduxHooks';
import { setDataType, setOpenDrawer } from '@/store/contactList/contactListSlice';
import { LimitAlert } from '@/components/LimitAlert';
import { SegmentLog } from '@/components/SegmentLog/SegmentLog';
import { Box } from '@/components/Box';
import { useLocation, useSearchParams } from 'react-router-dom';
import { filterEmptyParams } from '@/utils/helper';
import { defaultFilter } from '@/constants/contact-list';

const ContactListContainer: React.FC = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const dispatch = useAppDispatch();
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_, setSearchParams] = useSearchParams();
  const [exportData, setExportData] = useState<{ [key: string]: string }[]>([]);
  const { openNotification, isDataType } = useAppSelector(getSelector('contactList'));
  // const [enabled, setEnabled] = useState(false);
  //
  // const handleGetList = () => {
  //   return get<TContactItems>({
  //     endpoint: ENDPOINTS.contact_list,
  //     params: {
  //       page: 1,
  //       limit: 1,
  //       order_by: '-date_created,full_name',
  //       reminder_overdue: true,
  //     },
  //   });
  // };

  useEffect(() => {
    if (isDataType === 'DATASET') {
      dispatch(setDataType('AUDIENCE'));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.pathname]);

  // const { data: contactListDataResponse } = useQuery({
  //   queryKey: [QUERY_KEY.CONTACT_LIST_OVERDUE, enabled],
  //   refetchOnMount: false,
  //   enabled: enabled,
  //   refetchInterval: MINUTE,
  //   staleTime: MINUTE,
  //   queryFn: () => handleGetList(),
  // });
  // const countContact = contactListDataResponse?.data?.data?.count || listOverdue.count;
  // const handleViewOverdue = () => {
  //   const contact = contactListDataResponse?.data;
  //   dispatch(
  //     setListOverdue({
  //       items: contact?.data?.items || listOverdue.items || [],
  //       count: contact?.data?.count || listOverdue.count || 0,
  //       isOverdue: true,
  //     }),
  //   );
  // };

  // useEffect(() => {
  //   const timer = setTimeout(() => {
  //     setEnabled(true);
  //   }, MINUTE);
  //   return () => clearTimeout(timer); // Cleanup timer on unmount
  // }, []);

  return (
    <div>
      <Breadcrumb />
      <div className="text-left mb-6">
        <div className="flex justify-between items-center">
          <div className="flex-1 h-[64px]">
            <h3 className="text-2xl font-medium mb-2">{t('contactList.titlePage')}</h3>
            <p className="text-base font-normal text-tertiary-foreground tracking-[0.2px]">
              {t('contactList.descriptionPage')}
            </p>
          </div>
          <div className="h-[64px] flex-col flex items-end justify-between relative">
            {/*{countContact > 0 && (*/}
            {/*  <Button*/}
            {/*    className="relative py-0 h-auto hover:bg-transparent pr-5"*/}
            {/*    variant={'ghost'}*/}
            {/*    onClick={() => handleViewOverdue()}*/}
            {/*  >*/}
            {/*    {t('contactList.totalContactOverdue')}*/}
            {/*    <span className="text-primary font-semibold">{countContact}</span>*/}
            {/*    <span className="absolute flex h-3 w-3 right-[0]">*/}
            {/*      <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-brand opacity-75"></span>*/}
            {/*      <span className="relative inline-flex rounded-full h-3 w-3 bg-brand"></span>*/}
            {/*    </span>*/}
            {/*  </Button>*/}
            {/*)}*/}
            <Box className="gap-2">
              {/*<ReportContact />*/}
              <SegmentLog />
            </Box>
          </div>
        </div>
      </div>
      <ToolBar onExportData={() => writeFile({ defaultData: exportData })} />
      <LimitAlert />
      <TabsContainer
        isVertical={false}
        onChange={(value: string) => {
          setSearchParams(filterEmptyParams(defaultFilter));
          dispatch(setDataType(value.toUpperCase()));
        }}
        listTabs={[
          {
            title: `${t('contactList.titlePage')}`,
            value: 'audience',
            icon: <RiContactsBook2Line size={20} />,
            child: <ContactList setExportData={setExportData} isDataSet={false} />,
          },
          {
            title: `${t('contactList.titleDatasetPage')}`,
            value: 'dataset',
            icon: <RiContactsBookLine size={20} />,
            child: <ContactList setExportData={setExportData} isDataSet={true} />,
          },
          // {
          //   title: `${t('contactList.archivedTitlePage')}`,
          //   value: 'trash',
          //   icon: <RiArchiveLine size={20} />,
          //   child: <TrashContact />,
          // },
        ]}
      />
      <DrawerWrap
        open={openNotification}
        onOpenChange={(open) => {
          dispatch(setOpenDrawer(open));
        }}
        trigger={<div></div>}
        content={<NotiContainer />}
      />
    </div>
  );
};

export default ContactListContainer;
