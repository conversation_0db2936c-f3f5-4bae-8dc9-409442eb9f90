import { Box } from '@/components/Box';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';

const NotFound = () => {
  return (
    <Box variant="col-start" className="w-full h-full flex justify-center items-center gap-12">
      <svg
        width="180"
        height="68"
        viewBox="0 0 180 68"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clipPath="url(#clip0_1102_106367)">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M42.7649 29.0685C44.3295 29.0685 45.3355 30.3778 45.3355 32.4095C45.3355 34.4412 44.3178 35.6757 42.7649 35.6757C41.212 35.6757 40.2469 34.444 40.2469 32.4009C40.2469 30.3577 41.2003 29.0714 42.7649 29.0714V29.0685ZM37.5301 39.5175C37.5301 39.8024 37.5944 40.0873 37.767 40.3721C38.5069 41.5606 40.2557 42.4326 42.5953 42.4441C46.2918 42.4642 48.453 40.6945 48.453 37.7563V28.2368C48.453 27.1922 47.7979 26.6771 46.9001 26.6771C46.0023 26.6771 45.3238 27.1951 45.3238 28.2368V28.8555H45.2624C44.6629 27.5836 43.4142 26.7289 41.7618 26.7289C38.8052 26.7289 37.0505 28.916 37.0505 32.3635C37.0505 35.811 38.852 37.9577 41.7092 37.9577C43.3703 37.9577 44.7068 37.152 45.2419 35.9376H45.3034V37.739C45.3034 39.1491 44.2973 40.0757 42.5836 40.0642C41.5454 40.0556 40.7295 39.6211 39.8843 38.8326C39.5275 38.5189 39.2088 38.3894 38.7993 38.3894C38.0156 38.3894 37.5243 38.87 37.5243 39.5146L37.5301 39.5175ZM22.0626 34.5448C21.6561 33.9606 21.2993 33.7361 20.8197 33.7361C20.1558 33.7361 19.6645 34.2311 19.6645 34.9131C19.6645 36.6973 21.9193 38.3376 24.8584 38.3376C28.1367 38.3376 30.4441 36.5333 30.4441 33.952C30.4441 32.0843 29.0842 30.5505 27.274 30.3778V30.1937C28.7772 29.9778 29.985 28.5102 29.985 26.9102C29.985 24.6109 27.8735 22.9994 24.8671 22.9994C22.0216 22.9994 19.9014 24.5879 19.9014 26.3145C19.9014 27.0138 20.4015 27.5001 21.0975 27.5001C21.6093 27.5001 21.9924 27.2728 22.3199 26.7346C22.896 25.762 23.7354 25.267 24.8116 25.267C26.1949 25.267 27.1541 26.0785 27.1541 27.267C27.1541 28.4555 26.1744 29.3188 24.835 29.3188H23.8231C23.1593 29.3188 22.6913 29.785 22.6913 30.4527C22.6913 31.1203 23.1709 31.6066 23.8231 31.6066H24.8905C26.4902 31.6066 27.5694 32.5131 27.5694 33.8541C27.5694 35.1951 26.5136 36.0671 24.8818 36.0671C23.6623 36.0671 22.6913 35.5376 22.0626 34.5448ZM32.035 38.3376V30.8757C32.035 29.0023 33.5791 27.4857 35.483 27.4857V34.9477C35.483 36.821 33.9389 38.3376 32.035 38.3376ZM34.9712 23.4915C35.638 24.1505 35.638 25.2181 34.9712 25.8771C34.3044 26.5332 33.2165 26.5332 32.5468 25.8771C31.88 25.2181 31.88 24.1505 32.5468 23.4915C33.2136 22.8353 34.3015 22.8353 34.9712 23.4915ZM33.9623 11.2439C46.2597 11.2439 56.2263 21.1289 56.2263 33.3246C56.2263 45.5204 46.2567 55.4053 33.9623 55.4053C21.6678 55.4053 11.6982 45.5204 11.6982 33.3246C11.6982 21.1289 21.6678 11.2439 33.9623 11.2439Z"
            fill="#8F5CFF"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M85.2635 30.8498V40.024C87.6353 40.024 89.5567 38.1592 89.5567 35.8542V26.68C87.1849 26.68 85.2635 28.5448 85.2635 30.8498ZM101.825 33.0599C101.825 30.5304 100.573 28.9016 98.6254 28.9016C96.6777 28.9016 95.4904 30.5016 95.4904 33.0484C95.4904 35.5952 96.6924 37.1261 98.6254 37.1261C100.559 37.1261 101.825 35.6038 101.825 33.0599ZM92.1039 41.9147C92.1039 42.2657 92.1858 42.6197 92.3963 42.9708C93.3146 44.4557 95.4933 45.5435 98.412 45.555C103.012 45.5809 105.706 43.3794 105.706 39.719V27.8656C105.706 26.5678 104.89 25.9232 103.773 25.9232C102.655 25.9232 101.813 26.5678 101.813 27.8656V28.6369H101.734C100.991 27.0484 99.4326 25.9865 97.3767 25.9865C93.6948 25.9865 91.5102 28.7146 91.5102 33.0052C91.5102 37.2959 93.7533 39.9722 97.3124 39.9722C99.38 39.9722 101.044 38.965 101.714 37.4542H101.793V39.6988C101.793 41.4571 100.541 42.6082 98.4061 42.5967C97.1135 42.5852 96.0987 42.0442 95.0459 41.06C94.6043 40.6686 94.2036 40.5103 93.6977 40.5103C92.7209 40.5103 92.1097 41.1118 92.1097 41.9147H92.1039ZM75.4227 37.0772C77.7418 37.0772 78.9964 36.1045 78.9964 34.2973C78.9964 32.4901 77.7038 31.5865 75.3262 31.5865H71.9542V37.083H75.4227V37.0772ZM74.8495 28.9707C76.996 28.9707 78.2214 28.0412 78.2214 26.4412C78.2214 24.8412 77.1423 24.0527 75.2589 24.0527H71.9542V28.9707H74.8465H74.8495ZM67.9272 37.9549V23.1577C67.9272 21.8627 68.673 21.0973 69.9393 21.0973H76.3C79.9117 21.0973 82.1811 22.9188 82.1811 25.8196C82.1811 27.8915 80.6048 29.6239 78.5694 29.9232V30.0297C81.1839 30.2254 83.0994 32.1016 83.0994 34.6053C83.0994 37.9377 80.5551 40.024 76.4462 40.024H69.9393C68.6759 40.024 67.9302 39.2672 67.9272 37.9549ZM88.922 24.6771C89.7555 23.857 89.7555 22.5304 88.922 21.7102C88.0886 20.8901 86.7404 20.8901 85.904 21.7102C85.0705 22.5304 85.0705 23.857 85.904 24.6771C86.7375 25.4973 88.0856 25.4973 88.922 24.6771ZM108.575 36.7376C108.37 36.3261 108.279 35.9232 108.279 35.5463C108.279 34.5678 108.917 33.9549 109.975 33.9549C110.739 33.9549 111.224 34.2571 111.61 34.9448C112.262 36.2139 113.257 36.965 115.169 36.965C117.082 36.965 118.357 35.8887 118.357 34.3808C118.369 32.6254 117.055 31.647 114.824 31.647H114.02C113.093 31.647 112.552 31.1088 112.552 30.2944C112.552 29.48 113.087 28.9505 114.02 28.9505H114.771C116.696 28.9505 117.947 27.9232 117.947 26.4412C117.947 24.9591 116.953 24.021 115.09 24.021C113.534 24.021 112.59 24.6627 111.993 25.9404C111.572 26.7923 111.125 27.1088 110.259 27.1088C109.189 27.1088 108.642 26.493 108.642 25.5779C108.642 25.1491 108.732 24.7865 108.923 24.3606C109.741 22.5908 111.829 21.1116 115.081 21.1116C118.971 21.1116 121.597 23.0052 121.597 25.9059C121.597 28.2052 119.924 29.7074 117.76 30.1448V30.2167C120.501 30.4642 122.273 32.0498 122.273 34.5477C122.273 37.8369 119.378 40.0182 115.105 40.0182C111.595 40.0182 109.443 38.5247 108.577 36.7463V36.7348H108.575V36.7376ZM166.383 22.139V29.8196C166.383 30.4642 166.742 30.8441 167.345 30.8441C167.947 30.8441 168.295 30.4642 168.295 29.8196V22.139C168.295 21.4944 167.95 21.1145 167.339 21.1145C166.728 21.1145 166.383 21.4944 166.383 22.139ZM161.502 23.0167L162.727 26.8959H160.174L161.408 23.0167H161.502ZM157.507 29.2987C157.387 29.6585 157.337 29.8772 157.337 30.0642C157.337 30.5419 157.714 30.8585 158.223 30.8585C158.732 30.8585 159.016 30.5995 159.215 29.9376L159.753 28.3059H163.148L163.692 29.9376C163.882 30.5908 164.18 30.8585 164.698 30.8585C165.242 30.8585 165.605 30.5362 165.605 30.0297C165.605 29.8311 165.555 29.6009 165.456 29.2987L163.019 22.3692C162.715 21.4714 162.297 21.1059 161.49 21.1059C160.683 21.1059 160.25 21.4771 159.946 22.3692L157.51 29.2987H157.507ZM131.02 30.4959C132.962 30.4959 134.374 31.8484 134.374 33.7563C134.363 35.5779 132.871 37.0081 130.982 37.0081C129.093 37.0081 127.613 35.5261 127.613 33.6815C127.613 31.8369 129.078 30.4959 131.017 30.4959H131.02ZM123.361 30.7059C123.361 32.8153 123.741 34.611 124.443 36.0153H124.446H124.449C125.8 38.6743 128.122 40.0182 131.043 40.0182C135.149 40.0182 138.132 37.3477 138.132 33.5952C138.132 30.1966 135.699 27.6987 132.14 27.6987C129.654 27.6987 127.844 28.9275 127.13 30.6081H127.054V30.4211C127.078 26.5592 128.42 24.0987 131.043 24.0987C132.31 24.0987 133.201 24.5649 134.041 25.6038C134.599 26.2455 135.059 26.5707 135.825 26.5707C136.828 26.5707 137.43 25.9174 137.43 25.0771C137.43 24.7404 137.337 24.4153 137.161 24.0613C136.293 22.3663 134.099 21.0973 131.052 21.0973C126.27 21.0973 123.364 24.7088 123.364 30.7059H123.361ZM150.216 30.5304C150.216 26.4009 148.891 24.1332 146.634 24.1332C144.376 24.1332 143.036 26.4181 143.036 30.5304C143.036 34.6427 144.361 36.9794 146.634 36.9794C148.906 36.9794 150.216 34.6801 150.216 30.5304ZM139.121 30.5304C139.121 24.6829 141.99 21.0973 146.631 21.0973C151.272 21.0973 154.129 24.6714 154.129 30.5189C154.129 36.3664 151.298 40.0182 146.631 40.0182C141.963 40.0182 139.121 36.3894 139.121 30.5362V30.5304Z"
            fill="#6B7183"
          />
        </g>
        <defs>
          <clipPath id="clip0_1102_106367">
            <rect
              width="156.597"
              height="44.1614"
              fill="white"
              transform="translate(11.6982 11.2439)"
            />
          </clipPath>
        </defs>
      </svg>
      <svg
        width="416"
        height="129"
        viewBox="0 0 416 129"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M13.1549 88.5746C13.5967 87.4569 15.1821 87.4569 15.6239 88.5746L18.682 96.3112C18.8169 96.6525 19.0876 96.9226 19.4296 97.0572L27.1837 100.108C28.3039 100.549 28.3039 102.131 27.1837 102.572L19.4296 105.623C19.0876 105.758 18.8169 106.028 18.682 106.369L15.6239 114.106C15.1821 115.223 13.5967 115.223 13.1549 114.106L10.0967 106.369C9.96183 106.028 9.6911 105.758 9.3491 105.623L1.59504 102.572C0.47483 102.131 0.47483 100.549 1.59504 100.108L9.3491 97.0572C9.6911 96.9226 9.96183 96.6525 10.0967 96.3112L13.1549 88.5746Z"
          fill="url(#paint0_linear_1100_106231)"
        />
        <path
          d="M28.337 110.968C28.6315 110.223 29.6884 110.223 29.983 110.968L32.0217 116.126C32.1117 116.354 32.2921 116.534 32.5201 116.623L37.6895 118.658C38.4363 118.951 38.4363 120.006 37.6895 120.3L32.5201 122.334C32.2921 122.424 32.1117 122.604 32.0217 122.831L29.983 127.989C29.6884 128.734 28.6315 128.734 28.337 127.989L26.2982 122.831C26.2083 122.604 26.0278 122.424 25.7998 122.334L20.6304 120.3C19.8836 120.006 19.8836 118.951 20.6304 118.658L25.7998 116.623C26.0278 116.534 26.2083 116.354 26.2982 116.126L28.337 110.968Z"
          fill="url(#paint1_linear_1100_106231)"
        />
        <path
          d="M58.2495 102.701V82.4401L102.983 13.0481H121.271V40.553H110.66L84.4816 81.3903V82.2302H149.262V102.701H58.2495ZM110.98 120.548V96.5075L111.513 87.6367V13.0481H136.199V120.548H110.98Z"
          fill="url(#paint2_linear_1100_106231)"
        />
        <path
          d="M59.9238 100.929V83.4009L105.322 14.1622H120.933V38.424H111.694L83.0745 82.2701V83.0925H147.588V100.929H59.9238ZM112.119 119.434V95.5832L112.544 87.8215V14.1622H134.102V119.434H112.119Z"
          fill="url(#paint3_linear_1100_106231)"
        />
        <path
          d="M206.845 120.548C196.928 120.513 188.362 118.256 181.146 113.777C173.931 109.298 168.368 102.841 164.458 94.4079C160.548 85.9745 158.611 75.8614 158.646 64.0686C158.682 52.2408 160.637 42.1976 164.511 33.9392C168.421 25.6807 173.966 19.3994 181.146 15.0952C188.362 10.791 196.928 8.63892 206.845 8.63892C216.762 8.63892 225.329 10.8085 232.544 15.1477C239.76 19.4519 245.323 25.7332 249.233 33.9917C253.142 42.2501 255.08 52.2758 255.044 64.0686C255.044 75.9313 253.089 86.0794 249.179 94.5129C245.269 102.946 239.707 109.403 232.491 113.882C225.311 118.326 216.762 120.548 206.845 120.548ZM206.845 96.8224C211.964 96.8224 216.158 94.1979 219.428 88.9489C222.734 83.6649 224.369 75.3714 224.333 64.0686C224.333 56.6849 223.587 50.6486 222.094 45.9594C220.601 41.2703 218.54 37.806 215.909 35.5664C213.279 33.2918 210.258 32.1545 206.845 32.1545C201.727 32.1545 197.55 34.709 194.316 39.8181C191.081 44.9271 189.428 53.0106 189.357 64.0686C189.322 71.5922 190.05 77.786 191.543 82.6501C193.036 87.4792 195.098 91.0485 197.728 93.3581C200.394 95.6677 203.433 96.8224 206.845 96.8224Z"
          fill="url(#paint4_linear_1100_106231)"
        />
        <path
          d="M206.845 117.231C197.675 117.198 189.784 115.097 183.173 110.928C176.597 106.758 171.532 100.72 167.977 92.811C164.458 84.9025 162.716 75.3891 162.752 64.2709C162.752 53.1857 164.511 43.7385 168.03 35.9293C171.585 28.12 176.65 22.1804 183.226 18.1103C189.837 14.0071 197.71 11.9556 206.845 11.9556C215.98 11.9556 223.836 14.0071 230.412 18.1103C237.023 22.2135 242.106 28.1697 245.66 35.9789C249.215 43.7551 250.974 53.1857 250.939 64.2709C250.939 75.4222 249.162 84.9521 245.607 92.8606C242.088 100.769 237.041 106.808 230.465 110.977C223.889 115.147 216.016 117.231 206.845 117.231ZM206.845 99.4125C213.101 99.4125 218.095 96.484 221.828 90.6271C225.56 84.7701 227.408 75.9847 227.373 64.2709C227.373 56.5609 226.519 50.1414 224.813 45.0125C223.143 39.8835 220.761 36.0285 217.669 33.4475C214.612 30.8665 211.004 29.576 206.845 29.576C200.625 29.576 195.649 32.4714 191.917 38.2621C188.184 44.0529 186.3 52.7225 186.265 64.2709C186.265 72.0801 187.1 78.5988 188.771 83.8271C190.477 89.0222 192.876 92.9268 195.969 95.5409C199.061 98.1219 202.687 99.4125 206.845 99.4125Z"
          fill="url(#paint5_linear_1100_106231)"
        />
        <path
          d="M264.428 102.701V82.4401L309.161 13.0481H327.449V40.553H316.839L290.66 81.3903V82.2302H355.441V102.701H264.428ZM317.159 120.548V96.5075L317.692 87.6367V13.0481H342.378V120.548H317.159Z"
          fill="url(#paint6_linear_1100_106231)"
        />
        <path
          d="M266.103 100.929V83.4009L311.501 14.1622H327.112V38.424H317.873L289.253 82.2701V83.0925H353.767V100.929H266.103ZM318.298 119.434V95.5832L318.723 87.8215V14.1622H340.28V119.434H318.298Z"
          fill="url(#paint7_linear_1100_106231)"
        />
        <path
          d="M391.482 40.6123C391.041 41.73 389.455 41.73 389.013 40.6123L385.955 32.8756C385.82 32.5344 385.55 32.2643 385.208 32.1297L377.454 29.0784C376.333 28.6376 376.333 27.0558 377.454 26.615L385.208 23.5637C385.55 23.4291 385.82 23.159 385.955 22.8177L389.013 15.0811C389.455 13.9634 391.041 13.9634 391.482 15.0811L394.54 22.8178C394.675 23.159 394.946 23.4291 395.288 23.5637L403.042 26.615C404.162 27.0558 404.162 28.6376 403.042 29.0784L395.288 32.1297C394.946 32.2643 394.675 32.5344 394.54 32.8756L391.482 40.6123Z"
          fill="url(#paint8_linear_1100_106231)"
        />
        <path
          d="M406.978 18.2186C406.683 18.9637 405.627 18.9637 405.332 18.2186L403.293 13.0609C403.203 12.8334 403.023 12.6533 402.795 12.5636L397.625 10.5294C396.879 10.2355 396.879 9.18095 397.625 8.88707L402.795 6.85288C403.023 6.76316 403.203 6.58308 403.293 6.35559L405.332 1.19783C405.627 0.452699 406.683 0.452699 406.978 1.19783L409.017 6.35559C409.107 6.58308 409.287 6.76316 409.515 6.85288L414.685 8.88708C415.431 9.18095 415.431 10.2355 414.685 10.5294L409.515 12.5636C409.287 12.6533 409.107 12.8334 409.017 13.0609L406.978 18.2186Z"
          fill="url(#paint9_linear_1100_106231)"
        />
        <defs>
          <linearGradient
            id="paint0_linear_1100_106231"
            x1="25.652"
            y1="112.57"
            x2="3.18171"
            y2="90.0487"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#E06C14" />
            <stop offset="1" stopColor="#F48E2F" />
          </linearGradient>
          <linearGradient
            id="paint1_linear_1100_106231"
            x1="36.6684"
            y1="126.965"
            x2="21.6882"
            y2="111.951"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#E06C14" />
            <stop offset="1" stopColor="#F48E2F" />
          </linearGradient>
          <linearGradient
            id="paint2_linear_1100_106231"
            x1="103.756"
            y1="13.0481"
            x2="103.756"
            y2="112.061"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#5A18BF" />
            <stop offset="1" stopColor="#A585FF" />
          </linearGradient>
          <linearGradient
            id="paint3_linear_1100_106231"
            x1="103.756"
            y1="14.1622"
            x2="103.756"
            y2="111.123"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#A585FF" />
            <stop offset="1" stopColor="#5A18BF" />
          </linearGradient>
          <linearGradient
            id="paint4_linear_1100_106231"
            x1="246.66"
            y1="110.781"
            x2="155.256"
            y2="32.0455"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#F48E2F" />
            <stop offset="1" stopColor="#E06C14" />
          </linearGradient>
          <linearGradient
            id="paint5_linear_1100_106231"
            x1="243.269"
            y1="108.044"
            x2="157.67"
            y2="36.3386"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#E06C14" />
            <stop offset="1" stopColor="#F48E2F" />
          </linearGradient>
          <linearGradient
            id="paint6_linear_1100_106231"
            x1="309.935"
            y1="13.0481"
            x2="309.935"
            y2="112.061"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#5A18BF" />
            <stop offset="1" stopColor="#A585FF" />
          </linearGradient>
          <linearGradient
            id="paint7_linear_1100_106231"
            x1="309.935"
            y1="14.1622"
            x2="309.935"
            y2="111.123"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#A585FF" />
            <stop offset="1" stopColor="#5A18BF" />
          </linearGradient>
          <linearGradient
            id="paint8_linear_1100_106231"
            x1="378.985"
            y1="16.6174"
            x2="401.455"
            y2="39.1382"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#E06C14" />
            <stop offset="1" stopColor="#F48E2F" />
          </linearGradient>
          <linearGradient
            id="paint9_linear_1100_106231"
            x1="398.647"
            y1="2.22199"
            x2="413.627"
            y2="17.2359"
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor="#E06C14" />
            <stop offset="1" stopColor="#F48E2F" />
          </linearGradient>
        </defs>
      </svg>
      <Box variant="col-start" className="gap-6 w-full items-center">
        <div className="text-primary text-lg font-medium">Page not found!</div>
        <p className="text-secondary text-sm font-normal text-center">
          Sorry, we couldn’t find the page you’re looking for. Try going back to our homepage.
        </p>
        <Link to={`/`}>
          <Button className="w-full py-1 rounded-lg">Go to Homepage</Button>
        </Link>
      </Box>
    </Box>
  );
};

export default NotFound;
