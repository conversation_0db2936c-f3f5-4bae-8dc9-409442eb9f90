import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Provider } from 'react-redux';
import { Toaster } from './components/ui/toaster';
import AppRouter from './routes';
import AppContextProvider from './context/AppContextProvider';
import { crmStore } from './store';
import './config-translation';
import { Toast as SonnerProvider } from './components/ui/sonner';
import { SegmentProvider } from '@/pages/context/SegmentContext';
import { CallProvider } from '@/pages/context/CallContext';
import 'ckeditor5/ckeditor5.css';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      staleTime: 1000,
    },
  },
});

export default function App() {

  return (
    <Provider store={crmStore}>
      <QueryClientProvider client={queryClient}>
        <AppContextProvider>
          <SegmentProvider>
            <CallProvider>
              <AppRouter />
            </CallProvider>
          </SegmentProvider>
          <Toaster />
          <SonnerProvider />
        </AppContextProvider>
      </QueryClientProvider>
    </Provider>
  );
}
