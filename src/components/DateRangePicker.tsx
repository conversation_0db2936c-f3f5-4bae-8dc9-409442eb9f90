import { RiArrowDownSLine, RiCalendarLine } from '@remixicon/react';
import { Button } from './ui/button';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { t } from 'i18next';
import { cn } from '@/lib/utils';
import { fDateRange } from '@/utils/helper';
import { useEffect, useState } from 'react';
import { Calendar } from './ui/calendar';

type Props = {
  onChange: (date: { from: Date | null; to: Date | null }) => void;
  isDisabled?: boolean;
  defaultDate?: Date;
  placeholder?: string;
  className?: string;
  error?: string;
};

const DateRangePicker = ({
  onChange,
  isDisabled = false,
  className,
  defaultDate,
  placeholder,
  error,
}: Props) => {
  const [isOpen, setIsOpen] = useState(false);
  const [date, setDate] = useState<{ from: Date | null; to: Date | null }>({
    from: null,
    to: null,
  });
  const isSelected = date.from || date.to || isOpen;

  useEffect(() => {
    onChange(date);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [date]);

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          size={'lg'}
          variant={'outline'}
          className={cn(
            'flex relative h-10 items-center rounded-xl border p-2 bg-card',
            error && 'text-[#F53E3E] border-red-500',
            isDisabled && 'pointer-events-none',
            className,
          )}
        >
          {date.from || date.to
            ? fDateRange({ date: { from: date.from || new Date(), to: date.to || new Date() } })
            : ''}
          <div
            className={cn(
              'absolute flex text-sm gap-1 text-tertiary-foreground justify-start left-2 items-center duration-150',
              isSelected ? '-top-3 bg-card' : 'top-1/2 -translate-y-1/2',
            )}
          >
            <RiCalendarLine size={16} />
            {placeholder || t('common.button.date')}
          </div>
          <RiArrowDownSLine size={16} className="ml-auto" color="#6B7183" />
          {error && (
            <span className="text-[#F53E3E] -bottom-5 font-medium left-0 capitalize text-xs ml-2 absolute">
              {error}
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="min-w-[600px] p-2 flex rounded-xl bg-card border-tertiary"
        align="end"
      >
        <div>
          <Calendar
            mode="single"
            selected={date?.from || undefined}
            onSelect={(value) => {
              setDate({ ...date, from: value! });
            }}
            defaultMonth={defaultDate || new Date()}
          />
        </div>
        <div>
          <Calendar
            mode="single"
            selected={date?.to || undefined}
            onSelect={(value) => {
              setDate({ ...date, to: value! });
            }}
            defaultMonth={defaultDate || new Date()}
          />
        </div>
      </PopoverContent>
    </Popover>
  );
};
export default DateRangePicker;
