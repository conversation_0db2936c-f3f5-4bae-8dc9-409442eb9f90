import React from 'react';
import useAvatar from '@/hooks/useAvatar';
import { LoadingButtonIcon } from '@/components/Loading/LoadingButton';
import { cn } from '@/lib/utils';

type TAvatar = {
  uid: string;
  name: string;
  className?: string;
};
export const Avatar: React.FC<TAvatar> = ({ ...props }: TAvatar) => {
  const { uid, name, className } = props;
  const { avatar, loading } = useAvatar(uid);
  return !uid || !avatar ? (
    <div
      className={cn(
        'flex items-center justify-center font-bold text-lg text-white w-[32px] h-[32px] rounded-full bg-avatar',
        className,
      )}
    >
      {name ? name.charAt(0).toUpperCase() : ''}
    </div>
  ) : loading ? (
    <LoadingButtonIcon />
  ) : (
    <img
      className={cn('w-[32px] h-[32px] rounded-full', className)}
      src={avatar}
      alt={'fb_avatar'}
    />
  );
};
