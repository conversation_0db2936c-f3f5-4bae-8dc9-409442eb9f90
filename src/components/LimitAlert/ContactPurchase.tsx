import { Box } from '@/components/Box';
import {
  CONTACT_CODE,
  CONTACT_PURCHASE,
  ICreatePaymentResponse,
  ICretePaymentRequest,
  TContactPurchase,
} from '@/types/payment';
import { cn } from '@/lib/utils';
import React, { useState } from 'react';
import { RiCheckboxCircleFill, RiContactsLine, RiLoader2Line } from '@remixicon/react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { useMutation } from '@tanstack/react-query';
import { paymentAPI } from '@/apis/paymentApi';
import { TBaseResponse } from '@/types/ResponseApi';

interface IContactPurchaseProps {
  setOpenPurchase: (value: boolean) => void;
}

interface IPurchaseItemProps {
  isActive: boolean;
  contactPurchase: TContactPurchase;
  setSelectPackage: (value: CONTACT_CODE) => void;
}

export const ContactPurchase: React.FC<IContactPurchaseProps> = ({
  ...props
}: IContactPurchaseProps) => {
  const { setOpenPurchase } = props;
  const SUCCESS_PAYMENT = import.meta.env.REACT_APP_BIG360_URL + '/account/billing/success';
  const FAILED_PAYMENT = import.meta.env.REACT_APP_BIG360_URL + '/account/billing/failed';

  const { t } = useTranslation();
  const [selectPackage, setSelectPackage] = useState<CONTACT_CODE>('CONTACT_5000');

  const { mutate: purchaseMutation, isPending } = useMutation({
    mutationFn: async (payload: ICretePaymentRequest): Promise<ICreatePaymentResponse> => {
      return paymentAPI
        .createPaymentLink(payload)
        .then(
          (res) => (res as TBaseResponse<ICreatePaymentResponse>).data as ICreatePaymentResponse,
        );
    },
    onSuccess: (data: ICreatePaymentResponse) => {
      if (data) {
        window.open(data.url);
      }
      setOpenPurchase(false);
    },
    onError: (e) => e,
  });

  const handlePurchase = () => {
    purchaseMutation({
      contact_code: selectPackage,
      type: 'crm_contact',
      success_url: SUCCESS_PAYMENT,
      cancel_url: FAILED_PAYMENT,
    });
  };

  return (
    <div>
      <Box>
        {CONTACT_PURCHASE.map((item) => (
          <PurchaseItem
            key={item.contact_code}
            isActive={selectPackage === item.contact_code}
            contactPurchase={item}
            setSelectPackage={setSelectPackage}
          />
        ))}
      </Box>
      <div className="mt-4 w-fit ml-auto">
        <Button className="h-10 font-semibold" onClick={handlePurchase} disabled={isPending}>
          {isPending ? (
            <RiLoader2Line size={20} className="animate-spin" />
          ) : (
            t('limitAlert.proceedPayment')
          )}
        </Button>
      </div>
    </div>
  );
};

const PurchaseItem: React.FC<IPurchaseItemProps> = ({ ...props }: IPurchaseItemProps) => {
  const { isActive, contactPurchase, setSelectPackage } = props;
  const { contact_code, value, price } = contactPurchase;
  const { t } = useTranslation();
  return (
    <Button
      className={cn(
        'h-auto flex-1 bg-transparent cursor-pointer rounded-2xl border p-4 text-primary-crm hover:bg-transparent hover:border-[#20232C]',
        isActive ? '!border-[#8F5CFF]' : 'border-[#A7AAB1]',
      )}
      onClick={() => setSelectPackage(contact_code)}
    >
      <Box variant={'col-start'} className="gap-0">
        <Box className="relative bg-[#F0F0F0] rounded-xl p-2 gap-2 flex-1 w-full">
          <div className={cn('rounded-2xl p-[10px]', isActive ? 'bg-[#E2DAFF]' : 'bg-white ')}>
            <RiContactsLine color={isActive ? '#5A18BF' : '#20232C'} size={24} />
          </div>
          <div className="w-full text-left">
            <p className="text-lg font-medium">{value}</p>
            <p className="text-xs">{t('limitAlert.contactLimit')}</p>
          </div>
          {isActive && (
            <div className="absolute top-1 right-1">
              <RiCheckboxCircleFill size={24} color={'#8F5CFF'} />
            </div>
          )}
        </Box>
        <p className="ml-auto text-right mt-4 text-2xl font-medium">{price}</p>
      </Box>
    </Button>
  );
};
