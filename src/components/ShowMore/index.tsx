import * as React from 'react';
import { Toolt<PERSON>, Tooltip<PERSON>ontent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import SelectItem from '@/components/SelectItem';
import { IOptions } from '@/types/contactList';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';

const ShowMore = ({
  selected,
  onRemove,
  isShowMore,
  children,
  isRemoveClick,
  className,
}: {
  selected: IOptions[];
  onRemove: (value: string, e: React.MouseEvent) => void;
  isShowMore?: boolean;
  children?: React.ReactNode;
  isRemoveClick?: boolean;
  className?: string;
}) => {
  const { t } = useTranslation();
  return (
    <TooltipProvider delayDuration={100}>
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            className={cn(
              'z-20 w-fit h-[22px] px-1 bg-[#31333d14] text-primary flex items-center justify-center rounded-lg gap-0.5',
              className,
            )}
          >
            <span className="text-xs font-normal">
              +{selected.length} {isShowMore ? t('common.more') : ''}
            </span>
            {children}
          </div>
        </TooltipTrigger>
        <TooltipContent className="bg-card shadow z-50" side="top" align="start">
          <div className="max-w-[300px] flex flex-col gap-1">
            {selected.map((option) => (
              <SelectItem
                key={option.value}
                title={option.label}
                onClick={onRemove}
                value={option.value}
                isRemoveClick={isRemoveClick}
                color={option.color}
                className={cn(
                  'flex items-center justify-center w-fit h-[26px] text-center p-1 rounded-xl truncate bg-secondary-foreground_crm',
                )}
              />
            ))}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
export default ShowMore;
