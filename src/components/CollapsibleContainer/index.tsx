import React, { useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { RiArrowDropDownLine, RiArrowDropUpLine } from '@remixicon/react';

type TCollapsibleSection = {
  children: React.ReactNode;
  buttonClassName?: string;
};

const HEIGHT_TO_VIEW_MORE = 80;

const CollapsibleSection: React.FC<TCollapsibleSection> = ({ ...props }: TCollapsibleSection) => {
  const { children, buttonClassName } = props;
  const [isOpen, setIsOpen] = useState<boolean>(true);
  const [contentHeight, setContentHeight] = useState<number>(0);
  const contentRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    const checkHeight = () => {
      if (contentRef.current) {
        const height = (contentRef.current as HTMLDivElement).scrollHeight;
        setContentHeight(height);
      }
    };

    checkHeight();

    const resizeObserver = new ResizeObserver(checkHeight);
    if (contentRef.current) {
      resizeObserver.observe(contentRef.current);
    }

    return () => {
      if (contentRef.current) {
        // eslint-disable-next-line react-hooks/exhaustive-deps
        resizeObserver.unobserve(contentRef.current);
      }
    };
  }, []);

  const showCollapseButton = contentHeight > HEIGHT_TO_VIEW_MORE;

  return (
    <div className="w-full">
      {showCollapseButton && (
        <Button
          size={'lg'}
          className={cn('px-3 py-1 rounded-2xl float-right w-[40px] h-[40px]', buttonClassName)}
          variant={'secondary'}
          onClick={() => setIsOpen(!isOpen)}
        >
          {isOpen ? <RiArrowDropUpLine size={20} /> : <RiArrowDropDownLine size={20} />}
        </Button>
      )}

      <div
        ref={contentRef}
        className={`overflow-hidden transition-all duration-200 ease-in-out ${
          showCollapseButton ? (isOpen ? 'max-h-[120px]' : 'max-h-0') : 'max-h-none'
        }`}
      >
        {children}
      </div>
    </div>
  );
};
export default CollapsibleSection;
