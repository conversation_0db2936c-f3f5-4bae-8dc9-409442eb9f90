import React from 'react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { RiArrowDownSLine, RiArrowDropDownFill } from '@remixicon/react';
import { DualRangeSlider } from '@/components/ui/dual-range-slider';
import SelectItem from '@/components/SelectItem';
import { cn } from '@/lib/utils';
import { Option } from '@/types/contactList';

// const MORE_ITEMS = 2;

interface ISelectRange {
  data: Option[];
  title: string;
  icon: React.ReactNode;
  selected: string[];
  onChange: (selected: string[]) => void;
  className?: string;
}

const SelectRange: React.FC<ISelectRange> = ({ ...props }: ISelectRange) => {
  const { title, icon, selected, className, onChange } = props;
  const handleRemove = (valueToRemove: string[], e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onChange(valueToRemove);
  };
  const handleSelect = (value: number[]) => {
    onChange(value.map((item) => item.toString()));
  };
  // const showPlaceholderTop = selected.length > 0;
  const filterValue = selected
    .map((item, index) =>
      index === 1 && item === '' ? '∞' : index === 0 && item === '' ? 0 : item,
    )
    .join(' -> ');

  return (
    <Popover>
      <PopoverTrigger asChild>
        <button
          className={cn(
            'min-w-[203px] h-10 cursor-pointer relative flex items-center justify-center gap-1 px-2\tborder-[1px] border-tertiary rounded-xl',
            className,
          )}
        >
          <span
            className={cn(
              'absolute left-2 flex gap-1 items-center px-1 bg-white text-sm transition-all duration-300 pointer-events-none text-filter',
              // showPlaceholderTop ? '-top-3 text-xs' : 'top-2',
              'top-2',
            )}
          >
            <span>{icon}</span>
            <span>{title}</span>
          </span>

          <div className="flex justify-end items-center w-[fit] ml-auto bg-white z-10">
            <div className="flex gap-1 items-center">
              {selected.length > 0 && (
                <SelectItem
                  title={filterValue}
                  onClick={(_value, e) => handleRemove([], e)}
                  value={filterValue}
                />
              )}
            </div>
          </div>
          {selected.length === 0 && (
            <RiArrowDownSLine size={20} className="ml-2 shrink-0 opacity-50" />
          )}
        </button>
      </PopoverTrigger>
      <PopoverContent
        isShowArrow={false}
        align={'start'}
        className="w-[270px] border-[1px] border-tertiary rounded-2xl shadow-filter"
      >
        <div className="flex">
          <DualRangeSlider
            label={(value) => {
              return (
                <div className="relative">
                  <div className="absolute left-1/2 translate-y-[-7px] translate-x-[-50%] h-[26px] min-w-[30px] bg-create rounded-xl text-center z-20">
                    <span className="text-primary-foreground font-normal text-xs">
                      {isNaN(value ?? 0) ? 0 : value}
                    </span>
                  </div>
                  <div className="absolute left-1/2 translate-y-[9px] translate-x-[-50%]">
                    <RiArrowDropDownFill />
                  </div>
                </div>
              );
            }}
            value={selected.length > 0 ? selected.map((item) => parseInt(item)) : [0, 0]}
            step={1}
            onValueChange={(value) => {
              if (value[0] === 0 && value[1] === 0) {
                handleSelect([]);
              } else {
                handleSelect(value);
              }
            }}
          />
        </div>
      </PopoverContent>
    </Popover>
  );
};
export default SelectRange;
