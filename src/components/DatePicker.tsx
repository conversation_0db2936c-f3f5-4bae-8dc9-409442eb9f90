import { RiArrowDownSLine, RiCalendarLine } from '@remixicon/react';
import { Button } from './ui/button';
import { Calendar } from './ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { t } from 'i18next';
import { cn } from '@/lib/utils';
import { useEffect, useState } from 'react';
import { formatDate } from '@/utils/helper';

type Props = {
  onChange: (date: string) => void;
  isDisabled?: boolean;
  defaultDate?: Date;
  error?: string | boolean;
  disableDate?: (date: Date) => boolean;
  isHideLabel?: boolean;
  label?: string;
  className?: string;
  placeholder?: string;
};

const DatePicker = ({ onChange, isDisabled = false, defaultDate, error, disableDate, isHideLabel, label, className, placeholder }: Props) => {
  const [isOpen, setIsOpen] = useState(false);
  const [date, setDate] = useState<Date | null>(defaultDate || null);
  const isSelected = date || isOpen;

  useEffect(() => {
    setDate(defaultDate || null);
  }, [defaultDate]);

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          size={'lg'}
          variant={'outline'}
          disabled={isDisabled}
          className={cn(
            'flex relative h-10 items-center rounded-xl border p-2 bg-card',
            error && (typeof error === 'string' ? 'border-red-400' : 'text-[#F53E3E]'),
            isDisabled && 'pointer-events-none disabled:cursor-not-allowed',
            className,
          )}
        >
          <span className={isHideLabel ? 'ml-5' : ''}>
            {formatDate(date?.toDateString() ?? '', '/')}
          </span>
          <div
            className={cn(
              'absolute flex text-sm gap-1 text-tertiary-foreground justify-start left-2 items-center duration-150',
              !isHideLabel && isSelected ? '-top-3 bg-card' : 'top-1/2 -translate-y-1/2',
            )}
          >
            <RiCalendarLine size={16} />
            {!isHideLabel && (label || t('common.button.date'))}
            {isHideLabel && placeholder && !date && (
              <span className="text-tertiary">{placeholder}</span>
            )}
          </div>
          <RiArrowDownSLine size={16} className="ml-auto" color="#6B7183" />
          {typeof error === 'string' && (
            <div className="text-[#F53E3E] absolute -bottom-[18px] text-xs left-1">{error}</div>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="p-0 rounded-xl border-tertiary" align="end">
        <Calendar
          mode="single"
          selected={date || undefined}
          onSelect={(value) => {
            if (!!value) {
              setDate(value || null);
              onChange(formatDate(value?.toDateString() ?? '', '/'));
            }
          }}
          disabled={disableDate ?? undefined}
          defaultMonth={defaultDate || new Date()}
        />
      </PopoverContent>
    </Popover>
  );
};
export default DatePicker;
