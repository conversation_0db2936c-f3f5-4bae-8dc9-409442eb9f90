import { Drawer, DrawerContent, DrawerTrigger } from '@/components/ui/drawer';
import React from 'react';

type TDrawerWrap = {
  trigger: React.ReactNode;
  content: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
};
export const DrawerWrap: React.FC<TDrawerWrap> = ({ ...props }: TDrawerWrap) => {
  const { trigger, content, open ,onOpenChange} = props;
  return (
    <div>
      <Drawer open={open} onOpenChange={onOpenChange}>
        <DrawerTrigger asChild>{trigger}</DrawerTrigger>
        <DrawerContent>{content}</DrawerContent>
      </Drawer>
    </div>
  );
};
