import { RiLinkM, RiLoader2Line } from '@remixicon/react';
import { Button } from './ui/button';
import { useTranslation } from 'react-i18next';

type Props = {
  onSelect: () => void;
  value: string;
  loading?: boolean;
  disabled?: boolean;
  buttonText?: string;
};

const SelectAccountModal = ({ onSelect, value, loading = false, disabled = false, buttonText }: Props) => {
  const { t } = useTranslation();
  return (
    <div className="flex flex-col items-center justify-center">
      <div className="p-5 rounded-full bg-secondary">
        <RiLinkM size={40} />
      </div>
      <div className="flex items-center justify-center flex-col gap-2 mt-4">
        <div className="font-medium text-base text-center">
          {t('common.select.title', { value })}
        </div>
        <div className="text-sm text-secondary text-center">
          {t('common.select.subTitle', { value })}
        </div>
      </div>
      <Button
        className="p-3 rounded-xl mt-6"
        size={'lg'}
        onClick={onSelect}
        type="button"
        variant={'primary'}
        disabled={loading || disabled}
      >
        {loading ? (
          <RiLoader2Line size={20} className="animate-spin" />
        ) :buttonText}
      </Button>
    </div>
  );
};
export default SelectAccountModal;
