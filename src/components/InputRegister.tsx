import { cn } from '@/lib/utils';
import { RiErrorWarningLine } from '@remixicon/react';
import { InputHTMLAttributes, forwardRef } from 'react';
import { UseFormRegisterReturn } from 'react-hook-form';

interface Props extends Omit<InputHTMLAttributes<HTMLInputElement>, 'className'> {
  label?: string;
  placeholder?: string;
  error?: string;
  className?: string;
  registration?: UseFormRegisterReturn;
}

const InputRegister = forwardRef<HTMLInputElement, Props>(
  ({ label, placeholder, className, error, required = false, registration, ...props }, ref) => {
    return (
      <div className={cn('flex flex-col gap-1 items-start w-full', className)}>
        {label && (
          <label
            htmlFor={registration?.name || props.id}
            className="capitalize text-sm text-gray-700"
          >
            {label}
            {required && (
              <span className="text-red-500 ml-1" aria-hidden="true">
                *
              </span>
            )}
          </label>
        )}

        <input
          {...props}
          {...registration}
          ref={ref}
          id={registration?.name || props.id}
          className={cn(
            'outline-none border h-10 w-full rounded-xl px-3 py-1',
            'text-tertiary-foreground placeholder:text-gray-400',
            'transition-colors duration-200',
            'focus:ring-2 focus:ring-brand focus:ring-opacity-50',
            error ? 'border-red-500' : 'border-gray-300 hover:border-gray-400',
          )}
          placeholder={placeholder}
          aria-invalid={error ? 'true' : 'false'}
          aria-describedby={error ? `${registration?.name}-error` : undefined}
        />

        {error && (
          <div
            id={`${registration?.name}-error`}
            className="text-red-500 text-xs flex items-center gap-1"
            role="alert"
          >
            <RiErrorWarningLine size={16} aria-hidden="true" />
            {error}
          </div>
        )}
      </div>
    );
  },
);

InputRegister.displayName = 'InputRegister';

export default InputRegister;
