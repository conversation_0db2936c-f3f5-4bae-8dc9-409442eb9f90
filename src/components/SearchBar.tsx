import { cn } from '@/lib/utils';
import React, { HTMLInputTypeAttribute } from 'react';
import { RiSearchLine } from '@remixicon/react';

type Props = {
  value: string;
  setSearchQuery: (value: string) => void;
  className?: string;
  iconSize?: number;
  placeholder?: string;
  icon?: React.ReactNode;
  type?: HTMLInputTypeAttribute;
};

const SearchBar = ({
  value,
  setSearchQuery,
  className = '',
  iconSize = 20,
  placeholder = '',
  icon,
  type = 'text',
}: Props) => {
  return (
    <div className={cn('flex items-center p-2 border border-big360Color-neutral-300 rounded-2xl gap-1 focus-within:border-big360Color-brand-500', className)}>
      {icon ?? <RiSearchLine size={iconSize} color={'#909498'} />}
      <input
        type={type}
        className="outline-none flex-1 flex-shrink-0 min-w-0 placeholder:text-filter placeholder:text-sm"
        onChange={(value) => setSearchQuery(value.target.value)}
        placeholder={placeholder}
        value={value}
      />
    </div>
  );
};
export default SearchBar;
