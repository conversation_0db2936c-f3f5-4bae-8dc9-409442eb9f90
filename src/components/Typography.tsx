import { cn } from '@/lib/utils';
import { cva, type VariantProps } from 'class-variance-authority';

const typographyVariants = cva('text-primary', {
  variants: {
    variant: {
      h1: 'text-[72px] leading-[80px] tracking-[-2.1px]',
      h2: 'text-[64px] leading-[72px] tracking-[-1.7px]',
      h3: 'text-[48px] leading-[56px] tracking-[-1px]',
      h4: 'text-[36px] leading-[44px] tracking-[-0.7px]',
      h5: 'text-[28px] leading-[36px] tracking-[-0.5px]',
      h6: 'text-[24px] leading-[32px] tracking-[-0.3px]',
      title: 'text-[20px] leading-[32px] tracking-[0.6px]',
    },
    fontWeight: {
      default: 'font-normal',
      medium: 'font-medium',
      semi: 'font-semibold',
      bold: 'font-bold',
      extra: 'font-extrabold',
    },
  },
  defaultVariants: {
    variant: 'title',
    fontWeight: 'default',
  },
});

export interface TypographyProps extends VariantProps<typeof typographyVariants> {
  content?: string;
  className?: string;
}

const Typography = ({ content, className, variant, fontWeight }: TypographyProps) => {
  return (
    <div className={cn('text-primary', typographyVariants({ variant, fontWeight }), className)}>
      {content}
    </div>
  );
};

export default Typography;
