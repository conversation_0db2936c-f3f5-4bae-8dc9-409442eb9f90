import { InputOTP, InputOTPGroup, InputOTPSlot } from '@/components/ui/input-otp';
import { cn } from '@/lib/utils';

type Props = {
  title: string;
  length: number;
  value: string;
  setValue: (value: string) => void;
  className?: string;
};

const InputOtp = ({ title, length = 0, value: otpCode, setValue, className }: Props) => {
  return (
    <div className={cn('flex flex-col items-start gap-2', className)}>
      <span className="text-sm text-secondary">{title}</span>
      <InputOTP value={otpCode} onChange={setValue} maxLength={length}>
        <InputOTPGroup className="gap-2">
          {Array.from({ length }).map((_, index) => (
            <InputOTPSlot
              className="w-12 h-12 text-4xl font-semibold shadow-none border !rounded-2xl"
              key={index}
              index={index}
            />
          ))}
        </InputOTPGroup>
      </InputOTP>
    </div>
  );
};
export default InputOtp;
