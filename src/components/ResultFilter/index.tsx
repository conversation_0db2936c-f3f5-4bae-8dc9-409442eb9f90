import { upperFirstChar } from '@/utils/helper';
import { monthSelectOptions, statusSelectOptions } from '@/utils/constants';
import { RiCloseCircleFill } from '@remixicon/react';
import ShowMore from '@/components/ShowMore';
import { TSegment } from '@/types/segment';
import { getSelector, useAppSelector } from '@/hooks/reduxHooks';
import { TagFillIcon } from '@/assets/TagFillIcon';
import { useTranslation } from 'react-i18next';

type TSelectFilter<T extends object> = {
  keyInSearch: string;
  title: string;
  result: string[];
  isAction: boolean;
  setSearchFilter?: (filter: T) => void;
  searchFilter?: T;
  isShowMore: boolean;
};

type TResultItemFilter<T extends object> = {
  keyInSearch: string;
  title?: string;
  result: {
    id: string | number;
    name: string;
    color?: string;
  }[];
  isAction: boolean;
  setSearchFilter?: (filter: T) => void;
  searchFilter?: T;
  isShowMore: boolean;
};
const limitIem = 2;

function ResultFilter<T extends object>({ ...props }: TSelectFilter<T>) {
  const { keyInSearch, isShowMore, title, result, isAction, searchFilter, setSearchFilter } = props;
  const { t } = useTranslation();
  const { allSegments, allDatasetSegments } = useAppSelector(getSelector('segment'));
  if (result.length === 0) {
    return null;
  }

  const getListDataById = () => {
    const mapOptions = (options: { value: string; label: string }[], id: string) => {
      const found = options.find((option) => option.value === id);
      return {
        id: found?.value ?? id,
        name: found?.label ?? id,
      };
    };

    const mapSegmentOptions = (id: string) => {
      const mergedSegment = allSegments.items.concat(allDatasetSegments.items)
      const segmentFind = mergedSegment.find(
        (segment: TSegment) => segment.id.toString() === id,
      );
      return {
        id: segmentFind?.id ?? id,
        name: segmentFind?.name ?? id,
        color: segmentFind?.color ?? '#004fd0',
      };
    };

    const optionMapping: {
      [key: string]: (id: string) => { id: string; name: string; color?: string };
    } = {
      birthMonth: (id) => mapOptions(monthSelectOptions, id),
      status: (id) => mapOptions(statusSelectOptions, id),
      segment: mapSegmentOptions,
    };

    return result.map((id) => optionMapping[title]?.(id) ?? { id, name: id });
  };

  return (
    <div className="flex gap-1">
      <div className="flex items-center justify-center gap-3 rounded-lg border-[1px] border-tertiary p-2">
        <span className="font-semibold text-sm">{t(`filter.${title}`)}:</span>
        <ResultItemFilter
          keyInSearch={keyInSearch}
          result={getListDataById()}
          isAction={isAction}
          searchFilter={searchFilter}
          setSearchFilter={setSearchFilter}
          isShowMore={isShowMore}
        />
      </div>
    </div>
  );
}

export default ResultFilter;

function ResultItemFilter<T extends object>({ ...props }: TResultItemFilter<T>) {
  const { keyInSearch, isShowMore, result, isAction, searchFilter, setSearchFilter } = props;

  const handleRemove = (valueToRemove: string) => {
    if (isAction && searchFilter && setSearchFilter) {
      setSearchFilter({
        ...searchFilter,
        [keyInSearch]: (searchFilter[keyInSearch as keyof T] as string)
          .split(',')
          .filter((i) => i !== valueToRemove)
          .join(','),
      });
    }
  };
  return (
    <div className="flex gap-1">
      {result.slice(0, limitIem).map((item, index: number) => {
        return (
          <div
            key={item + index.toString()}
            className="w-fit h-[22px] px-1 bg-[#31333d14] flex items-center justify-center rounded-lg gap-0.5"
          >
            {item.color && <TagFillIcon color={item.color} />}
            <span className="text-primary text-xs font-normal">
              {upperFirstChar(item.name, false)}
            </span>
            {isAction && searchFilter && setSearchFilter && (
              <button
                onClick={() => {
                  setSearchFilter({
                    ...searchFilter,
                    [keyInSearch]: (searchFilter[keyInSearch as keyof T] as string)
                      .split(',')
                      .filter((i) => i !== item.id.toString())
                      .join(','),
                  });
                }}
              >
                <RiCloseCircleFill className="ml-1" size={14} color="#0D112666" />
              </button>
            )}
          </div>
        );
      })}
      {result.length > limitIem && isAction && (
        <ShowMore
          onRemove={handleRemove}
          isShowMore={isShowMore}
          selected={result.slice(limitIem).map((item) => ({
            label: item.name,
            value: item.id.toString(),
            color: item.color,
          }))}
        />
      )}
    </div>
  );
}
