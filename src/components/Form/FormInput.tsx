import React, { ComponentPropsWithoutRef } from 'react';
import {
  Controller,
  ControllerProps,
  ControllerRenderProps,
  FieldPath,
  FieldValues,
} from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { FormItem } from '@/components/ui/form';
import { ErrorField } from '@/components/Form/ErrorField';
import { cn } from '@/lib/utils';

type TFormInputPropsExtend = {
  className?: string;
  label?: React.ReactNode;
  onChange?: (e: string) => void;
  isRemoveLabel?: boolean;
  classContainer?: string;
};

type TFormInputProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> = Pick<ControllerProps<TFieldValues, TName>, 'name' | 'control' | 'rules'> &
  ComponentPropsWithoutRef<'input'> &
  TFormInputPropsExtend;

export function FormInput<
  T extends FieldValues = FieldValues,
  U extends FieldPath<T> = FieldPath<T>,
>({ ...props }: TFormInputProps<T, U>) {
  const { name, control, rules, onChange, className, label, isRemoveLabel, classContainer, ...rest } = props;
  const handleChange = (
    field: ControllerRenderProps<T, U> | ControllerRenderProps<FieldValues, string>,
    e: string,
  ) => {
    field.onChange(e);
    if (onChange) {
      onChange(e);
    }
  };
  return (
    <FormItem className={cn('mb-6',classContainer)}>
      {isRemoveLabel ? (
        <></>
      ) : (
        <div className={cn('h-[18px]', label ? 'font-normal text-xs text-secondary' : '')}>
          {label}
        </div>
      )}
      <Controller
        name={name}
        rules={rules}
        control={control}
        render={({ field, fieldState }) => {
          const isError = !!fieldState?.error?.message;
          return (
            <div className="relative">
              <Input
                {...rest}
                {...field}
                className={`mt-1 rounded-xl bg-background-foreground h-[40px] text-sm font-normal text-primary placeholder:text-tertiary ${className ?? ''} ${
                  isError ? 'border-delete outline-none' : ''
                }`}
                onChange={(e) => {
                  handleChange(field, (e as React.ChangeEvent<HTMLInputElement>).target.value);
                }}
              />
              {fieldState.error && fieldState.error.message && (
                <ErrorField message={fieldState.error.message} />
              )}
            </div>
          );
        }}
      />
    </FormItem>
  );
}
