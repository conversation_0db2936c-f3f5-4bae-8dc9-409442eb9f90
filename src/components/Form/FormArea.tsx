import React, { ComponentPropsWithoutRef } from 'react';
import {
  Controller,
  ControllerProps,
  ControllerRenderProps,
  FieldPath,
  FieldValues,
} from 'react-hook-form';
import { FormItem } from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { ErrorField } from '@/components/Form/ErrorField';
import { cn } from '@/lib/utils';

type TFormAreaPropsExtend = {
  className?: string;
  label?: React.ReactNode;
  onChange?: (e: string) => void;
  isRemoveLabel?: boolean;
  classContainer?: string;
};

type TFormAreaProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> = Pick<ControllerProps<TFieldValues, TName>, 'name' | 'control' | 'rules'> &
  ComponentPropsWithoutRef<'input'> &
  TFormAreaPropsExtend;

export function FormArea<
  T extends FieldValues = FieldValues,
  U extends FieldPath<T> = FieldPath<T>,
>({ ...props }: TFormAreaProps<T, U>) {
  const { name, control, rules, onChange, className, label, isRemoveLabel, classContainer } = props;
  const handleChange = (
    field: ControllerRenderProps<T, U> | ControllerRenderProps<FieldValues, string>,
    e: string,
  ) => {
    field.onChange(e);
    if (onChange) {
      onChange(e);
    }
  };
  return (
    <FormItem className={cn('mb-6',classContainer)}>
      {isRemoveLabel ? (
        <></>
      ) : (
        <div className={cn('h-[18px]', label ? 'font-normal text-xs text-secondary' : '')}>
          {label}
        </div>
      )}
      <Controller
        name={name}
        rules={rules}
        control={control}
        render={({ field, fieldState }) => {
          return (
            <div className="relative">
              <Textarea
                {...field}
                disabled={props.disabled}
                maxLength={props.maxLength}
                minLength={props.minLength}
                placeholder={props.placeholder}
                className={`mt-1 mb-2 rounded-xl bg-background-foreground text-sm font-normal text-primary placeholder:text-tertiary outline-none ${className ?? ''}`}
                onChange={(e) => {
                  handleChange(field, (e as React.ChangeEvent<HTMLTextAreaElement>).target.value);
                }}
              />
              {fieldState.error && fieldState.error.message && (
                <ErrorField message={fieldState.error.message} />
              )}
            </div>
          );
        }}
      />
    </FormItem>
  );
}
