import React, { ComponentPropsWithoutRef } from 'react';
import { FormItem } from '@/components/ui/form';
import { IOptions } from '@/types/contactList';
import {
  Controller,
  ControllerProps,
  ControllerRenderProps,
  FieldPath,
  FieldValues,
} from 'react-hook-form';
import { ErrorField } from '@/components/Form/ErrorField';
import { cn } from '@/lib/utils';
import { SelectSearch } from '@/components/SelectSearch';

type TFormSelectPropsExtend = {
  className?: string;
  label?: React.ReactNode;
  onChange?: (e: IOptions[]) => void;
  placeholder?: string;
  placeholderInput?: string;
  listSelect: IOptions[];
  isRemoveLabel?: boolean;
  defaultValues?: IOptions[];
};

type TFormSelectSearchProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> = Pick<ControllerProps<TFieldValues, TName>, 'name' | 'control' | 'rules'> &
  ComponentPropsWithoutRef<'select'> &
  TFormSelectPropsExtend;

export function FormSelectSearch<
  T extends FieldValues = FieldValues,
  U extends FieldPath<T> = FieldPath<T>,
>({ ...props }: TFormSelectSearchProps<T, U>) {
  const {
    name,
    control,
    rules,
    disabled,
    onChange,
    className,
    label,
    listSelect,
    placeholder,
    placeholderInput,
    isRemoveLabel,
    defaultValues,
  } = props;
  const handleChange = (
    field: ControllerRenderProps<T, U> | ControllerRenderProps<FieldValues, string>,
    e: IOptions[],
  ) => {
    field.onChange(e);
    if (onChange) {
      onChange(e);
    }
  };
  return (
    <FormItem className="mb-6">
      {isRemoveLabel ? (
        <></>
      ) : (
        <div className={cn('h-[18px]', label ? 'font-normal text-xs text-secondary' : '')}>
          {label}
        </div>
      )}
      <Controller
        name={name}
        rules={rules}
        control={control}
        render={({ field, fieldState }) => {
          const isError = !!fieldState?.error?.message;

          return (
            <div className="relative">
              <SelectSearch
                options={listSelect}
                defaultValues={defaultValues}
                onSelect={(value) => {
                  handleChange(field, value);
                }}
                classNameTrigger={cn(isError ? 'border-delete outline-none' : '')}
                className={cn('w-full bg-transparent mt-1', className)}
                placeholderInput={placeholderInput}
                placeholder={placeholder}
                disabled={disabled}
              />
              {fieldState.error && fieldState.error.message && (
                <ErrorField message={fieldState.error.message} />
              )}
            </div>
          );
        }}
      />
    </FormItem>
  );
}
