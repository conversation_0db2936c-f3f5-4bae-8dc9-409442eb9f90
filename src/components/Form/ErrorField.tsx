import React from 'react';
import { RiInformation2Line } from '@remixicon/react';

type TErrorField = {
  message: string;
};
export const ErrorField: React.FC<TErrorField> = ({ ...props }: TErrorField) => {
  const { message } = props;
  return (
    <div className="flex gap-1 absolute">
      <RiInformation2Line size={17} color={'#F53E3E'} />
      <p className="text-delete text-xs font-medium">{message}</p>
    </div>
  );
};
