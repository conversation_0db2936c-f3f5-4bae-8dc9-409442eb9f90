import React, { ComponentPropsWithoutRef } from 'react';
import { FormItem } from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { RiArrowDownSLine } from '@remixicon/react';
import { IOptions } from '@/types/contactList';
import {
  Controller,
  ControllerProps,
  ControllerRenderProps,
  FieldPath,
  FieldValues,
} from 'react-hook-form';
import { ErrorField } from '@/components/Form/ErrorField';
import { cn } from '@/lib/utils';

type TFormSelectPropsExtend = {
  className?: string;
  label?: React.ReactNode;
  onChange?: (e: string) => void;
  placeholder?: string;
  listSelect: IOptions[];
  isRemoveLabel?: boolean;
};

type TFormSelectProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> = Pick<ControllerProps<TFieldValues, TName>, 'name' | 'control' | 'rules'> &
  ComponentPropsWithoutRef<'select'> &
  TFormSelectPropsExtend;

export function FormSelect<
  T extends FieldValues = FieldValues,
  U extends FieldPath<T> = FieldPath<T>,
>({ ...props }: TFormSelectProps<T, U>) {
  const {
    name,
    control,
    rules,
    disabled,
    onChange,
    className,
    label,
    listSelect,
    placeholder,
    isRemoveLabel,
  } = props;
  const handleChange = (
    field: ControllerRenderProps<T, U> | ControllerRenderProps<FieldValues, string>,
    e: string,
  ) => {
    field.onChange(e);
    if (onChange) {
      onChange(e);
    }
  };
  return (
    <FormItem className="mb-6">
      {isRemoveLabel ? (
        <></>
      ) : (
        <div className={cn('h-[18px]', label ? 'font-normal text-xs text-secondary' : '')}>
          {label}
        </div>
      )}
      <Controller
        name={name}
        rules={rules}
        control={control}
        render={({ field, fieldState }) => {
          const isError = !!fieldState?.error?.message;

          return (
            <div className="relative">
              <Select
                {...field}
                disabled={disabled}
                onValueChange={(value) => {
                  handleChange(field, value);
                }}
              >
                <SelectTrigger
                  className={`mt-1 rounded-xl bg-background-foreground h-[40px] text-sm font-normal text-tertiary ${
                    !!field.value ? 'text-primary' : 'text-tertiary'
                  } ${className ?? ''}  ${isError ? 'border-delete outline-none' : ''}`}
                  rightIcon={<RiArrowDownSLine size={16} className="text-tertiary" />}
                >
                  <SelectValue
                    placeholder={placeholder}
                    className="font-normal text-xs text-primary"
                  />
                </SelectTrigger>
                <SelectContent className={`border-tertiary rounded-xl ${className ?? ''}`}>
                  {listSelect.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {fieldState.error && fieldState.error.message && (
                <ErrorField message={fieldState.error.message} />
              )}
            </div>
          );
        }}
      />
    </FormItem>
  );
}
