import Modal from '@/components/Modal';
import { Button } from '@/components/ui/button';
import { ErrorIcon } from '@/assets/ErrorIcon';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

interface ILogoutSocialPopupProps {
  onLogout: () => void;
  title: string;
  description: string;
  content: string;
  icon: React.ReactNode;
}

const LogoutSocialPopup: React.FC<ILogoutSocialPopupProps> = (props: ILogoutSocialPopupProps) => {
  const { onLogout, title, description, content, icon } = props;
  const [open, setOpen] = useState(false);
  const { t } = useTranslation();
  return (
    <Modal
      openModal={open}
      isCloseIcon={false}
      onOpenChange={setOpen}
      trigger={
        <Button
          size={'lg'}
          className="p-2 h-7 rounded-sm bg-transparent hover:bg-[#f3f4f6] w-full justify-start"
          variant={'secondary'}
        >
          {icon}
          {content}
        </Button>
      }
      className="max-w-[529px]"
    >
      <div className="flex items-center flex-col">
        <ErrorIcon />
        <span>{title}</span>
        <div className="flex flex-col mt-2 items-center text-sm text-secondary">
          <span>{description}</span>
        </div>
        <div className="grid grid-cols-2 gap-3 mt-6 w-full">
          <Button
            onClick={() => setOpen(false)}
            className="px-3 py-1 rounded-xl w-full"
            variant={'secondary'}
            size={'lg'}
          >
            {t('common.button.cancel')}
          </Button>
          <Button
            onClick={() => {
              onLogout();
              setOpen(false);
            }}
            variant={'destructive'}
            className="px-3 py-1 rounded-xl w-full"
            size={'lg'}
          >
            {t('common.button.logOut')}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default LogoutSocialPopup;
