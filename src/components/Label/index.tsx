import { cn } from '@/lib/utils';

const LabelCustom = ({
  label,
  className,
  isRequire,
}: {
  label: string;
  className?: string;
  isRequire?: boolean;
}) => (
  <span
    className={cn(
      'text-sm font-semibold text-secondary-foreground flex items-center gap-1',
      className,
    )}
  >
    {label}
    {isRequire && <span className="text-destructive">*</span>}
  </span>
);

export default LabelCustom;
