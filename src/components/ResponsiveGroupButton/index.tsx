import React, {
  Children,
  isValidElement,
  ReactNode,
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
} from 'react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';

type TResponsiveButtonContainerProps = {
  children: ReactNode;
  className?: string;
  showMoreButtonClassName?: string;
  popoverContentClassName?: string;

  // need to pass button size to calculate the width of the button
  buttonShowMoreSize: number;
  buttonGap: number;
};

const ResponsiveButtonContainer: React.FC<TResponsiveButtonContainerProps> = ({
  ...props
}: TResponsiveButtonContainerProps) => {
  const {
    children,
    className = '',
    showMoreButtonClassName = '',
    popoverContentClassName = '',
    buttonShowMoreSize,
    buttonGap,
  } = props;
  const containerRef = useRef<HTMLDivElement>(null);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const childrenRefs = useRef<(HTMLDivElement | null)[]>([]);
  const showMoreButtonRef = useRef<HTMLDivElement>(null);
  const [visibleCount, setVisibleCount] = useState<number>(0);

  const { t } = useTranslation();

  const childrenArray = Children.toArray(children).filter(isValidElement);

  const updateVisibleButtons = () => {
    if (!containerRef.current || !wrapperRef.current) {
      return;
    }

    const containerWidth = containerRef.current.offsetWidth;

    // button width is fixed
    // need to pass button size to calculate the width of the button
    const showMoreButtonWidth = buttonShowMoreSize;

    // default gap between buttons

    let totalWidth = 0;
    let newVisibleCount = 0;
    let remainingButtons = childrenArray.length;

    for (let i = 0; i < childrenRefs.current.length; i++) {
      const button = childrenRefs.current[i];
      if (!button) {
        continue;
      }

      const buttonWidth = button.offsetWidth;
      const nextTotalWidth = totalWidth + buttonWidth + (i > 0 ? buttonGap : 0);

      remainingButtons = childrenArray.length - i - 1;
      const needShowMore = remainingButtons > 0;
      const availableWidth = containerWidth - (needShowMore ? showMoreButtonWidth + buttonGap : 0);

      if (nextTotalWidth <= availableWidth) {
        totalWidth = nextTotalWidth;
        newVisibleCount = i + 1;
      } else {
        break;
      }
    }

    setVisibleCount(newVisibleCount);
  };

  useEffect(() => {
    const resizeObserver = new ResizeObserver(() => {
      updateVisibleButtons();
    });

    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
    //   eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useLayoutEffect(() => {
    requestAnimationFrame(updateVisibleButtons);
    //   eslint-disable-next-line react-hooks/exhaustive-deps
  }, [children]);

  const hiddenChildren = childrenArray.slice(visibleCount);

  return (
    <div ref={containerRef} className={cn('w-full', className)}>
      <div ref={wrapperRef} className="flex items-center gap-3 relative">
        {childrenArray.map((child, index) => {
          const classNameExtend =
            index < visibleCount
              ? 'visible opacity-100 translate-x-0'
              : 'invisible opacity-0 translate-x-2 absolute';
          return (
            <div
              key={'child-' + index}
              ref={(el) => (childrenRefs.current[index] = el)}
              className={cn(
                'flex-shrink-0 transition-all duration-150 ease-in-out',
                classNameExtend,
              )}
            >
              {child}
            </div>
          );
        })}

        {hiddenChildren.length > 0 && (
          <div
            className={`transition-all duration-150 ease-in-out
              ${
                hiddenChildren.length > 0
                  ? 'visible opacity-100 translate-x-0'
                  : 'invisible opacity-0 -translate-x-2'
              }
            `}
          >
            <Popover>
              <PopoverTrigger
                className={cn(
                  'flex-shrink-0 transition-colors duration-150',
                  showMoreButtonClassName,
                )}
              >
                <div
                  ref={showMoreButtonRef}
                  className={cn(
                    'inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 py-2 h-[40px] bg-transparent text-primary font-base font-medium border-[1px] border-border rounded-xl hover:bg-white hover:text-filter px-4 w-[110px]',
                    // fixed width of button
                    `w-[${buttonShowMoreSize}px]`,
                  )}
                >
                  {t('common.button.showMore')}
                </div>
              </PopoverTrigger>
              <PopoverContent
                className={cn('w-auto min-w-40 p-0', popoverContentClassName)}
                align={'end'}
                sideOffset={5}
                isShowArrow={true}
              >
                <div className="flex flex-col gap-2 p-2">
                  {hiddenChildren.map((child, index) => (
                    <div
                      key={`hidden-${index}`}
                      className="flex-shrink-0 transition-all duration-150"
                    >
                      {child}
                    </div>
                  ))}
                </div>
              </PopoverContent>
            </Popover>
          </div>
        )}
      </div>
    </div>
  );
};

export default ResponsiveButtonContainer;
