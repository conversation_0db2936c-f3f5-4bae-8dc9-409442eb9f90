import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import React from 'react';
import { cn } from '@/lib/utils';
import { SOLIDS_COLOR } from '@/utils/colorPicker';
import { useTranslation } from 'react-i18next';

type TColorPickerProps = {
  background: string;
  className?: string;
  setBackground: (background: string) => void;
  title?: string;
};

export const ColorPicker: React.FC<TColorPickerProps> = ({ ...props }: TColorPickerProps) => {
  const { background, className, setBackground, title } = props;
  const { t } = useTranslation();

  return (
    <Popover>
      <PopoverTrigger asChild className="h-fit">
        <div>
          <div className="font-normal text-xs text-secondary flex gap-1 h-[18px] items-end mb-[4px]">
            {title ?? t('common.tagColor')}
          </div>
          <Button
            variant={'outline'}
            className={cn(
              'w-[77px] h-[40px] p-3 justify-start text-left font-normal hover:bg-transparent',
              !background && 'text-muted-foreground',
              className,
            )}
            type={'button'}
          >
            <div
              className="w-[20px] h-[20px] flex items-center gap-2 rounded-full"
              style={{ background }}
            />
          </Button>
        </div>
      </PopoverTrigger>
      <PopoverContent className="w-full p-2">
        <div className="grid grid-cols-10 gap-1">
          {SOLIDS_COLOR.map((s) => (
            <div
              key={s}
              style={{ background: s }}
              className={cn(
                'rounded-full h-6 w-6 cursor-pointer active:scale-105',
                s === '#FFFFFF' && 'border border-gray-300',
              )}
              onClick={() => setBackground(s)}
            />
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
};
