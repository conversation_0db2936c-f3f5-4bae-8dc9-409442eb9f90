import { genderLabel } from '@/utils/constants';
import React from 'react';

type TRenderGender = {
  gender: string;
};

export const RenderGender: React.FC<TRenderGender> = ({ ...props }: TRenderGender) => {
  const { gender } = props;
  const handleRenderGender = (gender: string) => {
    switch (gender) {
      case 'male':
        return (
          <div className="bg-standard p-1 rounded-lg w-fit">
            <span className="text-standard-text font-normal text-xs">{genderLabel.male}</span>
          </div>
        );
      case 'female':
        return (
          <div className="bg-error p-1 rounded-lg w-fit">
            <span className="text-error-text font-normal text-xs">{genderLabel.female}</span>
          </div>
        );
      case 'other':
        return (
          <div className="bg-secondary-foreground_crm p-1 rounded-lg w-fit">
            <span className="text-secondary font-normal text-xs">{genderLabel.other}</span>
          </div>
        );
    }
  };
  return handleRenderGender(gender);
};
