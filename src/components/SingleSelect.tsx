import React, { useState, useEffect, ReactNode } from 'react';
import { RiArrowDownSLine } from '@remixicon/react';
import { cn } from '@/lib/utils';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { Option } from '@/types/contactList';

interface SingleSelectProps {
  options: Option[];
  onChange: (selected: string) => void;
  placeholder?: string | ReactNode;
  defaultValue?: string;
  className?: string;
  isError?: boolean;
  triggerClassName?: string;
}

const SingleSelect: React.FC<SingleSelectProps> = ({
  options,
  onChange,
  placeholder = 'Select option',
  defaultValue,
  className,
  isError,
  triggerClassName,
}) => {
  const [selected, setSelected] = useState<Option | null>(null);
  const [open, setOpen] = useState<boolean>(false);

  useEffect(() => {
    const defaultOption = options.find((option) => option.value === defaultValue);
    setSelected(defaultOption || null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultValue]);

  const handleOptionSelect = (option: Option) => {
    setSelected(option);
    onChange(option.value);
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <div
          onClick={() => setOpen(!open)}
          className={cn(
            'h-10 cursor-pointer p-2 group flex-grow-0 flex items-center justify-center text-sm border rounded-xl relative',
            isError && 'border-red-400 [&_span]:text-red-400 [&_svg]:text-red-400',
            triggerClassName,
          )}
        >
          {/* Floating label */}
          <span
            className={cn(
              'absolute bg-card left-2 duration-150 -translate-y-1/2',
              open ? '-top-1 text-xs text-secondary' : 'top-1/2',
              selected && '-top-1 text-xs text-secondary',
            )}
          >
            {placeholder}
          </span>

          {/* Selected value */}
          <div className="text-left line-clamp-1 max-w-[500px] overflow-hidden bg-secondary text-xs py-0.5 px-1 rounded-md w-fit">
            {selected?.label}
          </div>

          {/* Dropdown arrow */}
          <RiArrowDownSLine
            size={16}
            className={cn(
              'ml-auto flex-shrink-0 transition-transform duration-200',
              open && 'rotate-180',
            )}
          />
        </div>
      </PopoverTrigger>

      <PopoverContent
        align="start"
        className={cn(
          'w-[215px] max-h-[368px] overflow-x-hidden rounded-xl shadow-md p-0',
          className,
        )}
      >
        {options.length === 0 ? (
          <div className="flex justify-center items-center h-32 text-secondary">No options</div>
        ) : (
          <div className="flex flex-col py-3 px-2 gap-y-2">
            {options.map((option) => (
              <button
                key={option.value}
                className={cn(
                  'w-full p-2 rounded-xl text-start transition-colors duration-200',
                  selected?.value === option.value
                    ? 'bg-brand-foreground text-brand'
                    : 'hover:bg-primary-crm-foreground hover:text-primary',
                )}
                onClick={() => handleOptionSelect(option)}
              >
                <span className="line-clamp-1 max-w-[190px]">{option.label}</span>
              </button>
            ))}
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
};

export default SingleSelect;
