import { RiSearchLine } from '@remixicon/react';
import SearchBar from '@/components/SearchBar';
import React from 'react';
import { useTranslation } from 'react-i18next';

type TFilterBar = {
  isRevert: boolean;
  searchQuery: string;
  setSearchQuery: (value: string) => void;
  children?: React.ReactNode;
  resultFilter?: React.ReactNode;
};

export const FilterBar: React.FC<TFilterBar> = ({ ...props }: TFilterBar) => {
  const { isRevert, children, resultFilter, searchQuery, setSearchQuery } = props;
  const { t } = useTranslation();
  return (
    <div className={`flex ${isRevert ? 'flex-col-reverse' : 'flex-col'}`}>
      <SearchBar
        value={searchQuery}
        setSearchQuery={setSearchQuery}
        className="w-full h-[40px]"
        placeholder={t('common.searchPlaceHolder')}
        icon={<RiSearchLine size={16} color={'#6B7183'} />}
      />
      <>
        {children}
        {resultFilter}
      </>
    </div>
  );
};
