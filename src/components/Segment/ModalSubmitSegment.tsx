import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import { convertParamsToSelectFilter } from '@/utils/helper';
import SelectItem from '@/components/SelectItem';
import { SubmitError<PERSON>and<PERSON>, SubmitHandler, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { FormInput } from '@/components/Form/FormInput';
import { FormArea } from '@/components/Form/FormArea';
import { MAX_LENGTH_INPUT, TSegment, TSegmentSubmit } from '@/types/segment';
import { ColorPicker } from '@/components/ColorPicker';
import { Button } from '@/components/ui/button';
import { DialogClose } from '@/components/ui/dialog';
import { monthSelectOptions, statusSelectOptions } from '@/utils/constants';
import { z } from 'zod';
import { IOptions } from '@/types/contactList';
import { getSelector, useAppDispatch, useAppSelector } from '@/hooks/reduxHooks';
import { createSegment, updateSegment } from '@/store/segment/action';
import { toast } from '@/hooks/use-toast';

type TModalSubmitSegment = {
  segmentDetail?: TSegment;
  isShowResult?: boolean;
  countContact?: number;
};
const n = (key: keyof TSegmentSubmit) => key;
export const ModalSubmitSegment: React.FC<TModalSubmitSegment> = ({
  ...props
}: TModalSubmitSegment) => {
  const { segmentDetail, isShowResult = true, countContact = 0 } = props;
  const [searchParams] = useSearchParams();
  const allQueryParams = Object.fromEntries(searchParams.entries());
  const refClickCloseModal = useRef<HTMLButtonElement>(null);
  const { allSegments } = useAppSelector(getSelector('segment'));
  const querySelect = convertParamsToSelectFilter(allQueryParams);
  const [background, setBackground] = useState('#004FD0');
  const [disable, setDisable] = useState<boolean>(true);
  const dispatch = useAppDispatch();

  const { t } = useTranslation();
  const contactCount = segmentDetail?.contact_quantity ?? countContact;
  const {
    control,
    handleSubmit,
    watch,
    reset,
    setError,
    formState: { isDirty },
  } = useForm<TSegmentSubmit>({
    resolver: zodResolver(
      z.object({
        name: z
          .string()
          .min(2, {
            message: t('common.validate.segmentMinLength', { value: 2 }),
          })
          .refine((val) => val.trim().length > 2, {
            message: t('common.validate.segmentMinLength', { value: 2 }),
          }),
        content: z.string().optional(),
        script_content: z.string().optional(),
      }),
    ),
    defaultValues: {
      name: '',
      content: '',
      script_content: '',
    },
  });

  const handleSetForm = () => {
    if (!segmentDetail) {
      return;
    }
    const updateValues: TSegmentSubmit = Object.keys(segmentDetail).reduce((acc, key) => {
      const k = key as keyof TSegment;
      if (k !== 'id') {
        (acc[k] as unknown) = (segmentDetail[k] ?? '') as unknown;
      }
      return acc;
    }, {} as TSegment);
    if (segmentDetail.color) {
      setBackground(segmentDetail.color);
    }
    reset(updateValues);
  };

  useEffect(() => {
    if (background !== segmentDetail?.color) {
      setDisable(false);
    } else {
      setDisable(true);
    }
    //   eslint-disable-next-line react-hooks/exhaustive-deps
  }, [background]);

  const onSubmit: SubmitHandler<TSegmentSubmit> = (values: TSegmentSubmit) => {
    const valueSegment: TSegmentSubmit = {
      ...values,
      contact_quantity: contactCount,
      color: background,
    } as TSegmentSubmit;
    if (!!segmentDetail) {
      dispatch(
        updateSegment({ ...valueSegment, id: segmentDetail.id, filter: segmentDetail.filter }),
      );
      toast({ title: t('contactList.updateSegmentSuccess'), status: 'success' });
    } else {
      const paramsCreate = Object.fromEntries(
        Array.from(searchParams.entries()).filter(([key]) => key !== 'search'),
      );
      dispatch(createSegment({ ...valueSegment, filter: { ...paramsCreate } }));
      toast({ title: t('contactList.createSegmentSuccess'), status: 'success' });
    }
    handleCloseModal();
  };

  const onError: SubmitErrorHandler<TSegmentSubmit> = (error) => {
    console.log('🚀 ~ create ~ error:', error);
    const nameValue = (watch(n('name')) as string).trim();
    if (nameValue.length === 0) {
      setError(n('name'), { type: 'custom', message: t('common.error.enterYourSegmentName') });
    } else if (nameValue.length < 2) {
      setError(n('name'), {
        type: 'custom',
        message: t('common.validate.segmentMinLength', { value: 2 }),
      });
    }
  };

  const handleCloseModal = () => {
    refClickCloseModal.current?.click();
  };

  const handleCheckLabel = (key: string, value: string): string => {
    switch (key) {
      case 'birthMonth':
        return (
          monthSelectOptions.find((item: IOptions): boolean => item.value === value)?.label ?? ''
        );
      case 'status':
        return (
          statusSelectOptions.find((item: IOptions): boolean => item.value === value)?.label ?? ''
        );
      case 'segment':
        return (
          allSegments.items.find((segment: TSegment) => segment.id.toString() === value)?.name ??
          value
        );
      default:
        return value;
    }
  };

  useEffect(() => {
    if (!!segmentDetail) {
      handleSetForm();
    }
    //   eslint-disable-next-line react-hooks/exhaustive-deps
  }, [segmentDetail]);

  const countDescription: number = ((watch(n('script_content')) as string).length as number) ?? 0;
  const querySearch = Object.keys(querySelect).filter((item) => item !== 'search');

  return (
    <div>
      <div className="flex flex-wrap gap-2 mb-6">
        {querySearch.length > 0 ? (
          querySearch.map((key) => {
            const data = (querySelect as never)[key];
            return (
              <div
                key={key}
                className="border border-border rounded-lg flex h-[36px] w-fit p-2 items-center gap-3"
              >
                <p>{t(`filter.${key}`)}:</p>
                <div className="flex gap-1">
                  {Object.keys(data).map((item) => {
                    return (
                      <SelectItem
                        key={key}
                        title={handleCheckLabel(key, data[item])}
                        value={handleCheckLabel(key, data[item])}
                      />
                    );
                  })}
                </div>
              </div>
            );
          })
        ) : (
          <></>
        )}
      </div>
      {isShowResult && (
        <p className="flex gap-1 text-sm font-normal mb-6">
          <span className="font-medium text-primary">{Number(contactCount).toLocaleString()}</span>
          <span className="text-secondary font-normal">
            {t('common.resultsFound', {
              symbol: Number(contactCount) > 1 ? 's' : '',
            })}
          </span>
        </p>
      )}

      <form onSubmit={handleSubmit(onSubmit, onError)}>
        <div className="flex gap-3">
          <div className="w-full">
            <FormInput
              control={control}
              label={
                <div className="flex gap-1 h-[18px] items-end">
                  {t('segment.enterSegmentName')}
                  <span className="text-delete">*</span>
                </div>
              }
              name={n('name')}
            />
          </div>
          <ColorPicker background={background} setBackground={setBackground} />
        </div>
        <div className="relative">
          <FormArea
            control={control}
            label={t('common.callScript')}
            name={n('script_content')}
            maxLength={200}
            max={200}
            className="resize-none"
          />
          <div className="absolute bottom-[12px] right-[12px]">
            <p className="text-filter text-sm font-normal">
              {countDescription}
              {MAX_LENGTH_INPUT['200']}
            </p>
          </div>
        </div>
        <div className="flex gap-4 mt-6">
          <DialogClose className="flex-1 w-full" ref={refClickCloseModal}>
            <div className="flex items-center justify-center h-[40px] font-medium w-full bg-secondary rounded-lg text-sm text-primary hover:bg-secondary-foreground_crm hover:text-filter">
              {t('common.button.cancel')}
            </div>
          </DialogClose>
          <Button
            type="submit"
            disabled={disable && !isDirty}
            variant={'primary'}
            className="flex-1 h-[40px] w-full"
          >
            {t('common.button.saveChange')}
          </Button>
        </div>
      </form>
    </div>
  );
};
