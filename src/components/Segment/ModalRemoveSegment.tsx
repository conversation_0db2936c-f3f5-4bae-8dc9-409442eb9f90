import React from 'react';
import { RiDeleteBin6Line, RiError<PERSON>arningLine, RiLoader2Line } from '@remixicon/react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import Modal from '@/components/Modal';
import { TSegment } from '@/types/segment';
import { DialogClose } from '@/components/ui/dialog';

type TModalDeleteSegment = {
  segmentDetail: TSegment;
  loadingSegment: boolean;
  deleteSegment: (id: string) => void;
};
export const ModalRemoveSegment: React.FC<TModalDeleteSegment> = ({
  ...props
}: TModalDeleteSegment) => {
  const { loadingSegment, segmentDetail, deleteSegment } = props;
  const { t } = useTranslation();

  return (
    <Modal
      isCloseIcon={false}
      className="w-fit max-w-none"
      titleAlign={'center'}
      trigger={
        <button className="hover:text-primary">
          <RiDeleteBin6Line size={20} />
        </button>
      }
      title={
        <>
          <div className="m-auto w-fit">
            <RiErrorWarningLine size={80} color={'#F53E3E'} />
          </div>
          <p className="text-xl text-primary font-semibold text-center">
            {t('segment.titleDeleteSegment')}
          </p>
        </>
      }
    >
      <div className="flex flex-col gap-4 w-[529px] justify-center items-center">
        <p className="text-sm text-center text-secondary">
          {t('segment.deleteSegmentDescription')}
        </p>
        <div className="flex gap-4 w-full">
          <DialogClose className="flex-1" disabled={loadingSegment}>
            <div className="flex items-center justify-center flex-1 h-[40px] font-medium w-full bg-secondary-foreground_crm text-base text-filter rounded-xl hover:bg-secondary-foreground_crm hover:text-tertiary-foreground">
              {t('common.button.cancel')}
            </div>
          </DialogClose>

          <Button
            disabled={loadingSegment}
            onClick={() => {
              deleteSegment(segmentDetail.id);
            }}
            variant={'delete'}
            className="flex-1 h-[40px] w-full"
          >
            {loadingSegment ? (
              <RiLoader2Line className="mx-auto animate-spin" />
            ) : (
              t('common.button.remove')
            )}
          </Button>
        </div>
      </div>
    </Modal>
  );
};
