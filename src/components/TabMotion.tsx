import { cn } from '@/lib/utils';
import { useCallback, useLayoutEffect, useRef, useState } from 'react';

interface Props {
  options: string[];
  value: string;
  onChange: (value: string) => void;
}
const TabMotion = ({ options, value, onChange }: Props) => {
  const activeIndex = options.indexOf(value);
  const buttonsRef = useRef<(HTMLButtonElement | null)[]>([]);
  const [sliderStyle, setSliderStyle] = useState({
    width: 0,
    left: 0,
  });

  const updateSliderPosition = useCallback(() => {
    const activeButton = buttonsRef.current[activeIndex];
    if (!activeButton) return;

    const containerLeft = activeButton.parentElement?.getBoundingClientRect().left ?? 0;
    const buttonRect = activeButton.getBoundingClientRect();
    const buttonLeft = buttonRect.left - containerLeft;

    setSliderStyle({
      width: buttonRect.width,
      left: buttonLeft,
    });
  }, [activeIndex]);

  useLayoutEffect(() => {
    updateSliderPosition();
    window.addEventListener('resize', updateSliderPosition);
    return () => window.removeEventListener('resize', updateSliderPosition);
  }, [updateSliderPosition]);

  return (
    <div className="relative bg-[#F0F0F0] flex p-2 gap-2 rounded-full w-fit">
      <div
        className="absolute h-8 top-2 transition-all duration-300 ease-in-out rounded-full bg-brand-foreground"
        style={{
          width: sliderStyle.width,
          left: sliderStyle.left,
        }}
      />
      {options.map((option, index) => {
        const isSelected = option === value;
        return (
          <button
            ref={(el) => (buttonsRef.current[index] = el)}
            key={option}
            onClick={() => onChange(option)}
            className={cn(
              'relative px-2 py-1 rounded-full text-sm h-8 whitespace-nowrap font-medium',
              'transition-colors duration-300 ease-in-out',
              !isSelected ? 'text-secondary' : 'text-[#5A18BF]',
            )}
          >
            <span className="relative z-10">{option}</span>
          </button>
        );
      })}
    </div>
  );
};
export default TabMotion;
