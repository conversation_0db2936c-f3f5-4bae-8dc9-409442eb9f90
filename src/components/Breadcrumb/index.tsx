import React from 'react';
import { CRM360Icon } from '@/assets/CRM360Icon';
import { Link, useLocation } from 'react-router-dom';
import { RiArrowRightSLine } from '@remixicon/react';
import { useTranslation } from 'react-i18next';
import { navLinkConfig, TNavLinkConfig, TNavLinkConfigItem } from '@/utils/helper';
import { ROOT_PATH, ROOT_ROUTE } from '@/constants/router';

type TBreadcrumb = {
  endUrl?: string;
  title?: string;
};

const contactRoute = `${ROOT_PATH}/${ROOT_ROUTE.contact['']}`;
const facebookRoute = `${ROOT_PATH}/${ROOT_ROUTE.facebook['']}`;
const tiktokRoute = `${ROOT_PATH}/${ROOT_ROUTE.tiktok['']}`;

const Breadcrumb: React.FC<TBreadcrumb> = ({ ...props }: TBreadcrumb) => {
  const { endUrl, title } = props;
  const { t } = useTranslation();

  const location = useLocation();
  const navLinks = navLinkConfig();

  const checkIncludeRoute = (path: string) => {
    return path.includes(location.pathname);
  };

  const crm360Route = [
    {
      id: 1,
      url: contactRoute,
      title: t('contactList.titlePageBreadcrumb'),
      isHideArrow: checkIncludeRoute(contactRoute),
    },
    {
      id: 2,
      url: facebookRoute,
      title: t('common.facebookTitlePageBreadcrumb'),
      isHideArrow: checkIncludeRoute(facebookRoute),
    },
    {
      id: 3,
      url: tiktokRoute,
      title: t('common.tiktokTitlePageBreadcrumb'),
      isHideArrow: checkIncludeRoute(tiktokRoute),
    },
  ];

  return (
    <div className="pb-4 flex items-center gap-1">
      <CRM360Icon />
      <div className="flex items-center gap-1">
        <span className="text-xs font-normal text-secondary">CRM 360</span>
        <span>
          <RiArrowRightSLine />
        </span>
      </div>
      {crm360Route.map((item) => {
        if (location.pathname.includes(item.url)) {
          return (
            <ItemBreadcrumb
              key={item.id}
              url={item.url}
              title={item.title}
              isHideArrow={item.isHideArrow}
            />
          );
        }
      })}

      {navLinks.map((navLink: TNavLinkConfig) =>
        navLink.item
          .filter((item: TNavLinkConfigItem) => {
            return location.pathname.includes(item.url);
          })
          .map((item: TNavLinkConfigItem) => {
            return (
              !item.isHideTitle && (
                <ItemBreadcrumb
                  key={item.id}
                  url={item.url}
                  title={item.title}
                  isHideArrow={!location.pathname.includes('detail')}
                />
              )
            );
          }),
      )}
      {(endUrl || title) && (
        <ItemBreadcrumb key={endUrl} url={endUrl ?? ''} title={title ?? ''} isHideArrow={true} />
      )}
    </div>
  );
};

export default Breadcrumb;

type TItemBreadcrumb = {
  url: string;
  title: string;
  isHideArrow: boolean;
};
const ItemBreadcrumb: React.FC<TItemBreadcrumb> = ({ ...props }: TItemBreadcrumb) => {
  const { url, title, isHideArrow } = props;
  return (
    <Link to={url} className="flex items-center gap-1">
      <span className="text-xs font-normal text-secondary">{title}</span>
      {!isHideArrow ? <RiArrowRightSLine /> : <></>}
    </Link>
  );
};
