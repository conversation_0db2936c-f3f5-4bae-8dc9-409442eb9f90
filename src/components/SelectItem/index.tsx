import React from 'react';
import { Badge } from '@/components/ui/badge';
import { RiCloseCircleFill } from '@remixicon/react';
import { cn } from '@/lib/utils';
import { TagFillIcon } from '@/assets/TagFillIcon';

const SelectItem = ({
  title,
  onClick,
  value,
  isRemoveClick,
  className,
  color,
}: {
  title: string | React.ReactNode;
  onClick?: (value: string, e: React.MouseEvent) => void;
  value: string;
  isRemoveClick?: boolean;
  className?: string;
  color?: string;
}) => {
  return (
    <Badge
      variant="select"
      className={cn(
        'w-fit h-[22px] max-w-auto px-1 bg-[#31333d14] text-primary flex items-center justify-center rounded-lg gap-0.5',
        className,
      )}
    >
      {color && <TagFillIcon color={color} />}
      <p className="ml-[5px] capitalize text-xs font-normal truncate">
        {title}
      </p>
      {onClick && !isRemoveClick && (
        <RiCloseCircleFill
          className="cursor-pointer rounded"
          size={14}
          color="#0D112666"
          onClick={(e) => onClick(value, e)}
        />
      )}
    </Badge>
  );
};
export default SelectItem;
