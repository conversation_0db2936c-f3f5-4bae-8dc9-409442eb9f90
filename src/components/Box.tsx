import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from '@/lib/utils';


const boxVariants = cva("flex gap-6 ", {
  variants: {
    variant: {
      default: "items-center justify-between ",
      ["row-start"]: "items-center justify-center",
      ["col-start"]: "flex-col items-start w-full",
      ["col-center"]: "flex-col justify-center",
    },
  },
  defaultVariants: {
    variant: "default",
  },
});

export interface BoxProps
  extends React.ButtonHTMLAttributes<HTMLDivElement>,
    VariantProps<typeof boxVariants> {
  asChild?: boolean;
  icon?: React.ReactElement;
  content?: string;
}

const Box = React.forwardRef<HTMLDivElement, BoxProps>(
  ({ className, variant, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "div";

    return <Comp className={cn(boxVariants({ variant, className }))} ref={ref} {...props} />;
  }
);
Box.displayName = "Box";

export { Box, boxVariants };
