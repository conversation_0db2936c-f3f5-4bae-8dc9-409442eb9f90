import React from 'react';
import { Button, ButtonProps } from './ui/button';
import { toast } from '@/hooks/use-toast';

type Props = {
  status: 'success' | 'error' | 'warning' | 'info';
  message: string;
  title?: string;
  duration?: number;
} & ButtonProps;

const ToastButton: React.FC<Props> = ({ status, message, title, duration, ...props }: Props) => {
  return (
    <Button onClick={() => toast({ title, description: message, status, duration })} {...props} />
  );
};

export default ToastButton;
