import { cn } from '@/lib/utils';

type Props = {
  title: string;
  isRequired?: boolean;
  className?: string;
};

const TitleSchema = ({ title, isRequired = true, className }: Props) => {
  return (
    <div className={cn('flex items-center gap-1', className)}>
      <span className="text-sm font-medium">{title}</span>
      {isRequired && <div className="text-red-500">*</div>}
    </div>
  );
};
export default TitleSchema;
