import { useFieldArray, Control } from 'react-hook-form';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '../ui/form';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { ZaloTemplate } from '@/constants/zalo/validate';
import { RiAddLine, RiSubtractLine } from '@remixicon/react';
import { cn } from '@/lib/utils';
import { t } from 'i18next';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { paramFieldsOptions } from '@/constants/zalo';
import ErrorLabel from '../ErrorLabel';

type Props = {
  control: Control<ZaloTemplate>;
};

const TableSchemaEditor = ({ control }: Props) => {
  const { fields, append, remove } = useFieldArray({
    name: 'table',
    control: control,
  });

  return (
    <div className="flex flex-col gap-3">
      {fields.map((field, index) => (
        <div key={field.id} className="grid grid-cols-[1fr,1fr,auto] gap-3 items-start">
          <FormField
            control={control}
            name={`table.${index}.title`}
            render={({ field, fieldState }) => (
              <FormItem className={cn('flex flex-col items-start', index === 0 && 'gap-1')}>
                {index === 0 && (
                  <FormLabel>
                    {t('common.zaloAds.titleKey')} <span className="text-red-500">*</span>
                  </FormLabel>
                )}
                <FormControl>
                  <Input
                    className={cn(
                      fieldState.error
                        ? 'border-red-500'
                        : 'focus:shadow-medium focus:border-primary-crm',
                    )}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name={`table.${index}.content`}
            render={({ field, fieldState }) => (
              <FormItem className={cn('flex flex-col items-start', index === 0 && 'gap-1')}>
                {index === 0 && (
                  <FormLabel>
                    {t('common.zaloAds.contentKey')} <span className="text-red-500">*</span>
                  </FormLabel>
                )}
                <Select value={field.value} onValueChange={field.onChange}>
                  <FormControl>
                    <SelectTrigger className={cn(fieldState.error && 'border-red-500')}>
                      <SelectValue placeholder={t('common.zaloAds.selectType')} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {paramFieldsOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button
            className={cn('text-red-500 hover:text-red-500 p-1', index === 0 && 'mt-5')}
            type="button"
            variant="ghost"
            size="icon"
            onClick={() => remove(index)}
          >
            <RiSubtractLine />
          </Button>
        </div>
      ))}
      <ErrorLabel content={control.getFieldState('table')?.error?.message} />
      <Button
        type="button"
        variant="outline"
        size="sm"
        className="w-fit bg-[#F0F0F0] rounded-md border-none text-sm font-medium px-2 py-1"
        onClick={() => append({ title: '', content: '', value: '' })}
      >
        <RiAddLine />
        {t('common.zaloAds.addRow')}
      </Button>
    </div>
  );
};
export default TableSchemaEditor;
