import { useEffect, useRef, useState } from 'react';
import { RiArrowDownSLine, RiCalendarLine, RiCloseLine } from '@remixicon/react';

import { Calendar } from './Calendar';

import useResponsive from '@/hooks/useResponsive';
import { Box } from '@/components/Box';
import { formatDateISOString, formatDateRange } from '@/utils/helper';
import { cn } from '@/lib/utils';
import { DateRange, DateRangePickerCampaignProps, TPreset } from '@/types/RangePicker';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import PresetDateCampaign from '@/components/DateRanger/PresetDateCampaign';

const DateRangerPickerCampaign = ({
  initialDateFrom,
  initialDateTo,
  align = 'end',
  className,
  disableRemove = false,
  onChange,
  onClear,
  placeholder = '',
  onSelectPreset,
}: DateRangePickerCampaignProps) => {
  const { isMobile } = useResponsive();
  const [isOpen, setIsOpen] = useState(false);
  const [presetDataFilter, setPresetDataFilter] = useState<TPreset>('');
  const [range, setRange] = useState<DateRange>({
    from: initialDateFrom || '',
    to: initialDateTo || '',
  });

  const openedRangeRef = useRef<DateRange | undefined>();

  useEffect(() => {
    if (isOpen) {
      openedRangeRef.current = range;
    }
    //   eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);

  useEffect(() => {
    if (range.from == '' && range.to == '') {
      onChange({ from: '', to: '' });
    } else if (range.from !== '' && range.to !== '') {
      onChange({
        from: formatDateISOString(range?.from),
        to: range?.to ? formatDateISOString(range?.to) : formatDateISOString(range?.from),
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [range]);

  useEffect(() => {
    if (initialDateFrom && initialDateTo) {
      setRange({
        from: initialDateFrom,
        to: initialDateTo,
      });
    }
  }, [initialDateFrom, initialDateTo]);

  const handleRemove = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    if (onClear) onClear();
    setRange({ from: '', to: '' });
  };

  const DateFrom = range.from ? formatDateRange(range?.from || '') : '';
  const DateTo = range.to ? formatDateRange(range.to) : '';

  return (
    <Popover modal={true} open={isOpen} onOpenChange={(open: boolean) => setIsOpen(open)}>
      <PopoverTrigger asChild className="p-0 m-0">
        <Box
          className={cn(
            'flex relative whitespace-nowrap p-2 rounded-xl max-w-[213px] w-full border items-center text-custom-tertiary font-normal',
            className,
          )}
        >
          {range.from !== '' && range.to !== '' && (
            <div className="relative z-10">
              <div className="text-sm flex items-center gap-1">
                {DateFrom + ' - ' + DateTo}
                {!disableRemove && (
                  <button onClick={(e) => handleRemove(e)}>
                    <RiCloseLine className="bg-[#F7F7F8] ml-auto" size={14} color="#5314a3" />
                  </button>
                )}
              </div>
            </div>
          )}
          <div
            className={cn(
              ' z-[1] absolute flex bg-white items-center gap-1 transform -translate-y-1/2 duration-300  ',
              isOpen || (DateFrom !== '' && DateTo !== '') ? '-top-1 text-xs' : 'top-1/2',
            )}
          >
            <RiCalendarLine size={16} />
            <span className="font-normal text-sm">{placeholder}</span>
          </div>
          {!(initialDateFrom && initialDateTo) && (
            <RiArrowDownSLine className="ml-auto" size={16} />
          )}
        </Box>
      </PopoverTrigger>
      <PopoverContent align={align} sideOffset={12} className="w-auto border-custom-primary">
        <div className="flex py-2">
          {!isMobile && <PresetDateCampaign preset={presetDataFilter} setPreset={(preset)=>{
            onSelectPreset(preset)
            setPresetDataFilter(preset);
            setRange({ from: '', to: '' });
          }} />}
          <Box className="items-start">
            <Calendar
              mode="range"
              onSelect={(value: { from?: Date | string; to?: Date | string } | undefined) => {
                if (value?.from != null) {
                  setRange({ from: value.from, to: value?.to || '' });
                  setPresetDataFilter('');
                  onSelectPreset('')
                }
              }}
              disabled={(date) => new Date(date) > new Date()}
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              selected={range as any}
              numberOfMonths={isMobile ? 1 : 2}
              defaultMonth={
                new Date(new Date().setMonth(new Date().getMonth() - (isMobile ? 0 : 1)))
              }
            />
          </Box>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default DateRangerPickerCampaign;
