import * as React from 'react';
import { DayPicker } from 'react-day-picker';

import { cn } from '@/lib/utils';
import { OptionsDatePicker } from '@/utils/dateRange';
import { RiArrowDropLeftLine, RiArrowDropRightLine } from '@remixicon/react';

type TCalendarProps = {
  className?: string;
  classNames?: object;
  showOutsideDays?: boolean;
  mode?: string;
};
export type CalendarProps = React.ComponentProps<typeof DayPicker> & TCalendarProps;

function Calendar({ className, classNames, showOutsideDays = true, ...props }: CalendarProps) {
  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn('p-3', className)}
      classNames={{ ...OptionsDatePicker, ...classNames }}
      components={{
        IconLeft: () => <RiArrowDropLeftLine className="h-4 w-4" />,
        IconRight: () => <RiArrowDropRightLine className="h-4 w-4" />,
      }}
      {...props}
    />
  );
}

Calendar.displayName = 'Calendar';

export { Calendar };
