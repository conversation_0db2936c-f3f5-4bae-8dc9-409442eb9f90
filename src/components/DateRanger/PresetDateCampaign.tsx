import { useEffect, useState } from 'react';

import { TPreset, PresetButtonCampaignProps, PRESETS_OPTIONS_CAMPAiGN } from '@/types/RangePicker';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

interface Props {
  preset: TPreset;
  setPreset: (range: TPreset) => void;
}

const PresetButton = ({
  preset,
  label,
  isSelected,
  setPreset,
}: PresetButtonCampaignProps): JSX.Element => (
  <Button
    className={cn(
      isSelected && 'pointer-events-none bg-[#ecdffb] text-[#5314a3]',
      'w-full items-center justify-start',
    )}
    variant="ghost"
    onClick={() => setPreset(preset)}
  >
    {label}
  </Button>
);

const PresetDateCampaign = ({ preset, setPreset }: Props) => {
  const [selectedPreset, setSelectedPreset] = useState<string | undefined>(preset);
  const setPresetValue = (preset: TPreset): void => {
    setPreset(preset);
    setSelectedPreset(preset);
  };
  useEffect(() => {
    setSelectedPreset(preset);
  }, [preset]);

  return (
    <div className="flex flex-col items-end gap-1">
      {PRESETS_OPTIONS_CAMPAiGN.map((preset) => (
        <PresetButton
          key={preset.name}
          preset={preset.name as TPreset}
          label={preset.label}
          isSelected={selectedPreset === preset.name}
          setPreset={setPresetValue}
        />
      ))}
    </div>
  );
};

export default PresetDateCampaign;
