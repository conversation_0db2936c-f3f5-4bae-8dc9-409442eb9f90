import React from 'react';
import { Table } from '@tanstack/react-table';
import { MultipleSelect } from '../MultipleSelect';
import SearchBar from '../SearchBar';
import { TABLE_LABEL } from '@/constants/table/label';

type Props<T> = {
  table: Table<T>;
  searchColumn?: string;
  searchPlaceholder?: string;
  isSelectCol?: boolean;
};
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const ToolbarTable: React.FC<any> = <T extends object>({
  table,
  searchColumn,
  searchPlaceholder,
  isSelectCol = false,
}: Props<T>) => {
  const data = table.getAllColumns();

  const { options, visibleColumns } = data.reduce<{
    options: { value: string; label: string }[];
    visibleColumns: string[];
  }>(
    (acc, column) => {
      if (column.getCanHide()) {
        acc.options.push({ value: column.id, label: column.id });
        if (column.getIsVisible()) {
          acc.visibleColumns.push(column.id);
        }
      }
      return acc;
    },
    { options: [], visibleColumns: [] },
  );

  const handleColumnVisibilityChange = (selected: string[]) => {
    const visibilityState = data.reduce(
      (acc, column) => {
        acc[column.id] = column.getCanHide() ? selected.includes(column.id) : true;
        return acc;
      },
      {} as Record<string, boolean>,
    );
    table.setColumnVisibility(visibilityState);
  };

  if (!searchColumn && !isSelectCol) return null;

  return (
    <div className="flex items-center py-4 justify-between">
      {searchColumn && (
        <SearchBar
          value={(table.getColumn(searchColumn)?.getFilterValue() as string) ?? ''}
          setSearchQuery={(value) => table.getColumn(searchColumn)?.setFilterValue(value)}
          placeholder={searchPlaceholder}
          className="max-w-sm w-full h-10"
        />
      )}
      {isSelectCol && (
        <MultipleSelect
          options={options}
          selected={visibleColumns}
          onChange={handleColumnVisibilityChange}
          placeholder={TABLE_LABEL.column_select}
        />
      )}
    </div>
  );
};

export default ToolbarTable;
