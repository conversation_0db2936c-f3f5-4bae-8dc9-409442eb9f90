import { cn } from '@/lib/utils';
import { RiArrowDownSLine } from '@remixicon/react';
import { Row } from '@tanstack/react-table';
import React from 'react';

type Props<T> = {
  row: Row<T>;
  id?: string;
  title?: string;
  classOpen?: string;
  classClose?: string;
  setIdExpand?: (id: string) => void;
};
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const ButtonExpand: React.FC<Props<any>> = <T extends object>({
  row,
  id,
  title = '',
  classOpen,
  classClose,
  setIdExpand,
}: Props<T>) => {
  return (
    <button
      onClick={() => {
        if (!row.getIsExpanded() && setIdExpand) {
          setIdExpand(id ?? '');
        }
        row.toggleExpanded();
      }}
    >
      <RiArrowDownSLine
        size={20}
        className={cn(
          row.getIsExpanded() ? (classOpen ?? 'rotate-180') : (classClose ?? 'rotate-0'),
        )}
      />
      {title}
    </button>
  );
};
export default ButtonExpand;
