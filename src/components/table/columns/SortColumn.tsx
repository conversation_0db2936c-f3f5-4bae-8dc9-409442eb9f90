import { RiArrowUpDownLine } from "@remixicon/react";
import React from "react";

type Props = {
  onClick: () => void;
  title: string;
  isSorted?: boolean;
};

const BaseColumn: React.FC<Props> = ({ onClick, title, isSorted = false }: Props) => {
  const handleSort = () => {
    if (!isSorted) return;
    onClick();
  };
  return (
    <button className="flex items-center gap-1 font-semibold capitalize" onClick={handleSort}>
      {title}
      {isSorted && <RiArrowUpDownLine size={16} />}
    </button>
  );
};
export default BaseColumn;
