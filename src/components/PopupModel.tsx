import React from 'react';
import { <PERSON><PERSON>, <PERSON>alogContent, Di<PERSON>Header, DialogTitle, DialogTrigger } from './ui/dialog';
import { cn } from '@/lib/utils';

type Props = {
  trigger: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  title?: string;
};

const PopupModel = ({ trigger, children, className = '', title = '' }: Props) => {
  return (
    <Dialog>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className={cn('max-w-[636px]', className)}>
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold leading-9 tracking-[0.6px]">
            {title}
          </DialogTitle>
        </DialogHeader>
        {children}
      </DialogContent>
    </Dialog>
  );
};
export default PopupModel;
