import { cn } from '@/lib/utils';
import { RiErrorWarningLine } from '@remixicon/react';
import { HTMLInputTypeAttribute } from 'react';

type Props = {
  value: string;
  onChange: (value: string) => void;
  label?: string;
  placeholder?: string;
  className?: string;
  isRequired?: boolean;
  error?: string;
  type?: HTMLInputTypeAttribute;
  isDisabled?: boolean;
  size?: number;
};

const InputLabel = ({
  value,
  onChange,
  label,
  placeholder,
  className,
  isRequired = true,
  error,
  type = 'text',
  isDisabled = false,
  size,
}: Props) => {
  return (
    <div className={cn('flex flex-col gap-1 items-start w-full', className)}>
      {label && (
        <label className="capitalize text-sm">
          {label}
          {isRequired && <span className="text-red-500">*</span>}
        </label>
      )}
      <input
        type={type}
        className={cn(
          'outline-none border h-10 border-gray-300 w-full rounded-xl p-3 px-2 py-1 text-tertiary-foreground',
          error ? 'border-red-500' : '',
        )}
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        disabled={isDisabled}
        size={size}
      />
      {error && (
        <div className="text-red-500 text-xs flex items-center gap-1">
          <RiErrorWarningLine size={16} />
          {error}
        </div>
      )}
    </div>
  );
};
export default InputLabel;
