import React, { useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';
import Modal from './Modal';
import { t } from 'i18next';
import { cn } from '@/lib/utils';

export type TParams = {
  isShowIcon?: boolean;
  title?: string;
  align?: 'start' | 'end' | 'center';
  className?: string;
};

export type TOptionsDrop = {
  value?: string;
  label: string | React.ReactNode;
  params?: TParams;
  modal?:
    | React.ReactNode
    | ((payload: unknown, setIsOpen: (open: boolean) => void) => React.ReactNode);
};

export type TViewMore = {
  title?: string;
  onClick: () => void;
  className?: string;
};

type Props = {
  trigger: React.ReactNode;
  options: TOptionsDrop[];
  onChange?: (value: string) => void;
  renderItem?: (option: TOptionsDrop) => React.ReactNode;
  className?: string;
  align?: 'start' | 'center' | 'end';
  viewMore?: TViewMore;
};

export const DropdownModal = ({
  className = '',
  trigger,
  options,
  onChange,
  renderItem,
  align = 'center',
  viewMore,
}: Props) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="outline-none">{trigger}</DropdownMenuTrigger>
      <DropdownMenuContent
        align={align}
        className={cn(
          'border shadow-md rounded-xl p-2 gap-2',
          Boolean(viewMore) && 'pb-0 mb-5',
          className,
        )}
      >
        <div className="max-h-[200px] w-[200px] overflow-x-hidden">
          {options.map((option, i) => {
            if (option.modal) {
              return (
                <ModalDrop
                  renderItem={renderItem}
                  key={i}
                  option={option}
                  trigger={trigger}
                  params={option.params}
                />
              );
            }
            return (
              <DropdownMenuItem
                clearClasses={Boolean(renderItem)}
                className={cn(
                  'outline-none cursor-pointer',
                  renderItem
                    ? 'w-full'
                    : 'p-2 text-start rounded-md hover:bg-secondary-foreground_crm text-secondary w-full',
                )}
                key={i}
                onClick={() => onChange && onChange(option.value || '')}
              >
                {renderItem ? renderItem(option) : option.label}
              </DropdownMenuItem>
            );
          })}
        </div>
        {viewMore && (
          <div className="sticky bg-card bottom-0 pb-2">
            <DropdownMenuItem
              className={cn(
                'p-2 text-sm font-medium outline-none cursor-pointer rounded-lg',
                viewMore.className,
              )}
              onClick={viewMore.onClick}
            >
              {viewMore.title || t('common.button.viewMore')}
            </DropdownMenuItem>
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
export default DropdownModal;

type ModalDropProps = {
  option: TOptionsDrop;
  params?: TParams;
} & Omit<Props, 'options'>;

const ModalDrop = ({ renderItem, option, params }: ModalDropProps) => {
  const [isOpen, setIsOpen] = useState(false);
  return (
    <Modal
      openModal={isOpen}
      onOpenChange={setIsOpen}
      isCloseIcon={params?.isShowIcon}
      title={params?.title}
      titleAlign={params?.align}
      className={params?.className}
      trigger={
        (renderItem && renderItem(option)) || (
          <button
            onClick={() => setIsOpen(true)}
            className="p-2 text-start rounded-md hover:bg-secondary-foreground_crm text-secondary w-full"
          >
            {option.label}
          </button>
        )
      }
    >
      {typeof option.modal === 'function'
        ? option.modal(option.value || '', setIsOpen)
        : option.modal}
    </Modal>
  );
};
