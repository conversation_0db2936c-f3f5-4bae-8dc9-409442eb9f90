import { statusLabel } from '@/utils/constants';
import React from 'react';

type TRenderStatus = {
  status: string;
};

export const RenderStatus: React.FC<TRenderStatus> = ({ ...props }: TRenderStatus) => {
  const { status } = props;
  const handleRenderStatus = (status: string) => {
    switch (status) {
      case 'calling':
        return (
          <div className="flex items-center justify-center min-w-[80px] w-[80px] h-[26px] text-center bg-success p-1 rounded-lg">
            <span className="text-success-text font-normal text-xs">{statusLabel.calling}</span>
          </div>
        );
      case 'confirmed':
        return (
          <div className="flex items-center justify-center min-w-[80px] w-[80px] h-[26px] text-center bg-success p-1 rounded-lg">
            <span className="text-success-text font-normal text-xs">{statusLabel.valid}</span>
          </div>
        );
      case 'spam':
        return (
          <div className="flex items-center justify-center min-w-[80px] w-[80px] h-[26px] text-center bg-error p-1 rounded-lg">
            <span className="text-error-text font-normal text-xs">{statusLabel.invalid}</span>
          </div>
        );
      case 'unconfirm':
        return (
          <div className="flex items-center justify-center min-w-[80px] w-[80px] h-[26px] text-center bg-secondary-foreground_crm p-1 rounded-lg">
            <span className="text-secondary font-normal text-xs">{statusLabel.unverify}</span>
          </div>
        );
    }
  };
  return handleRenderStatus(status);
};
