import React, { FC, useEffect, useRef, useState } from 'react';
import {
  RiPauseCircleLine,
  RiPlayCircleLine,
  RiRecordCircleFill,
  RiRecordCircleLine,
} from '@remixicon/react';
import { useTranslation } from 'react-i18next';

interface AudioDurationProps {
  audioUrl: string;
  onPlayStateChange?: (isPlaying: boolean) => void;
  onTimeUpdate?: (currentTime: number) => void;
  onError?: (error: Error) => void;
  className?: string;
  isDotOnly?: boolean;
}

interface AudioState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  error: Error | null;
}

const AudioDuration: FC<AudioDurationProps> = ({ ...props }: AudioDurationProps) => {
  const { audioUrl, onPlayStateChange, onTimeUpdate, onError, isDotOnly, className = '' } = props;
  const [state, setState] = useState<AudioState>({
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    error: null,
  });
  const { t } = useTranslation();

  const audioRef = useRef<HTMLAudioElement | null>(null);
  const progressRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const audio = new Audio(audioUrl);
    audioRef.current = audio;

    const handleLoadedMetadata = () => {
      setState((prev) => ({
        ...prev,
        duration: audio.duration,
      }));
    };

    const handleTimeUpdate = () => {
      const currentTime = audio.currentTime;
      setState((prev) => ({ ...prev, currentTime }));
      onTimeUpdate?.(currentTime);
    };

    const handleError = () => {
      const error = new Error(t('common.error.errorAudio'));
      setState((prev) => ({ ...prev, error }));
      onError?.(error);
    };

    const handleEnded = () => {
      setState((prev) => ({ ...prev, isPlaying: false, currentTime: 0 }));
      onPlayStateChange?.(false);
    };

    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('error', handleError);
    audio.addEventListener('ended', handleEnded);

    return () => {
      if (audio) {
        audio.pause();
        audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
        audio.removeEventListener('timeupdate', handleTimeUpdate);
        audio.removeEventListener('error', handleError);
        audio.removeEventListener('ended', handleEnded);
      }
    };
  }, [audioUrl, onTimeUpdate, onError, onPlayStateChange, t]);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${String(mins).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
  };

  const handlePlayPause = (): void => {
    if (!audioRef.current) {
      return;
    }

    if (state.isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play().catch((error) => {
        setState((prev) => ({ ...prev, error }));
        onError?.(error);
      });
    }

    setState((prev) => ({ ...prev, isPlaying: !prev.isPlaying }));
    onPlayStateChange?.(!state.isPlaying);
  };

  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>): void => {
    if (!progressRef.current || !audioRef.current) {
      return;
    }

    const rect = progressRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const percentage = x / rect.width;
    const newTime = percentage * state.duration;

    audioRef.current.currentTime = newTime;
    setState((prev) => ({ ...prev, currentTime: newTime }));
    onTimeUpdate?.(newTime);
  };

  if (!audioUrl) {
    return <p className="text-primary">{t('common.error.noResponse')}</p>;
  }

  if (state.error) {
    return <p className="text-delete">{t('common.error.errorAudio')}</p>;
  }

  return isDotOnly ? (
    <button
      onClick={handlePlayPause}
      className="w-6 h-6 flex items-center justify-center rounded-full"
      aria-label={state.isPlaying ? 'Pause' : 'Play'}
    >
      {state.isPlaying ? (
        <RiRecordCircleFill size={26} color={'#F53E3E'} />
      ) : (
        <RiRecordCircleLine size={26} color={'#F53E3E'} />
      )}
    </button>
  ) : (
    <div className={`flex items-center w-full gap-3 ${className}`}>
      <button
        onClick={handlePlayPause}
        className="w-6 h-6 flex items-center justify-center rounded-full"
        aria-label={state.isPlaying ? 'Pause' : 'Play'}
      >
        {state.isPlaying ? (
          <RiPauseCircleLine color={'#20232C'} size={26} />
        ) : (
          <RiPlayCircleLine color={'#20232C'} size={26} />
        )}
      </button>

      <div
        className="flex-1 bg-disabled h-[6px] rounded-full max-w-[140px] cursor-pointer"
        ref={progressRef}
        onClick={handleProgressClick}
      >
        <div
          className="rounded-full cursor-pointer relative"
          role="progressbar"
          aria-valuenow={(state.currentTime / state.duration) * 100}
          aria-valuemin={0}
          aria-valuemax={100}
        >
          <div className="absolute h-full w-full">
            <div
              className="absolute h-[6px] bg-primary-crm rounded-full top-0"
              style={{ width: `${(state.currentTime / state.duration) * 100}%` }}
            />
            <div
              className="absolute h-[10px] w-[10px] bg-primary-crm rounded-full -top-[2px]"
              style={{ left: `calc(${(state.currentTime / state.duration) * 100}% - 6px)` }}
            />
          </div>
        </div>
      </div>

      <div className="text-sm text-primary-crm flex items-center space-x-1">
        <span>{formatTime(state.currentTime)}</span>
        <span>/</span>
        <span>{formatTime(state.duration)}</span>
      </div>
    </div>
  );
};

export default AudioDuration;
