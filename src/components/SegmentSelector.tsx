import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { t } from 'i18next';
import { Button } from './ui/button';
import { useState } from 'react';
import { RiAddLine, RiInformation2Line, RiLoader2Line } from '@remixicon/react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import InputField from '@/pages/Email/components/form/InputField';
import { Form } from './ui/form';
import SearchBar from './SearchBar';
import { fnSearchString } from '@/utils/string';
import { getSelector, useAppDispatch, useAppSelector } from '@/hooks/reduxHooks';
import { createSegment } from '@/store/segment/action';

type Props = {
  onChange: (selected: string) => void;
  currentSegment: string;
};

const SegmentSelector = (props: Props) => {
  const { onChange, currentSegment } = props;
  const { allSegments } = useAppSelector(getSelector('segment'));
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState<boolean>(false);
  const [search, setSearch] = useState<string>('');
  const allOptions = allSegments.items.map((item) => ({ label: item.name, value: item.id })) ?? [];

  const form = useForm<{ createNew: string }>({
    resolver: zodResolver(z.object({ createNew: z.string().nonempty() })),
    defaultValues: {
      createNew: '',
    },
  });
  const [openModal, setOpenModal] = useState<boolean>(false);

  const handleSelect = (value: string) => {
    onChange(value);
    setOpenModal(false);
  };

  const onCreateNewSegment = async (data: { createNew: string }) => {
    setLoading(true);
    dispatch(createSegment({ name: data.createNew }))
      .unwrap()
      .then((data) => {
        onChange(data.id);
        form.setValue('createNew', '');
      })
      .catch((error) => {
        return error;
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const currentSegmentOption = allOptions.find((option) => option.value === currentSegment);
  const options = search.trim()
    ? allOptions.filter((option) => fnSearchString(option.label, search))
    : allOptions;

  return (
    <Popover open={openModal} onOpenChange={setOpenModal}>
      <PopoverTrigger className="border w-full text-sm text-tertiary text-start h-10 py-2.5 px-3 rounded-xl truncate">
        {currentSegmentOption?.label ?? t('common.contactList.selectYourSegment')}
      </PopoverTrigger>
      <PopoverContent
        align="center"
        className="w-[778px] max-w-[calc(100vw-2rem)] overflow-auto p-2 rounded-xl"
      >
        <SearchBar
          className="h-11"
          placeholder={t('contactList.searchSegmentPlaceHolder')}
          value={search}
          setSearchQuery={setSearch}
        />
        <div className="flex flex-col gap-2 max-h-[250px] overflow-auto mt-2">
          {options.length === 0 && (
            <div className="h-[100px] flex items-center justify-center flex-col text-muted-foreground">
              <RiInformation2Line />
              {t('common.noData')}
            </div>
          )}
          {options.map((option) => (
            <Button
              variant={option.value === currentSegment ? 'default' : 'ghost'}
              className="p-2 w-full flex justify-start rounded-md"
              onClick={() => handleSelect(option.value)}
              key={option.value}
            >
              {option.label}
            </Button>
          ))}
        </div>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onCreateNewSegment)}
            className="w-full border-t py-3 px-2 flex items-start gap-2 mt-2"
          >
            <InputField
              placeholder={t('common.button.createNewSegment')}
              control={form.control}
              name={'createNew'}
              className="text-sm"
              disabled={loading}
            />
            <Button
              disabled={loading}
              variant={'secondary'}
              size={'lg'}
              className="font-semibold px-3 py-2.5 rounded-xl gap-1"
            >
              {loading ? (
                <RiLoader2Line size={20} className="animate-spin" />
              ) : (
                <RiAddLine size={20} />
              )}
              {t('common.button.createNewSegment')}
            </Button>
          </form>
        </Form>
      </PopoverContent>
    </Popover>
  );
};

export default SegmentSelector;
