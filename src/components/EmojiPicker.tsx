import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { Button } from './ui/button';
import { RiEmotionHappyLine } from '@remixicon/react';
import data from '@emoji-mart/data';
import Picker from '@emoji-mart/react';
import { cn } from '@/lib/utils';

interface Props {
  onEmojiSelect: (emoji: Emoji) => void;
  className?: string;
}

interface Emoji {
  id: string;
  name: string;
  native: string;
  unified: string;
  keywords: string[];
  shortcodes: string;
  emoticons: string[];
}

const EmojiPicker = ({ onEmojiSelect, className }: Props) => (
  <Popover>
    <PopoverTrigger asChild>
      <Button variant="textOnly" className={cn('p-0', className)}>
        <RiEmotionHappyLine />
      </Button>
    </PopoverTrigger>
    <PopoverContent className="w-full p-0 border-none">
      <Picker previewPosition="none" theme={'light'} data={data} onEmojiSelect={onEmojiSelect} />
    </PopoverContent>
  </Popover>
);
export default EmojiPicker;
