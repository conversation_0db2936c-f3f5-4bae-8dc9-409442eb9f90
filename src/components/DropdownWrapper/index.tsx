import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import React from 'react';
import { cn } from '@/lib/utils';

interface IDropdownWrapper {
  children: React.ReactNode;
  trigger: React.ReactNode;
  triggerClassName?: string;
  contentClassName?: string;
  alignOffset?: number;
  sideOffset?: number;
}

export const DropdownWrapper: React.FC<IDropdownWrapper> = (props: IDropdownWrapper) => {
  const { children, trigger, triggerClassName, contentClassName, alignOffset = 0, sideOffset = 0 } = props;
  return (
    <DropdownMenu>
      <DropdownMenuTrigger
        className={cn(
          'h-10 px-[10px] border rounded-2xl border-big360Color-neutral-300 data-[state=open]:border-big360Color-brand-500 focus:outline-none',
          triggerClassName,
        )}
      >
        {trigger}
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className={cn('border border-big360Color-neutral-300',contentClassName)}
        align="end"
        alignOffset={alignOffset}
        sideOffset={sideOffset}
      >
        {children}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
