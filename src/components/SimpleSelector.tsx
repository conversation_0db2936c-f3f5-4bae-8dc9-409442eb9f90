import React, { useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';
import { TSelectOption } from '@/types/select';
import { cn } from '@/lib/utils';
import { RiArrowDownSLine, RiCloseLine } from '@remixicon/react';

type Props = {
  options: TSelectOption[];
  // onChange: (value: string) => void;
  defaultOption?: string;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  icon?: React.ReactNode;
};

const SimpleSelector = ({
  options,
  defaultOption,
  placeholder,
  disabled = false,
  className = '',
  icon,
}: Props) => {
  const [open, setOpen] = useState(false);
  const [selected, setSelected] = useState<TSelectOption | null>(
    options.find((option) => option.value === defaultOption) || null,
  );
  const handleSelect = (option: TSelectOption) => {
    setSelected(option || null);
  };

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    setSelected(null);
  };

  return (
    <DropdownMenu open={open}>
      <DropdownMenuTrigger onClick={() => setOpen(!open)}>
        <div
          className={cn(
            'min-w-[170px] h-10 cursor-pointer relative flex items-center justify-center gap-1 px-2 border-[1px] border-tertiary rounded-xl',
            className,
            disabled && 'opacity-50 pointer-events-none',
          )}
        >
          <span
            className={cn(
              'absolute left-2 flex gap-1 items-center px-1 bg-card text-sm transition-all duration-300 pointer-events-none text-filter',
              selected ? '-top-3 text-xs' : 'top-2',
            )}
          >
            {icon}
            <span>{placeholder}</span>
          </span>
          {selected && (
            <div
              className="w-full text-start z-10 bg-brand-foreground rounded-md px-2 text-xs font-medium py-1 text-brand flex items-center"
              onClick={() => {
                console.log('tesst');
              }}
            >
              {selected.label}
              <button onClick={handleRemove}>
                <RiCloseLine size={16} />
              </button>
            </div>
          )}
          <RiArrowDownSLine size={20} className="ml-auto shrink-0 opacity-50" />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="min-w-[215px]">
        {options.map((option) => (
          <DropdownMenuItem
            onClick={() => {
              setOpen(false);
              handleSelect(option);
            }}
            key={option.value}
          >
            {option.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
export default SimpleSelector;
