import React, { useEffect, useState } from 'react';
import Modal from '@/components/Modal';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { CleaningUpIcon } from '@/assets/CleaningUpIcon';
import { TCleaning } from '@/types/segment';
import { cn } from '@/lib/utils';

type TCleanUpContainer = {
  countQuantity: number;
  extraHeader?: React.ReactNode;
  trigger: (value: () => void) => React.ReactNode;
  handleCleanUp: (data: TCleaning) => void;
  removePopup: boolean;
  setRemovePopup: (value: boolean) => void;
};

// const min = 0;

export const CleanUpContainer: React.FC<TCleanUpContainer> = ({ ...props }: TCleanUpContainer) => {
  const { countQuantity, extraHeader, removePopup, trigger, handleCleanUp, setRemovePopup } = props;
  // const max = countQuantity ?? 0;
  const { t } = useTranslation();
  // const [quantity, setQuantity] = useState<number>();
  const [openModal, setOpenModal] = useState<boolean>(false);
  const [isCleaning, setIsCleaning] = useState<boolean>(false);

  const handleCleanupSuccess = (refetchData: () => void) => {
    setIsCleaning(true);
    refetchData();
  };

  useEffect(() => {
    if (removePopup) {
      setTimeout(() => {
        setOpenModal(false);
        setRemovePopup(false);
      }, 3000);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [removePopup]);

  // const handleSetQuantity = (e: ChangeEvent<HTMLInputElement>) => {
  //   const quantity = Math.min(Math.max(parseInt(e.target.value, 10), min), max);
  //   setQuantity(quantity);
  // };

  return (
    <Modal
      trigger={trigger(() => {
        setIsCleaning(false);
        setOpenModal(true);
      })}
      openModal={openModal}
      className={cn('w-[850px] max-w-none', isCleaning && 'w-[500px] h-[350px]')}
      titleAlign={'center'}
      isCloseIcon={false}
      title={isCleaning ? undefined : t('common.verify')}
    >
      {isCleaning ? (
        <div className="flex flex-col items-center justify-center m-auto gap-6">
          <p className="text-xl font-semibold leading-9 tracking-[0.6px]">
            {t('common.verifying')}
          </p>
          <CleaningUpIcon />
          <div className="text-center text-sm text-black">
            <p>{t('verify.verifyContent1')}</p>
            <p>{t('verify.verifyContent2')}</p>
          </div>
        </div>
      ) : (
        <div>
          {extraHeader}
          <div className="rounded-2xl h-[120px] p-6 border-tertiary border">
            <div className="flex gap-6 justify-between">
              <div>
                <p className="text-base font-medium text-secondary mb-4">
                  {t('common.filterQuantity')}
                </p>
                <div className="flex items-center gap-2">
                  {/*<Input*/}
                  {/*  id="number"*/}
                  {/*  type="number"*/}
                  {/*  value={quantity}*/}
                  {/*  onChange={handleSetQuantity}*/}
                  {/*  placeholder={t('placeHolder.enterQuantity')}*/}
                  {/*  className="h-[40px] w-[122px] placeholder:text-sm"*/}
                  {/*/>*/}
                  <p className="flex gap-1 text-sm font-normal">
                    <span className="font-medium">
                      {Number(countQuantity ?? 0).toLocaleString()}
                    </span>
                    <span>
                      {t('common.resultsFound', {
                        symbol: Number(countQuantity) > 1 ? 's' : '',
                      })}
                    </span>
                  </p>
                </div>
              </div>
              <div>
                <p className="text-base font-medium text-secondary mb-4">{t('common.price')}</p>
                <p className="text-3xl font-semibold text-primary-crm">$ 100</p>
              </div>
            </div>
          </div>
          <div className="flex gap-4 mt-2">
            <Button
              onClick={() => setOpenModal(false)}
              variant={'cancel'}
              className="flex-1 h-[40px]"
            >
              {t('common.button.cancel')}
            </Button>
            <Button
              variant={'primary'}
              disabled={countQuantity === 0}
              onClick={() => handleCleanUp({ quantity: countQuantity ?? 0, handleCleanupSuccess })}
              className="flex-1 h-[40px]"
            >
              {t('common.button.accept')}
            </Button>
          </div>
        </div>
      )}
    </Modal>
  );
};
