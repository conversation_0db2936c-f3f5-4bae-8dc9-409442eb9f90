import { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks';
import { cn } from '@/lib/utils';
import { Box } from '@/components/Box';
import { Button } from '@/components/ui/button';
import { FolderCloseLine } from '@/assets/FolderCloseLineIcon';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { LoadingButtonIcon } from '@/components/Loading/LoadingButton';
import { handleGetSegmentLog } from '@/store/SegmentLog/slice';
import { formatDate } from '@/utils/helper';
import { useTranslation } from 'react-i18next';
import { Badge } from '@/components/ui/badge';
import { getAllAudienceSegment, getAllDatasetSegment } from '@/store/segment/action';
import { ShareFolderIcon } from '@/assets/ShareFolderIcon';

export const SegmentLog = () => {
  const { logSegment, loading, error } = useAppSelector((state) => state.segmentLog);
  const dispatch = useAppDispatch();
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [prevLogSegment, setPrevLogSegment] = useState<typeof logSegment>();

  useEffect(() => {
    if (prevLogSegment && logSegment) {
      const statusChanged = logSegment.items.some((item) => {
        const prevItem = prevLogSegment.items.find((prev) => prev.segment_id === item.segment_id);
        return prevItem && prevItem.status === 2 && item.status === 1;
      });

      if (statusChanged) {
        dispatch(getAllAudienceSegment()).unwrap();
        dispatch(getAllDatasetSegment()).unwrap();
      }
    }
    setPrevLogSegment(logSegment);
  }, [logSegment, prevLogSegment, dispatch]);

  const handleConvertStatus = (status: string | number) => {
    switch (status) {
      case 0:
        return <Badge variant={'error'}>{t('common.error.title')}</Badge>;
      case 1:
        return <Badge variant={'success'}>{t('common.success')}</Badge>;
      case 2:
        return <Badge variant={'process'}>{t('common.inprogress')}</Badge>;
    }
  };

  const handlePopoverOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (open) {
      // Fetch segment log when popover opens
      dispatch(handleGetSegmentLog()).unwrap();
    }
  };

  const handleConvertType = (type: string) => {
    switch (type) {
      case 'social':
        return 'Social';
      case 'persona':
        return 'Work Persona';
      default:
        return '';
    }
  };

  const isActive = logSegment?.items?.some((item) => item.status === 2) || false;

  return (
    <Popover open={isOpen} onOpenChange={handlePopoverOpenChange}>
      <PopoverTrigger asChild>
        <Button variant={'secondary'} className="bg-[#F0F0F0] flex gap-2 border-none">
          <ShareFolderIcon isActive={isActive} />
          {t('segmentLog.title')}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        align="end"
        className="w-full z-[9999] bg-[#F0F0F0] rounded-2xl border-none max-w-[calc(100%-16px)] md:min-w-[403px] p-3 shadow-[0_0_32px_0_rgba(9,10,13,0.02),_0_4px_20px_-8px_rgba(9,10,13,0.10)]"
      >
        <Box className="w-full">
          <p className="text-primary text-sm font-semibold py-[10px]">
            {t('segmentLog.recentSegmentAdded')}
          </p>
        </Box>
        {loading ? (
          <LoadingButtonIcon />
        ) : error ? (
          <div className="text-center py-4">
            <p className="text-sm text-red-500">Failed to load segment log</p>
          </div>
        ) : (
          <div
            className={cn(
              'rounded-lg overflow-auto h-full max-h-[320px] ',
              !!logSegment?.items.length && 'mt-[12px]',
            )}
          >
            {logSegment?.items?.map((item) => (
              <div
                className="flex p-2 bg-white w-full max-w-md border border-[#E1E2E3]"
                key={item.segment_id}
              >
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-[#E1E2E3] flex items-center justify-center rounded-full">
                    <FolderCloseLine />
                  </div>
                </div>
                <div className="ml-4">
                  <p className="text-sm text-[#515667]">
                    {t('limitAlert.contactImportFrom')}{' '}
                    <span className="font-semibold text-primary">
                      {handleConvertType(item.type)} {item.segment_name}
                    </span>{' '}
                    {t('limitAlert.toContactList')}{' '}
                  </p>
                  <div className="flex gap-1 items-center my-1">
                    {handleConvertStatus(item.status)}
                    <p className="text-xs text-[#515667]">
                      {t('limitAlert.contactImport')}: {formatDate(item.date_import, '/')}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
};
