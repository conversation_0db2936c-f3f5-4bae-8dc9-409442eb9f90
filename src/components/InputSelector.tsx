import useOutsideClick from '@/hooks/useClickOutSide';
import { cn } from '@/lib/utils';
import { Option } from '@/types/contactList';
import { RiAddLine, RiArrowDownSLine, RiCloseLine, RiLoader2Line } from '@remixicon/react';
import { t } from 'i18next';
import React, { useEffect, useRef, useState } from 'react';
import useDebounce from '@/hooks/useDebounce';

type Props = {
  options: Option[];
  onChange: (selected: string) => void;
  placeholder?: string;
  label?: string;
  defaultValue?: string;
  className?: string;
  classNameInput?: string;
  onSearch?: (search: string) => void;
  handleCreateNew?: (input: string) => void;
  loading?: boolean;
};

const InputSelector = ({
  onChange,
  options,
  placeholder,
  label,
  defaultValue,
  className,
  classNameInput,
  handleCreateNew,
  loading = false,
  onSearch,
}: Props) => {
  const [selected, setSelected] = useState<Option | null>(
    options.find((item) => item.value === defaultValue) || null,
  );
  const [open, setOpen] = useState<boolean>(false);
  const divRef = useRef<HTMLDivElement>(null);
  const optionRef = useRef<HTMLDivElement>(null);
  const [searchApi, setSearchApi] = useState<string>('');
  const [search, setSearch] = useState<string>('');

  useOutsideClick(divRef, optionRef, () => setOpen(!open));

  useEffect(() => {
    if (onSearch) {
      onSearch(searchApi);
    }
    //   eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchApi]);

  useEffect(() => {
    if (selected) {
      setSearch(selected.label);
    }
  }, [selected]);

  const newOptions =
    options.filter((option) => option.label.toLowerCase().includes(search.toLowerCase())) ??
    options;

  const handleRemove = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setSelected(null);
    setSearch('');
    onChange('');
  };

  const debouncedSearchQuery: string = useDebounce(search, 300) ?? '';

  useEffect(() => {
    setSearchApi(debouncedSearchQuery);
  }, [debouncedSearchQuery]);

  return (
    <div
      ref={divRef}
      onClick={() => setOpen(!open)}
      className={cn(
        'h-10 cursor-pointer flex pr-2 items-center justify-center text-sm border rounded-xl relative',
        className,
      )}
    >
      <span className='ml-3'>
        <RiAddLine size={20} color={'#515667'}/>
      </span>
      <input
        value={search}
        onChange={(value) => setSearch(value.target.value)}
        placeholder={placeholder}
        className={cn('rounded-xl h-full w-full outline-none p-2 mr-2', classNameInput)}
      />
      <span
        className={cn(
          'absolute bg-card left-2 duration-150 -translate-y-1/2',
          open ? '-top-1 text-xs text-secondary' : 'top-1/2',
          (selected || search) && '-top-1 text-xs text-secondary',
        )}
      >
        {label}
      </span>
      {selected && (
        <button
          onClick={handleRemove}
          className="absolute right-6 hover:bg-secondary hover:text-primary rounded-xl p-1"
        >
          <RiCloseLine size={20} />
        </button>
      )}
      {loading ? (
        <RiLoader2Line size={16} className="ml-auto flex-shrink-0 animate-spin" />
      ) : (
        <RiArrowDownSLine size={16} className="ml-auto flex-shrink-0" />
      )}
      {open && (
        <div
          ref={optionRef}
          className="z-20 flex flex-col absolute left-[-1px] max-h-[152px] w-full overflow-x-hidden shadow-md border bg-card bottom-full mb-1 p-1 rounded-xl"
        >
          {options.length === 0 && (
            <div className="flex-1 h-full min-h-10 flex items-center justify-center">
              {t('common.noData')}
            </div>
          )}
          {search.trim() && newOptions.length === 0 && (
            <button
              onClick={handleCreateNew && (() => handleCreateNew(search))}
              className="flex items-center w-full p-2 rounded-xl text-start hover:bg-primary-crm-foreground hover:text-primary"
            >
              <RiAddLine size={16} /> {search}
            </button>
          )}
          {newOptions.length > 0 && (
            <div className="flex flex-col py-3 px-2 gap-y-2">
              {newOptions.map((option) => (
                <button
                  className={cn(
                    'w-full p-2 rounded-xl text-start',
                    selected?.value === option.value
                      ? 'bg-brand-foreground text-brand'
                      : 'hover:bg-secondary hover:text-primary',
                  )}
                  onClick={() => {
                    setSelected(option);
                    onChange(option.value);
                  }}
                  key={option.value}
                >
                  <p className="truncate">{option.label}</p>
                </button>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default InputSelector;
