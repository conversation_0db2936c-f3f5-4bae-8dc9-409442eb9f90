import * as React from 'react';

import { cn } from '@/lib/utils';

type TTextareaProps = {
  className?: string;
};

const Textarea = React.forwardRef<
  HTMLTextAreaElement,
  React.ComponentProps<'textarea'> & TTextareaProps
>(({ className, ...props }, ref) => {
  return (
    <textarea
      className={cn(
        'flex min-h-[138px] w-full rounded-xl border border-border bg-transparent p-3 text-sm text-secondary placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
        className,
      )}
      ref={ref}
      {...props}
    />
  );
});
Textarea.displayName = 'Textarea';

export { Textarea };
