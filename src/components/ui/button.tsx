import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';

const buttonVariants = cva(
  'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-2xl text-sm font-medium transition-colors disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0',
  {
    variants: {
      variant: {
        default: 'bg-primary rounded-2xl text-primary-foreground hover:bg-brand-foreground',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline:
          'border border-big360Color-neutral-300 bg-background rounded-2xl hover:bg-accent hover:text-accent-foreground',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-black/20',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
        primary:
          'bg-primary rounded-2xl text-background-foreground hover:bg-brand-foreground font-medium',
        cancel:
          'bg-secondary rounded-2xl text-primary hover:bg-secondary-foreground_crm hover:text-filter font-medium',
        icon: 'bg-transparent text-primary-foreground hover:bg-transparent p-0',
        delete:
          'font-medium bg-delete text-background-foreground rounded-xl hover:bg-delete-foreground hover:text-background-foreground',
        ghostError: 'bg-transparent text-error-text font-medium hover:text-error-text',
        textOnly: 'bg-transparent text-primary-crm font-medium hover:text-primary-crm',
      },
      size: {
        default: 'h-10 px-4 py-2 font-medium text-sm',
        sm: 'h-8 rounded-2xl px-3 text-xs',
        lg: 'h-10 rounded-2xl px-8',
        icon: 'h-9 w-9',
        textOnly: 'p-0 m-0 h-auto',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button';
    return (
      <Comp className={cn(buttonVariants({ variant, size, className }))} ref={ref} {...props} />
    );
  },
);
Button.displayName = 'Button';

export { Button, buttonVariants };
