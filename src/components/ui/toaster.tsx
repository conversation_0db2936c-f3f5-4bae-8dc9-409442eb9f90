import { useToast } from '@/hooks/use-toast';
import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastIcon,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from '@/components/ui/toast';
import React from 'react';

export const Toaster: React.FC = () => {
  const { toasts } = useToast();

  const handleCheckStatus = (status: string) => {
    switch (status) {
      case 'info':
        return 'border-info text-info-text bg-info-background';
      case 'success':
        return 'border-success-hover text-success-text bg-success';
      case 'warning':
        return 'border-warning text-warning-text bg-warning-background';
      case 'error':
        return 'border-delete text-error-text bg-error';
      default:
        return '';
    }
  };
  return (
    <ToastProvider>
      {toasts.map(function ({ id, status, title, description, action, duration = 1500, ...props }) {
        return (
          <Toast key={id} {...props} duration={duration} className={handleCheckStatus(status??'')}>
            <div className="flex gap-3 items-center">
              <ToastIcon status={status} />
              <div className="grid gap-1 flex-1">
                {title && <ToastTitle>{title}</ToastTitle>}
                {description && <ToastDescription className={'text-sm font-normal'}>{description}</ToastDescription>}
              </div>
            </div>
            {action}
            <ToastClose status={status}/>
          </Toast>
        );
      })}
      <ToastViewport />
    </ToastProvider>
  );
};
