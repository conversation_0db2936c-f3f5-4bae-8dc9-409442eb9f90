import * as React from 'react';
import * as ProgressPrimitive from '@radix-ui/react-progress';

import { cn } from '@/lib/utils';
import { useMemo } from 'react';

type TProgressProps = {
  value?: number;
  className?: string;
  color: string;
};
const Progress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root> & TProgressProps
>(({ className, value, color, ...props }, ref) => {
  const normalizedValue = React.useMemo(() => {
    if (value === undefined || value === null) return 0;
    return Math.min(Math.max(value, 0), 100);
  }, [value]);

  const transform = useMemo(() => {
    return `translateX(-${100 - normalizedValue}%)`;
  }, [normalizedValue]);

  return (
    <ProgressPrimitive.Root
      ref={ref}
      className={cn('relative h-2 w-full overflow-hidden rounded-full bg-primary/20', className)}
      {...props}
    >
      <ProgressPrimitive.Indicator
        className="h-full w-full flex-1 transition-all"
        style={{
          transform,
          background: color,
        }}
      />
    </ProgressPrimitive.Root>
  );
});
Progress.displayName = ProgressPrimitive.Root.displayName;

export { Progress };
