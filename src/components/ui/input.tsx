import * as React from 'react';

import { cn } from '@/lib/utils';

export type InputProps = React.HTMLAttributes<HTMLInputElement> & {
  type?: React.HTMLInputTypeAttribute;
};

const Input = React.forwardRef<HTMLInputElement, React.ComponentProps<'input'> & InputProps>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          'flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base transition-colors focus:border-big360Color-brand-500 file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
          className,
        )}
        ref={ref}
        {...props}
      />
    );
  },
);
Input.displayName = 'Input';

export { Input };
