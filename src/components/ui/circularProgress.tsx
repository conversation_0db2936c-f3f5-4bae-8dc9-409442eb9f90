import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import { useEffect } from 'react';

const circularProgressVariants = cva('relative inline-flex items-center justify-center', {
  variants: {
    size: {
      sm: 'w-[16px] h-[16px]',
      md: 'w-[18px] h-[18px]',
      lg: 'w-[20px] h-[20px]',
      xl: 'w-[22px] h-[22px]',
    },
  },
  defaultVariants: {
    size: 'md',
  },
});

export interface CircularProgressProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof circularProgressVariants> {
  value: number;
  trackColor?: string;
  progressColor?: string;
  animateOnMount?: boolean;
}

const CircularProgress = React.forwardRef<HTMLDivElement, CircularProgressProps>(
  (
    {
      className,
      value,
      size,
      trackColor = '#F3F4F6',
      progressColor = '#6B7280',
      animateOnMount = false,
      ...props
    },
    ref,
  ) => {
    const [animatedValue, setAnimatedValue] = React.useState(animateOnMount ? 0 : value);

    const clampedValue = Math.min(100, Math.max(0, value));

    useEffect(() => {
      if (animateOnMount) {
        setAnimatedValue(0);
        const duration = 800;
        const startTime = Date.now();

        const animate = () => {
          const elapsed = Date.now() - startTime;
          const progress = Math.min(elapsed / duration, 1);
          const currentValue = progress * clampedValue;

          setAnimatedValue(currentValue);

          if (progress < 1) {
            requestAnimationFrame(animate);
          }
        };

        requestAnimationFrame(animate);
      } else {
        setAnimatedValue(clampedValue);
      }
    }, [clampedValue, animateOnMount]);

    const displayValue = animateOnMount ? animatedValue : clampedValue;

    const sizeMap = {
      sm: 14,
      md: 16,
      lg: 18,
      xl: 20,
    };

    const currentSize = sizeMap[size || 'md'];
    const center = currentSize / 2;
    const radius = center;

    const createPath = (percentage: number) => {
      const angle = (percentage / 100) * 360;
      const radians = (angle - 90) * (Math.PI / 180);

      const x = center + radius * Math.cos(radians);
      const y = center + radius * Math.sin(radians);

      const largeArcFlag = angle > 180 ? 1 : 0;

      if (percentage === 0) {
        return '';
      }
      if (percentage >= 100) {
        return `M ${center} ${center - radius} A ${radius} ${radius} 0 1 1 ${center - 0.01} ${center - radius} Z`;
      }

      return `M ${center} ${center} L ${center} ${center - radius} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x} ${y} Z`;
    };

    return (
      <div
        ref={ref}
        className={cn(
          'border-[2px] rounded-full border-[#515667] p-0.5',
          circularProgressVariants({ size, className }),
        )}
        {...props}
      >
        <svg width={currentSize} height={currentSize} viewBox={`0 0 ${currentSize} ${currentSize}`}>
          <circle cx={center} cy={center} r={radius} fill={trackColor} />

          <path d={createPath(displayValue)} fill={progressColor} />
        </svg>
      </div>
    );
  },
);

CircularProgress.displayName = 'CircularProgress';

export { CircularProgress, circularProgressVariants };
