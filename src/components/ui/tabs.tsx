import * as React from 'react';
import * as TabsPrimitive from '@radix-ui/react-tabs';

import { cn } from '@/lib/utils';

const Tabs = TabsPrimitive.Root;

type TTabProps = {
  className?: string;
  isVertical?: boolean;
};

const TabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List> & TTabProps
>(({ className, ...props }, ref) => (
  <TabsPrimitive.List ref={ref} className={cn('py-5 px-3 rounded-lg', className)} {...props} />
));
TabsList.displayName = TabsPrimitive.List.displayName;

const TabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger> & TTabProps
>(({ className, isVertical, ...props }, ref) => (
  <TabsPrimitive.Trigger
    ref={ref}
    className={cn(
      'relative inline-flex items-center whitespace-nowrap h-[52px] pl-[8px] pr-[40px] py-4 text-sm font-medium transition-all disabled:pointer-events-none text-filter disabled:opacity-50 data-[state=active]:before:bg-brand aria-[selected=true]:text-brand before:absolute',
      className,
      isVertical
        ? 'before:w-[2px] before:h-full before:-left-[1px] justify-start h-[38px]'
        : 'before:w-full before:h-[2px] before:-bottom-[1px] before:left-0 justify-center',
    )}
    {...props}
  />
));
TabsTrigger.displayName = TabsPrimitive.Trigger.displayName;

const TabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content> & TTabProps
>(({ className, isVertical, ...props }, ref) => (
  <TabsPrimitive.Content
    ref={ref}
    className={cn('text-left', className, isVertical ? 'border-r' : '')}
    {...props}
  />
));
TabsContent.displayName = TabsPrimitive.Content.displayName;

export { Tabs, TabsList, TabsTrigger, TabsContent };
