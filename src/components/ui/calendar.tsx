import * as React from 'react';
import { DayPicker, DropdownProps } from 'react-day-picker';
import 'react-day-picker/dist/style.css';
import { cn } from '@/lib/utils';
import { buttonVariants } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { RiArrowDownSLine } from '@remixicon/react';

type TCalendarProps = {
  className?: string;
  classNames?: object;
  showOutsideDays?: boolean;
  mode?: string;
};
export type CalendarProps = React.ComponentProps<typeof DayPicker> & TCalendarProps;
const days = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];

function Calendar({ className, classNames, showOutsideDays = true, ...props }: CalendarProps) {
  const customWeekdayFormatter = (day: Date) => {
    return days[day.getDay()];
  };
  const startMonth = new Date(1900, 1);
  const endMonth = new Date(2100, 11);
  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn(' rounded-2xl w-[256px]', className)}
      defaultMonth={new Date(1999, 11, 1)}
      captionLayout="dropdown-buttons"
      fromDate={startMonth}
      toDate={endMonth}
      formatters={{
        formatWeekdayName: customWeekdayFormatter,
      }}
      modifiers={{
        weekend: { dayOfWeek: [6, 0] }, // Saturday[6] and Sunday[0]
      }}
      modifiersStyles={{
        weekend: { color: '#F53E3E' },
      }}
      weekStartsOn={1} // Set week start to Monday
      classNames={{
        months: 'flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0',
        month: 'space-y-2 w-full',
        caption: 'flex justify-center pt-1 items-center relative',
        caption_label: 'text-base font-medium flex gap-1 items-center mx-2 cursor-pointer ',
        nav: 'space-x-1 flex items-center absolute w-full justify-between',
        nav_button: cn(
          buttonVariants({ variant: 'outline' }),
          'h-4 w-4 bg-transparent p-0 [&_svg]:text-primary rounded-lg border-none bg-secondary-foreground_crm text-secondary-foreground',
        ),
        nav_button_previous: 'left-4',
        nav_button_next: 'right-4',
        table: 'w-full border-collapse space-y-1',
        head_row:
          'flex border-b justify-between border-tertiary [&>*:nth-child(6)]:text-delete [&>*:nth-child(7)]:text-delete',
        head_cell: 'text-secondary rounded-[1rem] w-8 font-medium',
        row: 'flex w-full mt-2 justify-between',
        cell: cn(
          'relative p-0 text-center text-base font-normal focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected].day-range-end)]:rounded-r-[1rem]',
          props.mode === 'range'
            ? '[&:has(>.day-range-end)]:rounded-r-[1rem] [&:has(>.day-range-start)]:rounded-l-[1rem] first:[&:has([aria-selected])]:rounded-l-[1rem] last:[&:has([aria-selected])]:rounded-r-[1rem]'
            : '[&:has([aria-selected])]:rounded-[1rem]',
        ),
        day: cn(
          buttonVariants({ variant: 'ghost' }),
          'h-8 w-8 p-0 font-normal aria-selected:opacity-100 text-base',
        ),
        day_range_start: 'day-range-start',
        day_range_end: 'day-range-end',
        day_selected:
          'rounded-[1rem] bg-brand text-primary-foreground hover:bg-brand hover:text-primary-foreground focus:bg-brand focus:text-primary-foreground',
        day_today: 'bg-accent text-accent-foreground',
        day_outside:
          'day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground',
        day_disabled: 'text-muted-foreground opacity-50',
        day_range_middle: 'aria-selected:bg-accent aria-selected:text-accent-foreground',
        day_hidden: 'invisible',
        ...classNames,
      }}
      components={{
        Dropdown: ({ value, onChange, children }: DropdownProps) => {
          const options = React.Children.toArray(children) as React.ReactElement<
            React.HTMLProps<HTMLOptionElement>
          >[];
          const selected = options.find((child) => child.props.value === value);
          const handleChange = (value: string) => {
            const changeEvent = {
              target: { value },
            } as React.ChangeEvent<HTMLSelectElement>;
            onChange?.(changeEvent);
          };
          return (
            <div className="z-10">
              <Select
                value={value?.toString()}
                onValueChange={(value) => {
                  handleChange(value);
                }}
              >
                <SelectTrigger
                  className="pr-1.5 border-none"
                  rightIcon={<RiArrowDownSLine size={22} className="text-filter" />}
                >
                  <SelectValue>{selected?.props?.children}</SelectValue>
                </SelectTrigger>
                <SelectContent position="popper" className="w-[100px]">
                  <ScrollArea className="h-80 cursor-pointer">
                    {options.map((option, id: number) => (
                      <SelectItem
                        key={`${option.props.value}-${id}`}
                        value={option.props.value?.toString() ?? ''}
                        className="cursor-pointer w-[100px]"
                      >
                        {option.props.children}
                      </SelectItem>
                    ))}
                  </ScrollArea>
                </SelectContent>
              </Select>
            </div>
          );
        },
      }}
      {...props}
    />
  );
}

Calendar.displayName = 'Calendar';

export { Calendar };
