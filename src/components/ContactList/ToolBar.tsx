import { ContactSubmitForm } from '@/components/ContactList/ContactSubmitForm';
import { FloatingLabel } from '@/components/ui/floating-label-input';
import { ROOT_PATH, ROOT_ROUTE, ROUTE_NAME_BIG360 } from '@/constants/router';
import { getSelector, useAppDispatch, useAppSelector } from '@/hooks/reduxHooks';
import { cn } from '@/lib/utils';
// import UploadPopup from '@/pages/ContactList/components/UploadPopup';
import { choosePhone, getUsedPhone } from '@/store/usedPhone/usedPhoneSlice';
import { QUERY_KEY } from '@/utils/constants';
import { RiAddCircleLine, RiArrowDownSLine, RiDownloadLine, RiPhoneLine } from '@remixicon/react';
import { useQueryClient } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate } from 'react-router-dom';
import DropdownModal, { TOptionsDrop } from '../DropdownModal';
import { Button } from '../ui/button';
import { setOpenRentPhone } from '@/store/phoneBought/phoneBoughtSlice';
import { handleFormatPhone } from '@/utils/helper';

const ToolBar = ({ onExportData }: { onExportData: () => void }) => {
  const dispatch = useAppDispatch();
  const { t } = useTranslation();
  const { items } = useAppSelector(getSelector('phone_bought'));
  const { phone } = useAppSelector(getSelector('used_phone'));
  const { isDataType } = useAppSelector(getSelector('contactList'));

  const [phoneSelected, setPhoneSelected] = useState<string>(phone.using_hotline);
  const navigate = useNavigate();

  const queryClient = useQueryClient();
  const handleRefetchContactList = () => {
    queryClient.invalidateQueries({
      queryKey: [QUERY_KEY.CONTACT_LIST],
      stale: true,
    });
  };

  const optionsDataSet: TOptionsDrop[] = [
    {
      label: (
        <Link
          to={`${ROUTE_NAME_BIG360.yourSegment}`}
          className="text-start text-base rounded-md text-secondary w-full"
        >
          {t('contactList.addFormBig360')}
        </Link>
      ),
      params: {
        isShowIcon: false,
        title: t('contactList.addFormBig360'),
        align: 'center',
        className: 'w-fit max-w-none',
      },
    },
  ];

  const options: TOptionsDrop[] = [
    {
      label: t('contactList.addNewContact'),
      modal: () => {
        return <ContactSubmitForm handleSubmitSuccess={handleRefetchContactList} isCreate={true} />;
      },
      params: {
        isShowIcon: false,
        title: t('contactList.titleAddContact'),
        align: 'center',
        className: 'w-fit max-w-none',
      },
    },
    // {
    //   label: t('contactList.upload'),
    //   params: {
    //     isShowIcon: false,
    //     title: t('contactList.uploadContact'),
    //     align: 'center',
    //     className: 'max-w-[850px]',
    //   },
    //   modal: (_, onClose) => <UploadPopup setIsOpen={onClose} />,
    // },
    {
      label: (
        <Link
          to={`${ROUTE_NAME_BIG360.yourSegment}`}
          className="text-start text-base rounded-md text-secondary w-full"
        >
          {t('contactList.addFormBig360')}
        </Link>
      ),
      params: {
        isShowIcon: false,
        title: t('contactList.addFormBig360'),
        align: 'center',
        className: 'w-fit max-w-none',
      },
    },
  ];
  useEffect(() => {
    setPhoneSelected(phone.using_hotline);
  }, [phone]);

  const phoneOptions: TOptionsDrop[] = items.reduce<TOptionsDrop[]>((acc, item) => {
    if (item.status !== 'spam') {
      acc.push({
        label: handleFormatPhone(item.phone_number),
        value: item.phone_number,
      });
    }
    return acc;
  }, []);

  const handleSelectPhone = async (phone_number: string) => {
    const phoneItem = items.find((item) => item.phone_number === phone_number);
    if (phoneItem?.id) {
      dispatch(choosePhone(phoneItem?.id))
        .unwrap()
        .then(() => dispatch(getUsedPhone()));
    }
  };

  const handleNavigateToPhone = () => {
    dispatch(setOpenRentPhone(!phone.using_hotline));
    navigate(`${ROOT_PATH}/${ROOT_ROUTE.contact.phone}`);
  };

  return (
    <div className="flex justify-between mb-3">
      {!phoneSelected ? (
        <PhoneContainer
          onClick={handleNavigateToPhone}
          empty={phoneOptions.length === 0}
          showArrow={false}
        >
          <span className="font-medium text-xl text-primary">
            <button
              className={cn('p-2 text-sm font-medium outline-none cursor-pointer rounded-lg')}
              onClick={handleNavigateToPhone}
            >
              {t('common.button.getPhoneNumber')}
            </button>
          </span>
        </PhoneContainer>
      ) : (
        <DropdownModal
          trigger={
            <PhoneContainer showArrow={true}>
              <p
                className={cn(
                  'font-medium text-base ',
                  phoneSelected ? 'text-primary ' : 'text-filter',
                )}
              >
                {handleFormatPhone(phoneSelected) ?? t('common.getHotline')}
              </p>
            </PhoneContainer>
          }
          onChange={handleSelectPhone}
          options={phoneOptions}
          className="w-[214px] max-h-[258px] overflow-y-auto"
          renderItem={(payload) =>
            NumberSelect(String(payload.label), phoneSelected === payload.label)
          }
          viewMore={{
            title: t('common.button.viewMorePhone'),
            onClick: () => {
              navigate(`${ROOT_PATH}/${ROOT_ROUTE.contact.phone}`);
            },
            className:
              'flex items-center justify-center gap-1 h-[32px] p-3 bg-secondary-foreground_crm font-medium text-primary text-sm rounded-xl hover:bg-secondary-foreground_crm hover:text-secondary',
          }}
        />
      )}
      <div className="flex gap-4 items-center">
        <Button
          size={'lg'}
          className="px-3 py-1 rounded-2xl"
          variant={'secondary'}
          onClick={onExportData}
        >
          <RiDownloadLine size={20} />
        </Button>
        <DropdownModal
          trigger={
            <div className="flex items-center p-3 gap-1 h-[40px] bg-create text-white font-medium text-sm rounded-xl hover:text-primary-foreground hover:bg-create-foreground">
              <RiAddCircleLine size={20} />
              {t('common.button.addContact')}
            </div>
          }
          options={isDataType === 'AUDIENCE' ? options : optionsDataSet}
        />
      </div>
    </div>
  );
};

export default ToolBar;

const NumberSelect = (title: string, isSelected: boolean) => {
  return (
    <>
      <div
        className={cn(
          'w-full cursor-pointer text-start text-primary text-base py-1 px-2 rounded-lg',
          isSelected
            ? 'bg-brand-foreground text-text-primary'
            : 'hover:bg-secondary-foreground_crm',
        )}
      >
        {title}
      </div>
      <div className="w-full my-1" />
    </>
  );
};

type PhoneContainerProps = {
  children: React.ReactNode;
  showArrow: boolean;
  empty?: boolean;
  onClick?: () => void;
};
const PhoneContainer = ({ ...props }: PhoneContainerProps) => {
  const { children, showArrow, empty = false, onClick } = props;
  const { t } = useTranslation();
  return (
    <div className="h-[40px] py-[8px] outline-none relative flex items-center justify-between text-start w-[214px] border shadow-none text-2xl font-medium	px-4 rounded-2xl">
      <FloatingLabel
        className={cn(
          'left-4 translate-y-1/2 -top-5 flex items-center gap-1 text-xs',
          empty && 'top-0 text-sm hover:text-secondary cursor-pointer',
        )}
        onClick={empty ? onClick : undefined}
      >
        <RiPhoneLine size={empty ? 20 : 14} />
        <span className="font-normal text-primary">{t('common.rentPhoneNumber')}</span>
      </FloatingLabel>
      {empty ? null : children}
      {showArrow ? <RiArrowDownSLine className="ml-auto" size={24} /> : <></>}
    </div>
  );
};
