import React from 'react';
import { TSearchFilter } from '@/types/contactList';
import ResultFilter from '@/components/ResultFilter';
import { paramsSearch } from '@/utils/constants';


type TListResultTableContact = {
  selectFilter: TSearchFilter;
  isAction: boolean;
  setSelectFilter: (selectFilter: TSearchFilter) => void;
  listResult: string[];
};


const ListResultTableContact: React.FC<TListResultTableContact> = ({
  ...props
}: TListResultTableContact) => {
  const { listResult, selectFilter, isAction, setSelectFilter } = props;

  if (!listResult.length) {
    return <></>
  }

  return (
    <div className="flex flex-wrap gap-2">
      {listResult.map((item: string) => {
        const value = item as keyof TSearchFilter;
        const result: string[] = (selectFilter[value])?.toString()?.split(',').filter(Boolean) ?? [];

        return (
          <ResultFilter
            key={item}
            keyInSearch={item}
            title={paramsSearch[value]}
            result={result}
            isAction={isAction}
            searchFilter={selectFilter}
            isShowMore={true}
            setSearchFilter={setSelectFilter}
          />
        );
      })}
    </div>
  );
};
export default ListResultTableContact;
