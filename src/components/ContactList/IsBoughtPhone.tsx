import { getSelector, useAppSelector } from '@/hooks/reduxHooks';
import { ReactNode } from 'react';
import { useParams } from 'react-router-dom';
import NotExistsBoughtPhone from './NotExistsBoughtPhone';
import { CONTACT_STATUS } from '@/constants/contact-list';

type Props = {
  children: ReactNode;
};

const IsBoughtPhone = ({ children }: Props) => {
  const { items } = useAppSelector(getSelector('phone_bought'));
  const { id } = useParams<{ id: string }>();
  const isBought = items.some(
    (item) => item.phone_number === id && item.status !== CONTACT_STATUS.SPAM,
  );

  if (!isBought) return <NotExistsBoughtPhone />;
  return <>{children}</>;
};
export default IsBoughtPhone;
