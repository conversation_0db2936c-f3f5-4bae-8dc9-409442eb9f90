import React, { Fragment, useEffect, useState } from 'react';
import SearchBar from '@/components/SearchBar';
import { RiSearchLine } from '@remixicon/react';
import { IContactFilter } from '@/types/contactList';
import { useTranslation } from 'react-i18next';
import { useLocation, useSearchParams } from 'react-router-dom';
import useDebounce from '@/hooks/useDebounce';
import { filterEmptyParams } from '@/utils/helper';
import { cn } from '@/lib/utils';

const FilterBarContainer: React.FC<IContactFilter> = ({ ...props }: IContactFilter) => {
  const {
    isFilter,
    isSearch,
    searchPlaceHolder,
    leftNode,
    centerNode,
    rightNode,
    bottomNode,
    filterNode
  } = props;
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [searchParams, setSearchParams] = useSearchParams();
  const allQueryParams = Object.fromEntries(searchParams.entries());
  const location = useLocation();
  useEffect(() => {
    const querySelect = allQueryParams['search'] ?? '';
    setSearchQuery(querySelect);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location]);

  const debouncedSearchQuery: string = useDebounce(searchQuery, 300) ?? '';

  useEffect(() => {
    if (searchQuery) {
      setSearchParams(filterEmptyParams({ ...allQueryParams, ['search']: debouncedSearchQuery }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debouncedSearchQuery]);

  return (
    <>
      {isFilter && filterNode ? filterNode : <></>}
      <div
        className={cn(isSearch || leftNode ? 'flex gap-4 w-full justify-between mt-4' : 'my-4',
          !bottomNode ? 'mb-4' : '')}
      >
        {leftNode ?? (
          <Fragment>
            {isSearch ? (
              <SearchBar
                value={searchQuery}
                setSearchQuery={(value) => {
                  setSearchQuery(value);
                  if (value === '') {
                    setSearchParams(filterEmptyParams({ ...allQueryParams, ['search']: '' }));
                  }
                }}
                className="w-full rounded-xl h-[40px]"
                placeholder={searchPlaceHolder ?? t('common.searchPlaceHolder')}
                icon={<RiSearchLine size={16} color={'#6B7183'} />}
              />
            ) : (
              <></>
            )}
          </Fragment>
        )}
        {centerNode ?? <></>}
        {rightNode ?? <></>}
      </div>
      {bottomNode ?? <></>}
    </>
  );
};

export default FilterBarContainer;
