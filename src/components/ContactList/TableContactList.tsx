import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  RiCakeLine,
  RiCloseCircleFill,
  RiFolderAddLine,
  RiFolderLine,
  RiIdCardLine,
  RiMapPinLine,
} from '@remixicon/react';
import DataTable from '@/components/table/DataTable';
import { ITableContactList, TContactItems, TSearchFilter } from '@/types/contactList';
import { ColumnDef } from '@tanstack/react-table';
import {
  ageSelectOptions,
  emptySegment,
  genderSelectOptions,
  monthSelectOptions,
  OptionFilterDemoGraphic,
} from '@/utils/constants';
// import { filterEmptyParams, paramsOrderBy } from '@/utils/helper';
import { filterEmptyParams } from '@/utils/helper';
import { useTranslation } from 'react-i18next';
import { Link, useLocation, useSearchParams } from 'react-router-dom';
import ContactHistory from '@/components/ContactList/ContactHistory';
import Modal from '@/components/Modal';
import { ModalSubmitSegment } from '@/components/Segment/ModalSubmitSegment';
import CustomToolTips from '@/components/CustomToolTips';
import { cn } from '@/lib/utils';
import MultipleSelectColumn from '@/components/MultipleSelectColumn';
import { MultipleSelect } from '@/components/MultipleSelect';
import ResultFound from '@/components/ResultFound';
import { TSegment, TSegmentData } from '@/types/segment';
// import { ENDPOINTS } from '@/apis/endpoints';
// import { CleanUpContainer } from '@/components/CleanUpContainer';
import ListResultTableContact from '@/components/ContactList/ListResultTableContact';
import { getSelector, useAppDispatch, useAppSelector } from '@/hooks/reduxHooks';
import ResponsiveButtonContainer from '@/components/ResponsiveGroupButton';
// import { update } from '@/apis/apiHelper';
import { setListOverdue } from '@/store/contactList/contactListSlice';
import { ROOT_PATH, ROOT_ROUTE } from '@/constants/router';
import { defaultFilter } from '@/constants/contact-list';

const TableContactList: React.FC<ITableContactList> = ({ ...props }: ITableContactList) => {
  const {
    columns,
    data,
    count,
    pagination,
    isLoading,
    filter,
    isShowHistory,
    isOverdue,
    rightNodeFilterContact,
    className,
    historyData,
    isDataSet,
    // sortContactList,
    setPagination,
    getRowSelected,
    onResetFilter,
    setSortContactList,
  } = props;
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { allSegments, allDatasetSegments } = useAppSelector(getSelector('segment'));
  const { locationData } = useAppSelector(getSelector('location_province'));
  const { listOverdue } = useAppSelector(getSelector('contactList'));
  const [searchParams, setSearchParams] = useSearchParams();
  // const [removePopup, setRemovePopup] = useState<boolean>(false);
  const [selectFilter, setSelectFilter] = useState<TSearchFilter>(defaultFilter);
  const location = useLocation();
  const allQueryParams = Object.fromEntries(searchParams.entries());
  // const sortContact = sortContactList ?? [];

  useEffect(() => {
    if (Object.keys(allQueryParams).length) {
      setSelectFilter({ ...defaultFilter, ...allQueryParams });
    } else {
      setSelectFilter(defaultFilter);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location]);

  const handleSelectDemographic = (value: string[]) => {
    const valueSearch = {
      ...selectFilter,
      gender__in: value
        .filter((val) => genderSelectOptions.some((option) => option.value === val))
        .join(','),
      age__in: value
        .filter((val) => ageSelectOptions.some((option) => option.value === val))
        .join(','),
    };
    handleUpdateFilter(valueSearch);
  };

  const hasFilters =
    Object.values(selectFilter).some((value) => value !== '') ||
    Object.values(allQueryParams).some((value) => value !== '');

  const handleSearch = (key: keyof TSearchFilter, value: string[]) => {
    const valueSearch = {
      ...selectFilter,
      [key]: value.join(','),
    };
    if (key === 'frequency') {
      const [frequency_min, frequency_max] = valueSearch['frequency']?.split(',') ?? [];
      valueSearch['frequency_min'] = frequency_min;
      valueSearch['frequency_max'] = frequency_max;
      delete valueSearch['frequency'];
    }
    handleUpdateFilter(valueSearch);
  };

  const handleUpdateFilter = (value: TSearchFilter) => {
    setSelectFilter(value);
    setSearchParams(filterEmptyParams(value));
  };

  const handleRemoveAllFilter = () => {
    setSelectFilter(defaultFilter);
    setSearchParams(filterEmptyParams(defaultFilter));
  };

  // const queryParams = () => {
  //   return `${
  //     !!Object.keys(allQueryParams).length
  //       ? '?' + new URLSearchParams(allQueryParams).toString() + '&'
  //       : '?'
  //   }${paramsOrderBy(sortContact)}`;
  // };

  // const handleCleanUpContact = async (quantity: number) => {
  //   await update({
  //     endpoint: `${ENDPOINTS.contact_list_clean_up}${queryParams()}`,
  //     params: {
  //       contact_quantity: quantity,
  //     },
  //   }).finally(() => {
  //     setRemovePopup(true);
  //   });
  // };

  const queryFilter = Object.keys(allQueryParams).filter((item) => item !== 'search');

  const handleConvertSegment = (segmentList: TSegmentData) => {
    return segmentList.items
      .map((item: TSegment) => {
        return {
          label: item.name,
          value: item.id,
          color: item.color,
        };
      })
      .concat(emptySegment);
  };

  return (
    <DataTable
      data={data ?? []}
      columns={columns as ColumnDef<TContactItems>[]}
      total={count}
      isSelectCol={false}
      renderSubComponent={(row: TContactItems) =>
        isShowHistory ? (
          <ContactHistory
            contactHistoryId={row.id}
            flatData={row.history ?? []}
            historyData={historyData}
          />
        ) : (
          <></>
        )
      }
      getRowCanExpand={() => !!isShowHistory}
      isToolbar={false}
      isPerPageChange={true}
      classNameHeader="h-[52px] z-20"
      className={cn('max-h-[700px]', className)}
      getRowsSelected={getRowSelected}
      getSortingChange={setSortContactList}
      onResetFilter={onResetFilter}
      loading={isLoading}
      pagination={pagination}
      setPagination={setPagination}
      isShowFilter={true}
      filter={{
        isSearch: true,
        isFilter: true,
        rightNodeFilter: rightNodeFilterContact ?? (
          <>
            {!!queryFilter.length ? (
              <Modal
                className="w-[850px] max-w-none"
                titleAlign={'center'}
                title={t('segment.createSegment')}
                trigger={
                  <Button className="h-[40px] bg-transparent text-primary text-sm font-medium border-[1px] border-border rounded-xl hover:bg-white hover:text-filter">
                    <RiFolderAddLine size={20} />
                    {t('common.button.addSegment')}
                  </Button>
                }
              >
                <ModalSubmitSegment countContact={count} />
              </Modal>
            ) : (
              <CustomToolTips
                element={<CreateSegmentButton isDisable={true} />}
                content={t('common.filterOptionToCreateSegment')}
              />
            )}
            {/*<CleanUpContainer*/}
            {/*  trigger={(setOpen: () => void) => (*/}
            {/*    <Button*/}
            {/*      onClick={setOpen}*/}
            {/*      className="h-[40px] bg-transparent text-primary text-sm font-medium border-[1px] border-border rounded-xl hover:bg-white hover:text-filter"*/}
            {/*    >*/}
            {/*      <RiShieldCheckLine size={20} />*/}
            {/*      {t('common.button.verify')}*/}
            {/*    </Button>*/}
            {/*  )}*/}
            {/*  removePopup={removePopup}*/}
            {/*  setRemovePopup={setRemovePopup}*/}
            {/*  countQuantity={count}*/}
            {/*  handleCleanUp={async (data) => {*/}
            {/*    data.handleCleanupSuccess(() => {*/}
            {/*      handleCleanUpContact(data.quantity);*/}
            {/*    });*/}
            {/*  }}*/}
            {/*/>*/}
          </>
        ),
        filterNode: (
          <div>
            <div className="flex gap-2 mt-6 mb-3">
              <ResponsiveButtonContainer buttonShowMoreSize={110} buttonGap={12}>
                <MultipleSelectColumn
                  key={'demographic'}
                  data={OptionFilterDemoGraphic}
                  icon={<RiIdCardLine size={16} />}
                  title={t('common.demographic')}
                  selected={
                    selectFilter?.gender__in
                      ?.split(',')
                      .filter(Boolean)
                      .concat(selectFilter?.age__in?.split(',').filter(Boolean)) ?? []
                  }
                  onChange={(e) => {
                    handleSelectDemographic(e);
                  }}
                  className="min-w-[185px]"
                />
                <MultipleSelect
                  key={'dob'}
                  icon={<RiCakeLine size={16} />}
                  title={t('common.birthMonth')}
                  isHideSearch={true}
                  options={monthSelectOptions}
                  selected={selectFilter?.dob_month__in?.split(',').filter(Boolean) ?? []}
                  onChange={(e) => handleSearch('dob_month__in', e)}
                  placeholder={t('common.birthMonth')}
                  className="min-w-[170px]"
                />

                <MultipleSelect
                  key={'location'}
                  icon={<RiMapPinLine size={16} />}
                  title={t('common.location')}
                  options={locationData}
                  selected={selectFilter?.person_province__in?.split(',').filter(Boolean) ?? []}
                  onChange={(e) => handleSearch('person_province__in', e)}
                  placeholder={t('common.location')}
                  className="min-w-[150px]"
                />
                <MultipleSelect
                  key={'segment'}
                  icon={<RiFolderLine size={16} />}
                  title={t('common.segment.title')}
                  options={handleConvertSegment(isDataSet ? allDatasetSegments : allSegments)}
                  selected={selectFilter?.segment__in?.split(',').filter(Boolean) ?? []}
                  onChange={(e) => handleSearch('segment__in', e)}
                  className="min-w-[170px]"
                  placeholder={t('common.segment.title')}
                  extraButton={
                    <Link
                      to={`${ROOT_PATH}/${ROOT_ROUTE.contact.segment}`}
                      className="mx-2 mb-3 flex items-center justify-center gap-1 h-[40px] p-3 bg-secondary-foreground_crm font-medium text-primary text-sm rounded-xl hover:bg-secondary-foreground_crm hover:text-secondary"
                    >
                      {t('common.button.viewAllSegment')}
                    </Link>
                  }
                />
                {/*<MultipleSelect*/}
                {/*  key={'status'}*/}
                {/*  icon={<RiNotificationBadgeLine size={16} />}*/}
                {/*  title={t('common.status')}*/}
                {/*  isHideSearch={true}*/}
                {/*  options={statusSelectOptions}*/}
                {/*  selected={selectFilter?.status__in?.split(',').filter(Boolean) ?? []}*/}
                {/*  onChange={(e) => handleSearch('status__in', e)}*/}
                {/*  placeholder={t('common.status')}*/}
                {/*  className="min-w-[140px]"*/}
                {/*/>*/}
                {/*<SelectRange*/}
                {/*  key={'contactFrequency'}*/}
                {/*  data={RangeOptions}*/}
                {/*  icon={<RiContactsBook3Line size={16} />}*/}
                {/*  title={t('common.contactFrequency')}*/}
                {/*  selected={*/}
                {/*    selectFilter.frequency_min || selectFilter.frequency_max*/}
                {/*      ? [selectFilter.frequency_min, selectFilter.frequency_max]*/}
                {/*      : []*/}
                {/*  }*/}
                {/*  onChange={(e) => {*/}
                {/*    handleSearch('frequency', e);*/}
                {/*  }}*/}
                {/*  className="min-w-[190px]"*/}
                {/*/>*/}
              </ResponsiveButtonContainer>
            </div>

            {hasFilters && allQueryParams ? (
              <ResultFound countItems={count} handleRemoveAllFilter={handleRemoveAllFilter}>
                <ListResultTableContact
                  listResult={Object.keys(allQueryParams).filter((item) => item !== 'search')}
                  selectFilter={selectFilter}
                  setSelectFilter={handleUpdateFilter}
                  isAction={true}
                />
              </ResultFound>
            ) : (
              <></>
            )}
          </div>
        ),
        ...filter,
        bottomNodeFilter: isOverdue ? (
          <div className="w-fit h-[22px] px-2 my-2 bg-[#31333d14] flex items-center justify-center rounded-lg gap-0.5">
            <span className="text-primary text-xs font-normal">
              {t('common.button.reminderOverdue')}
            </span>

            <Button
              variant={'icon'}
              className="p-0"
              onClick={() => {
                dispatch(
                  setListOverdue({
                    count: listOverdue.count,
                    items: listOverdue.items,
                    isOverdue: false,
                  }),
                );
              }}
            >
              <RiCloseCircleFill className="ml-1" size={14} color="#0D112666" />
            </Button>
          </div>
        ) : undefined,
      }}
    />
  );
};
export default TableContactList;

type TCreateSegmentButtonProps = {
  isDisable: boolean;
};
const CreateSegmentButton = ({ ...props }: TCreateSegmentButtonProps) => {
  const { isDisable } = props;
  const { t } = useTranslation();
  return (
    <Button
      disabled={isDisable}
      className="h-[40px] bg-transparent text-primary text-sm font-medium border-[1px] border-border rounded-xl hover:bg-white hover:text-filter"
    >
      <RiFolderAddLine size={20} />
      {t('common.button.addSegment')}
    </Button>
  );
};
