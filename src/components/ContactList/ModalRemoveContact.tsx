import React, { useRef } from 'react';
import { TContactItems, TContactListResponse, TContactRestore } from '@/types/contactList';
import { RiDeleteBin6Line, RiErrorWarningLine, RiLoader2Line } from '@remixicon/react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { useMutation } from '@tanstack/react-query';
import { TBaseResponse, TErrorResponse } from '@/types/ResponseApi';
import { update } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { toast } from '@/hooks/use-toast';
import Modal from '@/components/Modal';
import { TPagination } from '@/types/table';
import { DialogClose } from '@/components/ui/dialog';
import { useParams } from 'react-router-dom';

type TModalDeleteContact = {
  listContactRemove?: TContactItems[];
  isContactInSegment?: boolean;
  setListContactRemove?: (value: TContactItems[]) => void;
  handleResetFilter?: () => void;
  pagination: TPagination;
  setPagination: (pagination: TPagination) => void;
  count: number;
  refetchContact: () => void;
};
export const ModalRemoveContact: React.FC<TModalDeleteContact> = ({
  ...props
}: TModalDeleteContact) => {
  const {
    listContactRemove,
    count,
    pagination,
    isContactInSegment = false,
    setListContactRemove,
    handleResetFilter,
    setPagination,
    refetchContact,
  } = props;
  const { t } = useTranslation();
  const { id } = useParams();
  const refClickCloseModal = useRef<HTMLButtonElement>(null);

  const handleRemoveSuccess = () => {
    if (listContactRemove && listContactRemove.length > 0) {
      const countAfter = count - listContactRemove.length;
      const lastPage = Math.ceil(countAfter / pagination.pageSize);
      setPagination({
        ...pagination,
        currentPage:
          pagination.currentPage > lastPage
            ? lastPage === 0
              ? 1
              : lastPage
            : pagination.currentPage,
      });
      if (lastPage >= pagination.currentPage || pagination.currentPage === 1) {
        refetchContact();
      }
    }
    toast({
      status: 'success',
      description: t('contactList.contactMovedToTrash'),
    });
    if (setListContactRemove && handleResetFilter) {
      setListContactRemove([]);
      handleResetFilter();
    }
  };

  const deleteListContactMutation = useMutation({
    mutationFn: async (payload: TContactRestore): Promise<TBaseResponse<TContactListResponse>> => {
      return update({
        endpoint: ENDPOINTS.bulk_delete_contact,
        params: payload,
      }).then((res) => res.data as TBaseResponse<TContactListResponse>);
    },
    onSuccess: handleRemoveSuccess,
    onError: (error: TErrorResponse) => {
      console.log('error', { error });
    },
  });

  const deleteListContactSegmentMutation = useMutation({
    mutationFn: async (payload: TContactRestore): Promise<TBaseResponse<TContactListResponse>> => {
      return update({
        endpoint: ENDPOINTS.segments.delete + id + '/remove-contact/',
        params: payload,
      }).then((res) => res.data as TBaseResponse<TContactListResponse>);
    },
    onSuccess: handleRemoveSuccess,
    onError: (error: TErrorResponse) => {
      console.log('error', { error });
    },
  });

  const handleRemoveContact = () => {
    if (listContactRemove && listContactRemove.length > 0) {
      if (isContactInSegment) {
        deleteListContactSegmentMutation.mutate({
          contact_ids: listContactRemove.map((item) => item.id),
        });
      } else {
        deleteListContactMutation.mutate({
          contact_ids: listContactRemove.map((item) => item.id),
        });
      }
    }
  };

  const loading = deleteListContactMutation.isPending || deleteListContactSegmentMutation.isPending;
  return (
    <Modal
      isCloseIcon={false}
      className="w-fit max-w-none"
      titleAlign={'center'}
      trigger={
        <button className="hover:text-primary">
          <RiDeleteBin6Line size={20} />
        </button>
      }
      title={
        <>
          <div className="m-auto w-fit">
            <RiErrorWarningLine size={80} color={'#F53E3E'} />
          </div>
          <p className="text-xl text-primary font-semibold text-center">
            {t('contactList.titleDeleteContact')}
          </p>
        </>
      }
    >
      <div className="flex flex-col gap-4 w-[529px] justify-center items-center">
        <p className="text-sm text-center text-secondary whitespace-pre-line">
          {t('contactList.deleteContactDescription')}
        </p>
        <div className="flex gap-4 w-full">
          <DialogClose className="flex-1" ref={refClickCloseModal}>
            <div className="flex items-center justify-center h-[40px] font-medium w-full bg-secondary rounded-lg text-sm text-primary hover:bg-secondary-foreground_crm hover:text-filter">
              {t('common.button.cancel')}
            </div>
          </DialogClose>

          <Button
            disabled={loading}
            onClick={handleRemoveContact}
            variant={'delete'}
            className="flex-1 h-[40px] w-full"
          >
            {loading ? (
              <RiLoader2Line className="mx-auto animate-spin" />
            ) : (
              t('common.button.remove')
            )}
          </Button>
        </div>
      </div>
    </Modal>
  );
};
