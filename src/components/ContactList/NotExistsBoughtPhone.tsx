import { RiArrowLeftLine } from '@remixicon/react';
import { t } from 'i18next';
import { Link } from 'react-router-dom';

const NotExistsBoughtPhone = () => {
  return (
    <div className="w-full h-full flex flex-col justify-center items-center gap-2">
      <div className="flex flex-col">
        <h2 className="text-xl font-semibold tracking-[0.6px]">
          {t('common.not_exist_bought_phone')}
        </h2>
        <h2 className="text-sm font-medium text-secondary">{t('common.pls_click_btn_select')}</h2>
      </div>
      <Link
        to={'/contact'}
        className="rounded-lg bg-brand h-[10] p-2 text-white text-sm flex items-center gap-1"
      >
        <RiArrowLeftLine size={16} /> {t('common.back_to_bought_phones')}
      </Link>
    </div>
  );
};

export default NotExistsBoughtPhone;
