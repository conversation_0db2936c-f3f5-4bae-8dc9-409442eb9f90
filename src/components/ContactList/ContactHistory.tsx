import React, { useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { TContactHistoryItems, TContactRestoreTable } from '@/types/contactList';
import { ColumnDef } from '@tanstack/react-table';
import { formatDate, formatTime } from '@/utils/helper';
import DataTable from '@/components/table/DataTable';
import BodyTableInfinite from '@/components/BodyTableInfinite';
import AudioDuration from '@/components/AudioDuration';
import { RiFacebookCircleFill, RiPhoneLine } from '@remixicon/react';
import { HISTORY_CALL_STATUS } from '@/utils/constants';
import { ZaloIcon } from '@/assets/ZaloIcon';

type IContactHistoryProps = {
  contactHistoryId: number;
  flatData: TContactHistoryItems[];
  historyData?: {
    loadingExpand: boolean;
  };
};

type TDescription = {
  content: string;
  type: string;
  status: string;
  url: string;
};

const n = (key: keyof TContactRestoreTable) => key;
const ContactHistory: React.FC<IContactHistoryProps> = (props: IContactHistoryProps) => {
  const { flatData, historyData } = props;
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const { t } = useTranslation();

  const columns: ColumnDef<TContactHistoryItems>[] = useMemo<
    ColumnDef<TContactHistoryItems>[]
  >(() => {
    const viewType = (type: string) => {
      switch (type) {
        case 'outgoing':
          return (
            <p className="flex gap-2 items-center">
              <RiPhoneLine size={16} />
              <span> {t('common.callOut')}</span>
            </p>
          );
        case 'zalo':
          return (
            <p className="flex gap-2 items-center">
              <ZaloIcon size={16} />
              <span>{t('common.zalo')}</span>
            </p>
          );
        case 'facebook':
          return (
            <p className="flex gap-2 items-center">
              <RiFacebookCircleFill size={16} />
              <span> {t('common.facebook')}</span>
            </p>
          );
      }
    };

    const viewStatus = (status: string, url: string) => {
      const statusView = (
        <p className="flex gap-2 items-center text-delete">
          <span>{HISTORY_CALL_STATUS[status as keyof typeof HISTORY_CALL_STATUS]}</span>
        </p>
      );
      switch (status) {
        case 'meet_call':
          return <AudioDuration audioUrl={url} />;
        case 'miss_call':
        case 'stop_at_survey_IVR':
        case 'stop_at_IVR':
          return statusView;
        default:
          return '';
      }
    };

    const handleViewContent = (data: TDescription) => {
      const { content, type, status, url } = data;
      switch (type) {
        case 'outgoing':
          return viewStatus(status, url);
        case 'zalo':
        case 'facebook':
          return t('contactHistory.customAudience') + ': ' + content;
        default:
          return content;
      }
    };

    return [
      {
        id: n('type'),
        accessorKey: n('type'),
        size: 100,
        minSize: 100,
        enableResizing: false,
        header: () => <p className="w-full">{t('common.type')}</p>,
        cell: ({ getValue }) => {
          return (
            <p className="truncate text-sm text-primary-crm">{viewType(getValue<string>())}</p>
          );
        },
      },
      {
        id: n('date'),
        accessorKey: n('date'),
        size: 100,
        minSize: 100,
        enableResizing: false,
        header: () => <p className="w-full">{t('common.date')}</p>,
        cell: ({ row }) => {
          const time = row.original.date_created;
          return <p className="truncate text-sm text-primary-crm">{formatDate(time, '/')}</p>;
        },
      },
      {
        id: n('time'),
        accessorKey: n('time'),
        size: 100,
        minSize: 100,
        enableResizing: false,
        header: () => <p className="w-full">{t('common.time')}</p>,
        cell: ({ row }) => {
          const time = row.original.time_call ?? row.original.date_created;
          return <p className="truncate text-sm text-primary-crm">{formatTime(time, ':')}</p>;
        },
      },
      {
        id: n('description'),
        accessorKey: n('description'),
        size: 300,
        minSize: 300,
        enableResizing: false,
        header: () => <p className="w-full">{t('common.description')}</p>,
        cell: ({ row }) => {
          const url = row.original.recording_url;
          return (
            <p className="max-w-[558px] truncate text-sm text-primary-crm">
              {/*{getValue<string>()} {t('common.min')}*/}
              {handleViewContent({
                content: row.original.content ?? '',
                type: row.original.type,
                status: row.original.status,
                url: url,
              })}
            </p>
          );
        },
      },
    ];
    //   eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="w-[908px] mx-8 my-2 p-4 bg-tertiary rounded-2xl sticky left-8">
      <p className="mb-4 font-medium text-base text-left">{t('contactList.titleHistory')}</p>
      <DataTable
        data={flatData}
        columns={columns}
        total={flatData.length}
        classNameHeader="text-left pt-4 [&_th]:h-[28px] [&_th]:pb-2 [&_th]:pt-0 [&_th]:px-0 [&_th]:bg-tertiary [&_th]:text-tertiary-foreground"
        className="border-none shadow-none [&_thead]:border-b [&_thead]:border-disabled [&_th]:py-4 rounded-none max-h-[180px]"
        containerClassName="border-none shadow-none rounded-none"
        scrollRef={scrollContainerRef}
        body={(table) => {
          return (
            <BodyTableInfinite
              table={table}
              data={flatData}
              columns={columns}
              isLoading={historyData?.loadingExpand}
              className={'min-h-[200px]'}
              scrollContainerRef={scrollContainerRef}
            />
          );
        }}
      />
    </div>
  );
};
export default ContactHistory;
