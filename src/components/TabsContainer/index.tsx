import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import React, { ReactNode } from 'react';
import { cn } from '@/lib/utils';

type TListTabs = {
  title: string;
  icon?: ReactNode;
  child: ReactNode;
  value: string;
};

export type TTabsContainerProps = {
  listTabs: TListTabs[];
  isVertical: boolean;
  className?: string;
  valueTab?: string;
  onChange?: (value: string) => void;
};

export const TabsContainer: React.FC<TTabsContainerProps> = ({ ...props }: TTabsContainerProps) => {
  const { listTabs, isVertical, className, valueTab, onChange } = props;
  const tabClass = isVertical ? 'flex-row' : '';
  const tabListClass = isVertical ? 'flex-col border-l' : 'grid-cols-2 border-b';
  return (
    <Tabs
      defaultValue={listTabs[0].value}
      value={valueTab ?? undefined}
      className={cn('w-full flex flex-col ', tabClass, className)}
      onValueChange={onChange}
    >
      <TabsList className={cn('flex border-tertiary rounded-none p-0 gap-[24px]', tabListClass)}>
        {listTabs.map((tab) => (
          <TabsTrigger
            value={tab.value}
            key={tab.value}
            className={'bg-transparent rounded-none'}
            isVertical={isVertical}
          >
            <div className="flex items-center gap-2 text-base">
              {tab.icon}
              {tab.title}
            </div>
          </TabsTrigger>
        ))}
      </TabsList>
      {listTabs.map((item, index: number) => {
        return (
          <TabsContent value={item.value} key={index}>
            {item.child}
          </TabsContent>
        );
      })}
    </Tabs>
  );
};
