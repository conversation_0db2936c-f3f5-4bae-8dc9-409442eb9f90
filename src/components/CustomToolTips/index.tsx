import React from 'react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { TooltipArrow, TooltipPortal } from '@radix-ui/react-tooltip';
import { cn } from '@/lib/utils';

interface ICustomToolTipsProps {
  content: React.ReactNode;
  element: React.ReactNode;
  isAlwaysOpen?: boolean;
  className?: string;
}

const CustomToolTips = React.forwardRef<HTMLDivElement, ICustomToolTipsProps>(
  ({ content, element, isAlwaysOpen, className }, ref) => {
    return (
      <TooltipProvider delayDuration={100} skipDelayDuration={100}>
        <Tooltip open={isAlwaysOpen}>
          <TooltipTrigger asChild>
            <div ref={ref}>{element}</div>
          </TooltipTrigger>
          <TooltipPortal>
            <TooltipContent sticky="always" className={cn('rounded-xl', className)}>
              {content}
              <TooltipArrow fill="text-primary" className="rounded-xl -mt-[1px]" />
            </TooltipContent>
          </TooltipPortal>
        </Tooltip>
      </TooltipProvider>
    );
  },
);

CustomToolTips.displayName = 'CustomToolTips';

export default CustomToolTips;
