import * as React from 'react';
import { useEffect, useMemo, useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { IOptions } from '@/types/contactList';
import useDebounce from '@/hooks/useDebounce';
import { cn, removeVietnameseTones } from '@/lib/utils';
import { RiArrowDownSLine, RiCloseLine } from '@remixicon/react';
import { useTranslation } from 'react-i18next';
import { TagFillIcon } from '@/assets/TagFillIcon';

type TSelectSearch = {
  options: IOptions[];
  placeholder?: string;
  placeholderInput?: string;
  label?: string;
  className?: string;
  defaultValues?: IOptions[];
  isHideSearch?: boolean;
  isMulti?: boolean;
  onSelect?: (values: IOptions[]) => void;
  disabled?: boolean;
  classNameTrigger?: string;
  onSearch?(value: string): void;
};

export const SelectSearch: React.FC<TSelectSearch> = ({ ...props }: TSelectSearch) => {
  const {
    options,
    placeholder,
    label,
    className,
    defaultValues = [],
    isHideSearch,
    disabled,
    classNameTrigger,
    onSelect,
    onSearch,
  } = props;
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [activeItem, setActiveItem] = useState<string | null>(null);
  const [selectedValues, setSelectedValues] = useState<IOptions[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [searchApi, setSearchApi] = useState<string>('');

  useEffect(() => {
    // Only update if defaultValues actually changed (deep comparison by value)
    const currentValueIds = selectedValues.map(v => v.value).sort().join(',');
    const defaultValueIds = defaultValues.map(v => v.value).sort().join(',');

    if (currentValueIds !== defaultValueIds) {
      const defaultSelected = options.filter((option) =>
        defaultValues.some((def) => def.value === option.value),
      );
      setSelectedValues(defaultSelected);
      // Don't call onSelect here to prevent infinite loops
      // The parent should manage its own state
    }
    //   eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultValues, options]);

  const filteredOptions = useMemo(() => {
    if (!searchQuery) {
      return options;
    }
    return options.filter(
      (option) =>
        option.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
        option.value.toLowerCase().includes(searchQuery.toLowerCase()),
    );
  }, [options, searchQuery]);

  const debouncedSearchQuery: string = useDebounce(searchQuery, 300) ?? '';

  useEffect(() => {
    setSearchApi(debouncedSearchQuery);
  }, [debouncedSearchQuery]);

  useEffect(() => {
    if (onSearch) {
      onSearch(searchApi);
    }
  }, [searchApi, onSearch]);

  const handleSelect = (currentValue: string) => {
    const selectedOption = options.find((opt) => {
      return removeVietnameseTones(opt.value) === removeVietnameseTones(currentValue);
    });
    if (selectedOption) {
      const isSelected = selectedValues.some(
        (item) => removeVietnameseTones(item.value) === removeVietnameseTones(selectedOption.value),
      );
      let newSelectedValues: IOptions[];

      if (isSelected) {
        newSelectedValues = selectedValues.filter(
          (item) =>
            removeVietnameseTones(item.value) !== removeVietnameseTones(selectedOption.value),
        );
      } else {
        newSelectedValues = [...selectedValues, selectedOption];
      }
      setSelectedValues(newSelectedValues);
      if (onSelect) {
        onSelect(newSelectedValues);
      }
    }
  };

  const removeValue = (optionToRemove: IOptions, e: React.MouseEvent) => {
    e.stopPropagation();
    const newSelectedValues = selectedValues.filter((item) => item.value !== optionToRemove.value);
    setSelectedValues(newSelectedValues);
    if (onSelect) {
      onSelect(newSelectedValues);
    }
  };

  return (
    <Popover
      open={open}
      onOpenChange={(open) => {
        setOpen(open);
        if (open) {
          setSearchQuery('');
        }
      }}
    >
      <PopoverTrigger asChild disabled={disabled} className={classNameTrigger}>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            'block w-[200px] justify-between min-h-[40px] h-auto hover:bg-white',
            className,
          )}
        >
          <div className="flex flex-wrap gap-1">
            {!isHideSearch && selectedValues.length > 0 ? (
              selectedValues.map((option) => (
                <div
                  key={option.value}
                  className={cn(
                    'flex items-center gap-1 relative w-fit max-w-full h-[26px] rounded-full border border-border bg-hover-foreground text-hover-filter py-[2px] pl-2 pr-6',
                    option.color && 'bg-secondary-foreground_crm border-0',
                  )}
                >
                  {option.color && (
                    <TagFillIcon color={option.color} />
                  )}
                  <p className={cn('truncate font-normal text-sm mr-1', option.color && 'ml-[5px]')}>{option.label}</p>
                  <div
                    className="absolute flex items-center h-[24px] right-1.5 top-0 p-0 m-0 bg-transparent hover:bg-transparent"
                    onClick={(e) => removeValue(option, e)}
                  >
                    <RiCloseLine color={'#515667'} size={20} />
                  </div>
                </div>
              ))
            ) : (
              <span className="text-sm font-normal text-tertiary">
                {placeholder ?? t('placeHolder.selectSegment')}
              </span>
            )}
            <RiArrowDownSLine size={20} className="ml-auto mt-[2px] opacity-50" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="p-0 w-[200px] border-tertiary rounded-2xl shadow-filter"
        align="start"
      >
        <Command className="rounded-2xl">
          {!isHideSearch && (
            <CommandInput
              placeholder={label ?? t('placeHolder.segment')}
              value={searchQuery}
              onValueChange={setSearchQuery}
            />
          )}
          <CommandList className="rounded-2xl pb-0 mr-[2px] scrollbar-thin scrollbar-thumb-primary scrollbar-track-background">
            <CommandEmpty>{t('common.error.noItemFound')}</CommandEmpty>
            <CommandGroup className="w-[197px] px-2 pt-2 pb-0 mb-2">
              {filteredOptions.map((option) => {
                const isSelected = selectedValues.some((item) => item.value === option.value);
                return (
                  <CommandItem
                    key={option.value}
                    value={searchQuery ? option.label : option.value}
                    onSelect={() => handleSelect(option.value)}
                    onMouseEnter={() => setActiveItem(option.value)}
                    onMouseLeave={() => setActiveItem(null)}
                    className={cn(
                      'cursor-pointer h-[36px] hover:text-hover-filter hover:bg-hover-foreground font-medium rounded-xl data-[selected=true]:bg-hover-foreground data-[selected=true]:text-hover-filter mb-2 p-2.5',
                      isSelected
                        ? 'bg-hover-foreground text-hover-filter font-medium'
                        : 'data-[selected=true]:bg-transparent data-[selected=true]:text-foreground',
                      activeItem === option.value
                        ? 'data-[selected=true]:bg-secondary-foreground_crm'
                        : '',
                    )}
                  >
                    <div className="flex items-center gap-2 w-[177px] pr-1">
                      {option.color && <TagFillIcon color={option.color} />}
                      <p className={cn('truncate', option.color && 'ml-[5px]')}>{option.label}</p>
                    </div>
                  </CommandItem>
                );
              })}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};
