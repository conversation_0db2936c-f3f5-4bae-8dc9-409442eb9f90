import { RiDeleteBin6Line } from 'react-icons/ri';
import { useTranslation } from 'react-i18next';
import React from 'react';
import CollapsibleSection from '@/components/CollapsibleContainer';
import { cn } from '@/lib/utils';
import { useSearchParams } from 'react-router-dom';

type TResultFound = {
  countItems: number;
  children?: React.ReactNode;
  handleRemoveAllFilter: () => void;
};

function ResultFound({ ...props }: TResultFound) {
  const { countItems, children, handleRemoveAllFilter } = props;
  const [searchParams] = useSearchParams();
  const allQueryParams = Object.fromEntries(searchParams.entries());
  const { t } = useTranslation();
  return (
    <>
      <div
        className={cn(
          'flex items-center justify-between flex-wrap mt-4 gap-x-4',
          Object.keys(allQueryParams).length > 0 && 'mb-[10px]',
        )}
      >
        <p className="flex gap-1 text-sm font-normal">
          <span className="font-medium">{Number(countItems).toLocaleString()}</span>
          <span>
            {t('common.resultsFound', {
              symbol: Number(countItems) > 1 ? 's' : '',
            })}
          </span>
        </p>
        <div className="flex gap-4 mr-[56px]">
          <button
            onClick={handleRemoveAllFilter}
            className="flex gap-1 items-center font-normal text-delete"
          >
            <RiDeleteBin6Line />
            {t('common.button.clearAll')}
          </button>
        </div>
      </div>
      <CollapsibleSection buttonClassName={'-mt-[44px]'}>{children}</CollapsibleSection>
    </>
  );
}

export default ResultFound;
