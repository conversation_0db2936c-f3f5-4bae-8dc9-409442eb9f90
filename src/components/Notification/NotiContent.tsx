import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { TNotification } from '@/types/notifications';
import React from 'react';
import { formatFullDateTime } from '@/utils/helper';
import { useMutation } from '@tanstack/react-query';
import { update } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { TErrorResponse } from '@/types/ResponseApi';
import { RiCheckLine } from '@remixicon/react';
import { useNavigate } from 'react-router-dom';
import { ROOT_PATH, ROOT_ROUTE } from '@/constants/router';

interface INotiContent {
  notification: TNotification;
  refetchNoti: () => void;
}

export const NotiContent: React.FC<INotiContent> = ({ ...props }: INotiContent) => {
  const { notification, refetchNoti } = props;
  const { contents, is_read, date_created, id, data } = notification;
  const { contact_id, action } = data;
  const { en: enContent } = contents;

  const navigate = useNavigate();

  const { t } = useTranslation();

  const updateNotification = async () => {
    return update({
      endpoint: ENDPOINTS.notification.read(id.toString()),
      params: {
        is_read: true,
      },
    }).then((res) => res.data);
  };

  const markReadMutation = useMutation({
    mutationFn: async () => updateNotification(),
    onSuccess: () => {
      refetchNoti();
    },
    onError: (error: TErrorResponse) => {
      console.log('error', { error });
    },
  });

  const navigateToContactDetail = () => {
    navigate(`${ROOT_PATH}/${ROOT_ROUTE.contact.detail}/${contact_id}`);
  };

  const handleActionNoti = () => {
    if (!is_read) {
      updateNotification().finally(() => navigateToContactDetail());
    } else {
      navigateToContactDetail();
    }
  };

  const handleViewDetail = () => {
    if (action === 'reminder') {
      return (
        <Button className="h-[32px] min-w-[50px]" variant={'primary'} onClick={handleActionNoti}>
          {t('common.button.viewDetails')}
        </Button>
      );
    }
  };

  const handleViewType = () => {
    if (action === 'clean_up') {
      return 'verify';
    }
    return action;
  };

  return (
    <div className="relative py-3 px-2 border-t border-[#E1E2E3] min-h-[90px] flex flex-col gap-2 w-[336px] group">
      <div className="flex gap-4">
        <div className="w-[292px]">
          <p>{enContent}</p>
        </div>
        {!is_read && <div className="w-3 h-3 bg-brand rounded-full" />}
      </div>
      <div className="flex gap-2 items-center">
        <p className="text-xs text-filter">
          {formatFullDateTime(date_created, {
            date: '/',
            time: ':',
          })}
        </p>
        <div className="w-[4px] h-[4px] bg-filter rounded-full" />
        <p className="text-xs text-filter">{t(`notification.${handleViewType()}`)}</p>
      </div>
      <div className="flex gap-2">
        {handleViewDetail()}
        {!is_read && (
          <Button
            variant={'icon'}
            className="absolute bottom-2.5 right-0 ml-auto p-0 hidden group-hover:block h-auto"
            onClick={() => markReadMutation.mutate()}
          >
            <RiCheckLine color={'#20232C'} size={20} />
          </Button>
        )}
      </div>
    </div>
  );
};
