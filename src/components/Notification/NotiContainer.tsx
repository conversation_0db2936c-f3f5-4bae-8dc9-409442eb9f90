import { TabsContainer } from '@/components/TabsContainer';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Notification } from '@/components/Notification/index';
import { get } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { PAGE_SIZE, QUERY_KEY } from '@/utils/constants';
import { TMultiResponse } from '@/types/ResponseApi';
import { keepPreviousData, useInfiniteQuery } from '@tanstack/react-query';
import { TNotification, TNotificationData } from '@/types/notifications';

export const NotiContainer: React.FC = () => {
  const { t } = useTranslation();
  const [notiType, setNotiType] = useState<'all' | 'unread' | 'read'>('all');
  const [tabValue, setTabValue] = useState<string>('contactList');
  const [typeInApi, setTypeInApi] = useState<string>('verify,reminder');
  const [count, setCount] = useState({
    all: 0,
    unread: 0,
    read: 0,
  });

  const getCountUnread = async () => {
    return await get<TNotification>({
      endpoint: ENDPOINTS.notification.get,
      params: {
        page: 1,
        limit: 1,
        type__in: typeInApi,
        is_read: false,
      },
    }).then((res) => (res.data as TMultiResponse<TNotification>).data || { items: [], count: 0 });
  };

  const fetchNotification = async ({ pageParam }: { pageParam: number }) => {
    let params: { [key: string]: string | number | boolean } = {
      page: pageParam + 1,
      limit: PAGE_SIZE,
      order_by: '-date_created',
      type__in: typeInApi,
    };
    if (notiType !== 'all') {
      params = { ...params, is_read: notiType === 'read' };
    }
    return get<TNotification>({
      endpoint: ENDPOINTS.notification.get,
      params: params,
    }).then((res) => {
      handleCountNoti();
      return (res.data as TMultiResponse<TNotification>).data || { items: [], count: 0 };
    });
  };

  const {
    data: notifications,
    fetchNextPage,
    isFetching,
    isLoading,
    refetch: refetchNoti,
  } = useInfiniteQuery<TNotificationData>({
    queryKey: [QUERY_KEY.NOTIFICATIONS, notiType, tabValue, typeInApi],
    queryFn: async ({ pageParam = 0 }) => {
      return await fetchNotification({
        pageParam: pageParam as number,
      });
    },
    initialPageParam: 0,
    getNextPageParam: (_lastGroup, groups) => groups.length,
    refetchOnMount: 'always',
    placeholderData: keepPreviousData,
  });

  const flatData: TNotification[] = useMemo(
    () => notifications?.pages?.flatMap((page) => page.items) ?? [],
    [notifications],
  );

  const handleCountNoti = () => {
    getCountUnread().then((res) => {
      setCount({
        ...count,
        unread: res?.count || 0,
        all: res?.count || 0,
      });
    });
  };

  const handleRefetchNoti = () => {
    refetchNoti();
  };

  const handleChangeTab = (tab: string) => {
    setNotiType('all');
    if (tab === 'zalo' || tab === 'contactList') {
      const isZalo = tab === 'zalo';
      setTabValue(isZalo ? 'zalo' : 'contactList');
      setTypeInApi(isZalo ? 'zalo' : 'verify,reminder');
    }
  };

  const totalCount = notifications?.pages?.[0]?.count ?? 0;
  const totalFetch = flatData.length;

  const children = useMemo(() => {
    return (
      <Notification
        setNotiType={setNotiType}
        notiType={notiType}
        dataNoti={flatData}
        count={count}
        fetchNextPage={fetchNextPage}
        isFetching={isFetching}
        isLoading={isLoading}
        totalFetch={totalFetch}
        totalCount={totalCount}
        refetchNoti={handleRefetchNoti}
      />
    );
    //   eslint-disable-next-line react-hooks/exhaustive-deps
  }, [notiType, flatData, count, fetchNextPage, isFetching, isLoading, totalFetch, totalCount]);

  return (
    <div className="px-2">
      <TabsContainer
        isVertical={true}
        valueTab={tabValue}
        onChange={handleChangeTab}
        listTabs={[
          {
            title: `${t('common.big360')}`,
            value: 'big360',
            child: children,
          },
          {
            title: `${t('contactList.titlePage')}`,
            value: 'contactList',
            child: children,
          },
          {
            title: `${t('common.facebook')}`,
            value: 'facebook',
            child: children,
          },
          {
            title: `${t('common.zalo')}`,
            value: 'zalo',
            child: children,
          },
        ]}
      />
    </div>
  );
};
