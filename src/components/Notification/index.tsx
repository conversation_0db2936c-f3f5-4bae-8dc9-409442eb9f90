import { useTranslation } from 'react-i18next';
import { But<PERSON> } from '@/components/ui/button';
import React, { useCallback } from 'react';
import { NotiContent } from '@/components/Notification/NotiContent';
import { RiCheckDoubleLine, RiLoader2Line } from '@remixicon/react';
import { TNotification } from '@/types/notifications';
import {
  DefaultError,
  FetchNextPageOptions,
  InfiniteQueryObserverResult,
  useMutation,
} from '@tanstack/react-query';
import { cn } from '@/lib/utils';
import { update } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { TErrorResponse } from '@/types/ResponseApi';
import { NoData } from '@/components/NoData';

type TNotificationProps = {
  count: {
    all: number;
    unread: number;
    read: number;
  };
  totalCount: number;
  totalFetch: number;
  notiType: 'all' | 'unread' | 'read';
  isFetching: boolean;
  isLoading: boolean;
  dataNoti: TNotification[];
  setNotiType: (notiType: 'all' | 'unread' | 'read') => void;
  fetchNextPage: (
    options?: FetchNextPageOptions,
  ) => Promise<InfiniteQueryObserverResult<unknown, DefaultError>>;
  refetchNoti: () => void;
};

export const Notification: React.FC<TNotificationProps> = ({ ...props }: TNotificationProps) => {
  const {
    count,
    dataNoti,
    isFetching,
    notiType,
    isLoading,
    totalCount,
    totalFetch,
    fetchNextPage,
    setNotiType,
    refetchNoti,
  } = props;
  const { t } = useTranslation();

  const fetchMoreOnBottomReached = useCallback(
    (containerRefElement?: HTMLDivElement | null) => {
      if (containerRefElement) {
        const { scrollHeight, scrollTop, clientHeight } = containerRefElement;
        if (
          scrollHeight - scrollTop - clientHeight < 180 &&
          !isFetching &&
          totalFetch < totalCount
        ) {
          fetchNextPage();
        }
      }
    },
    [fetchNextPage, isFetching, totalFetch, totalCount],
  );

  const markAllReadMutation = useMutation({
    mutationFn: async () => {
      return update({
        endpoint: ENDPOINTS.notification.mark_all_read,
      }).then((res) => res.data);
    },
    onSuccess: () => {
      setNotiType('read');
      refetchNoti();
    },
    onError: (error: TErrorResponse) => {
      console.log('error', { error });
    },
  });

  return (
    <div className="border-l border-tertiary pl-2 ml-1 w-[350px]">
      <div className="bg-white">
        <div className="flex items-center pt-[7px] mr-[48px] justify-between">
          <p className="font-medium text-md text-primary-crm">{t('notification.title')}</p>
          {count.unread > 0 && (
            <Button
              variant={'icon'}
              size={'icon'}
              className="h-auto"
              onClick={() => markAllReadMutation.mutate()}
            >
              <RiCheckDoubleLine color={'#20232C'} size={20} />
            </Button>
          )}
        </div>
        <div className="flex gap-2 justify-between items-center h-[52px] text-left mt-3">
          <Button
            onClick={() => {
              setNotiType('all');
            }}
            className={cn(
              'bg-transparent border-none text-filter hover:bg-transparent text-primary rounded-lg',
              notiType === 'all' && 'bg-brand-foreground hover:bg-brand-foreground',
            )}
          >
            {t('common.all')}
            <CountNoti count={count.all} />
          </Button>
          <Button
            onClick={() => {
              setNotiType('unread');
            }}
            className={cn(
              'bg-transparent border-none text-filter hover:bg-transparent text-primary rounded-lg',
              notiType === 'unread' && 'bg-brand-foreground hover:bg-brand-foreground',
            )}
          >
            {t('common.button.unread')}
            <CountNoti count={count.unread} />
          </Button>
          <Button
            onClick={() => {
              setNotiType('read');
            }}
            className={cn(
              'bg-transparent border-none text-filter hover:bg-transparent text-primary rounded-lg',
              notiType === 'read' && 'bg-brand-foreground hover:bg-brand-foreground',
            )}
          >
            {t('common.button.read')}
          </Button>
        </div>
      </div>
      <div
        onScroll={(e) => fetchMoreOnBottomReached(e.target as HTMLDivElement)}
        className="overflow-y-auto overflow-x-hidden max-h-[calc(100vh-128px)]"
      >
        {(isFetching || isLoading) && (
          <div className="flex items-center justify-center p-4">
            <RiLoader2Line className="animate-spin" size={24} />
          </div>
        )}
        {dataNoti.map((item: TNotification) => (
          <NotiContent key={item.id} notification={item} refetchNoti={refetchNoti} />
        ))}
        {dataNoti.length === 0 && <NoData className={'h-[calc(100vh-128px)]'} />}
      </div>
    </div>
  );
};

type TCount = {
  count: number;
};
const CountNoti: React.FC<TCount> = ({ ...props }: TCount) => {
  const { count } = props;
  if (count === 0) {
    return null;
  }
  return (
    <div className="flex justify-center items-center w-[32px] h-[22px] rounded-lg">
      <p className="text-text-primary">{count}</p>
    </div>
  );
};
