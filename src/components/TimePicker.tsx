import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { RiArrowDownSLine, RiTimeLine } from '@remixicon/react';
import { t } from 'i18next';
import { Button } from './ui/button';

export type TimePickerProps = {
  hour: string | null;
  minute: string | null;
};

interface Props {
  value?: TimePickerProps;
  onChange?: (time: TimePickerProps) => void;
  isDisabled?: boolean;
  error?: string | boolean;
  isHideLabel?: boolean;
  className?: string;
  placeholder?: string;
}

type TimePicker = {
  selectedHour: string | null;
  selectedMinute: string | null;
};

const defaultTime: TimePicker = { selectedHour: null, selectedMinute: null };

const TimePickerComponent = ({
  value,
  onChange,
  isDisabled = false,
  error,
  isHideLabel,
  className,
  placeholder,
}: Props) => {
  const [timePicker, setTimePicker] = useState<TimePicker>(defaultTime);
  const [isOpen, setIsOpen] = useState(false);

  const hours = useMemo(
    () => Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, '0')),
    [],
  );

  const minutes = useMemo(
    () => Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0')),
    [],
  );

  useEffect(() => {
    if (value) {
      setTimePicker({ selectedHour: value.hour, selectedMinute: value.minute });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value?.hour, value?.minute]);

  useEffect(() => {
    if (onChange && timePicker.selectedHour && timePicker.selectedMinute) {
      onChange({
        hour: timePicker.selectedHour,
        minute: timePicker.selectedMinute,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [timePicker]);

  const isTop = isOpen || timePicker.selectedHour !== null || timePicker.selectedMinute !== null;

  const label = useMemo(() => {
    if (timePicker.selectedHour || timePicker.selectedMinute) {
      return `${timePicker.selectedHour ?? '__'}:${timePicker.selectedMinute ?? '__'}`;
    }
    return '';
  }, [timePicker.selectedHour, timePicker.selectedMinute]);

  const handleHourChange = useCallback((value: string) => {
    setTimePicker((prev) => ({
      ...prev,
      selectedHour: value,
    }));
  }, []);

  const handleMinuteChange = useCallback((value: string) => {
    setTimePicker((prev) => ({
      ...prev,
      selectedMinute: value,
    }));
  }, []);

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          size="lg"
          variant="outline"
          disabled={isDisabled}
          className={cn(
            'flex relative h-10 items-center rounded-xl border p-2 bg-card',
            isDisabled && 'disabled:cursor-not-allowed',
            error && (typeof error === 'string' ? 'border-red-400' : 'text-[#F53E3E]'),
            className,
          )}
        >
          <div
            className={cn(
              'absolute flex text-sm gap-1 text-tertiary-foreground justify-start left-2 items-center duration-150',
              !isHideLabel && isTop ? '-top-3 bg-card' : 'top-1/2 -translate-y-1/2',
            )}
          >
            <RiTimeLine size={16} />
            {!isHideLabel && t('common.button.time')}
          </div>
          <span className={isHideLabel ? 'ml-5' : ''}>{label}</span>
          {isHideLabel && placeholder && !label && (
            <span className="text-tertiary">{placeholder}</span>
          )}
          <RiArrowDownSLine size={16} className="ml-auto" color="#6B7183" />
          {typeof error === 'string' && (
            <div className="text-[#F53E3E] absolute -bottom-[18px] text-xs left-1">{error}</div>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent align="end" className="w-[144px] py-3 px-2 z-50">
        <div className="h-11 rounded-xl text-secondary font-medium text-sm bg-secondary-foreground_crm p-3">
          {t('common.button.time')}
        </div>
        <div className="grid grid-cols-2 mt-2 gap-1">
          <TimeOptions
            options={hours}
            onChange={handleHourChange}
            value={timePicker.selectedHour || ''}
          />
          <TimeOptions
            options={minutes}
            onChange={handleMinuteChange}
            value={timePicker.selectedMinute || ''}
          />
        </div>
      </PopoverContent>
    </Popover>
  );
};

export const TimePicker = memo(TimePickerComponent);

type TimeOptionsProps = { options: string[]; onChange: (input: string) => void; value: string };

const TimeOptions = ({ ...props }: TimeOptionsProps) => {
  const { value, options, onChange } = props;

  const scrollToPOV = (data: { el: HTMLDivElement | null; option: string }) => {
    const { el, option } = data;
    if (value === option && el) {
      el.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  };

  return (
    <div className="overflow-x-hidden max-h-[280px]">
      {options.map((option) => (
        <div
          key={option}
          className={cn(
            'cursor-pointer p-2 rounded-xl text-sm',
            value === option && 'bg-brand-foreground text-brand',
          )}
          onClick={() => onChange(option)}
          ref={(el) => scrollToPOV({ el, option })}
        >
          {option}
        </div>
      ))}
    </div>
  );
};
