import { ReactNode, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

type TItems = {
  label: string;
  value: string;
  selected: boolean;
};
type Props = {
  options: Omit<TItems, 'selected'>[];
  onChange: (e: string[]) => void;
};

const TransferList = ({ options = [], onChange }: Props) => {
  const { t } = useTranslation();
  const [selected, setSelected] = useState<TItems[]>(
    options.map((item) => ({ ...item, selected: true })),
  );

  const { isSelected, unSelected } = selected.reduce<{
    isSelected: TItems[];
    unSelected: TItems[];
  }>(
    (acc, item) => {
      if (item.selected) {
        acc.isSelected.push(item);
      } else {
        acc.unSelected.push(item);
      }
      return acc;
    },
    { isSelected: [], unSelected: [] },
  );

  useEffect(() => {
    onChange(isSelected.filter((i) => i.selected).map((i) => i.value));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selected]);

  const handleChangeValue = (value: string) => {
    const newSelected = selected.map((i) =>
      i.value === value ? { ...i, selected: !i.selected } : i,
    );
    setSelected(newSelected);
  };

  return (
    <div className="rounded-xl p-3 flex gap-x-3 w-full min-h-7 bg-secondary-foreground_crm">
      <Wrapper title={t('contactList.dataOption')}>
        {unSelected.map((item) => (
          <TransferItem
            key={item.value}
            value={item.label}
            onClick={() => handleChangeValue(item.value)}
          />
        ))}
      </Wrapper>
      <Wrapper title={t('contactList.selectedOption')}>
        {isSelected.map((item) => (
          <TransferItem
            key={item.value}
            value={item.label}
            onClick={() => handleChangeValue(item.value)}
          />
        ))}
      </Wrapper>
    </div>
  );
};
export default TransferList;

const Wrapper = ({ title, children }: { title: string; children: ReactNode }) => {
  return (
    <div className="bg-card p-4 rounded-xl flex flex-col flex-1 gap-4 h-full">
      <div className="font-medium">{title}</div>
      <div className="border py-3 px-4 rounded-lg h-[166px] overflow-x-hidden">
        <div className="flex flex-col gap-1">{children}</div>
      </div>
    </div>
  );
};

const TransferItem = ({ value, onClick }: { value: string; onClick: () => void }) => {
  return (
    <button
      onClick={onClick}
      className="cursor-pointer text-start hover:bg-secondary-foreground_crm rounded-lg p-2"
    >
      {value}
    </button>
  );
};
