import React from 'react';

export const MissedCallIcon: React.FC<{ color?: string; size?: number }> = ({
  color,
  size = 12,
}) => {
  const colorFill = color || '#BF1616';
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={size} height={size} viewBox="0 0 12 12" fill="none">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.23315 9.48782L8.30422 2.41675L9.01133 3.12385L1.94026 10.1949L1.23315 9.48782Z"
        fill={colorFill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.60577 6.52917C5.24432 6.18054 4.93272 5.78056 4.68278 5.34105L5.30195 4.89879C5.50775 4.75177 5.57135 4.4742 5.4501 4.25222C5.06585 3.54882 4.83233 2.77144 4.76815 1.96054C4.74756 1.70052 4.53055 1.5 4.26971 1.5H2.03833C1.78197 1.5 1.56715 1.69387 1.54095 1.94888C1.51371 2.21385 1.5 2.48114 1.5 2.75C1.5 4.92442 2.39549 6.88969 3.83781 8.29713L4.54498 7.58996C3.2836 6.36353 2.5 4.64827 2.5 2.75C2.5 2.6665 2.50151 2.58316 2.50453 2.5H3.82364C3.91814 3.13592 4.10273 3.75257 4.3721 4.33404L3.42213 5.0126C3.52877 5.2871 3.65424 5.5522 3.79697 5.8063L3.8135 5.83535C4.10792 6.3531 4.4741 6.8246 4.89857 7.23637L5.60577 6.52917ZM6.39349 7.15566L5.67321 7.87594C5.83179 7.98679 5.99576 8.09046 6.16465 8.1865L6.1937 8.20305C6.4478 8.34575 6.7129 8.47125 6.9874 8.5779L7.66595 7.6279C8.24745 7.89725 8.8641 8.08185 9.5 8.17635V9.49545C9.41685 9.4985 9.3335 9.5 9.25 9.5C7.78144 9.5 6.42241 9.03101 5.31452 8.23464L4.59919 8.94996C5.89461 9.92324 7.50495 10.5 9.25 10.5C9.51885 10.5 9.78615 10.4863 10.0511 10.4591C10.3061 10.4328 10.5 10.2181 10.5 9.9617V7.7303C10.5 7.46945 10.2995 7.25245 10.0394 7.23185C9.22855 7.16765 8.4512 6.93415 7.7478 6.5499C7.5258 6.42865 7.24825 6.49225 7.1012 6.69805L6.65895 7.3172C6.56877 7.26592 6.48025 7.21204 6.39349 7.15566Z"
        fill={colorFill}
      />
    </svg>
  );
};
