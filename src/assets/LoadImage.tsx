import React, { useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';
import { RiLoader2Line } from '@remixicon/react';

type TLoadImageProps = {
  url: string;
  className?: string;
};

export const LoadImage: React.FC<TLoadImageProps> = ({
  ...props
}: TLoadImageProps): React.ReactNode => {
  const { url, className } = props;
  const REACT_APP_HOST_URL = import.meta.env.REACT_APP_HOST_URL;

  const [isVisible, setIsVisible] = useState(false);
  const imgRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(true);
            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.1 },
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => {
      if (imgRef.current) {
        // eslint-disable-next-line react-hooks/exhaustive-deps
        observer.unobserve(imgRef.current);
      }
    };
  }, []);

  return (
    <div
      ref={imgRef}
      className="rounded-lg relative h-auto w-full flex items-center justify-center"
    >
      {isVisible ? (
        <img
          src={REACT_APP_HOST_URL + url}
          alt={url}
          className={cn('w-full', className)}
          loading="lazy"
        />
      ) : (
        <div className="rounded-lg min-w-[120px] min-h-[120px] h-full flex items-center justify-center bg-secondary-foreground_crm text-primary-crm">
          <RiLoader2Line className="animate-spin" size={24} />
        </div>
      )}
    </div>
  );
};
