import React from 'react';

export const ZaloIcon: React.FC<{ color?: string; size?: number }> = ({ color, size = 14 }) => {
  const colorFill = color || '#8F5CFF';
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={`${size}`} height={`${size}`} viewBox="0 0 14 14" fill="none">
      <path
        d="M9.14975 4.70175H9.70596C9.70294 5.78513 9.70294 6.86893 9.70596 7.95231C9.52459 7.92687 9.22273 8.04284 9.1545 7.80185C9.14328 6.76892 9.15364 5.73512 9.14975 4.70175Z"
        fill={colorFill}
      />
      <path
        d="M3.48225 4.74021C4.3442 4.73935 5.20529 4.73374 6.06681 4.73978C6.06077 4.90834 6.05127 5.092 5.93812 5.22823C5.34909 5.96025 4.77259 6.70175 4.18399 7.43378C4.80973 7.43766 5.43546 7.43507 6.0612 7.43507C6.04954 7.58122 6.10093 7.7515 6.00592 7.87825C5.94633 7.96016 5.83837 7.95283 5.74898 7.95369C4.96605 7.94938 4.18313 7.95757 3.40063 7.94938C3.40279 7.78427 3.40452 7.60148 3.52025 7.46999C4.10237 6.74098 4.6914 6.01715 5.27093 5.28643C4.67542 5.28729 4.07949 5.28255 3.48398 5.28901C3.48009 5.10622 3.48139 4.923 3.48225 4.74021Z"
        fill={colorFill}
      />
      <path
        d="M11.0848 5.45748C11.7049 5.31909 12.3777 5.72563 12.5475 6.33522C12.7604 6.96938 12.3717 7.72338 11.7296 7.91652C11.1841 8.10836 10.5312 7.85789 10.2483 7.35608C10.0264 6.98533 10.013 6.4999 10.2147 6.11837C10.3857 5.78167 10.716 5.53551 11.0848 5.45748ZM11.0745 6.00671C10.6962 6.13992 10.4923 6.59948 10.64 6.97067C10.7622 7.32159 11.1738 7.53628 11.5313 7.42851C11.9394 7.33108 12.186 6.85384 12.0413 6.46282C11.9204 6.07569 11.4519 5.85237 11.0745 6.00671Z"
        fill={colorFill}
      />
      <path
        d="M6.49093 5.90901C6.78933 5.53998 7.31185 5.35805 7.77219 5.48954C7.9242 5.52791 8.06196 5.60379 8.19497 5.6844C8.19367 5.64388 8.19065 5.56326 8.18935 5.52274C8.36338 5.52188 8.53698 5.52231 8.71101 5.52144C8.71015 6.33193 8.70929 7.14241 8.71145 7.95333C8.58362 7.94988 8.45364 7.96497 8.32797 7.9391C8.23988 7.90332 8.21267 7.80718 8.17769 7.72872C7.69749 8.1012 6.94306 8.0167 6.54404 7.56317C6.11868 7.12387 6.09666 6.37547 6.49093 5.90901ZM7.19396 6.01765C6.79408 6.15819 6.60018 6.6725 6.80272 7.04326C6.97545 7.40453 7.4617 7.55628 7.80933 7.3571C8.13192 7.18811 8.2809 6.76864 8.14228 6.43324C8.00798 6.06852 7.55498 5.86418 7.19396 6.01765Z"
        fill={colorFill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.19388 0.338173C3.73181 0.327865 3.26675 0.328136 2.80943 0.400293C2.15865 0.490625 1.53979 0.81072 1.10238 1.30253C0.550498 1.88331 0.342762 2.70227 0.333252 3.48514V10.5123C0.350982 11.0196 0.417487 11.5374 0.639828 12.0005C1.00693 12.8015 1.77558 13.4008 2.64144 13.5684C3.32938 13.6981 4.0337 13.6658 4.7298 13.6555C4.9717 13.668 5.21442 13.6661 5.45721 13.6642C5.61189 13.6628 5.76664 13.6615 5.92125 13.6639C7.30271 13.6634 8.68417 13.6639 10.0656 13.6634C10.5147 13.6691 10.9669 13.6487 11.4078 13.5578C12.1937 13.3905 12.8924 12.868 13.2777 12.1646C13.6089 11.5648 13.6818 10.8617 13.6641 10.1879L13.63 10.225C13.0146 10.9122 12.1872 11.3774 11.3222 11.6782C10.1567 12.0748 8.90828 12.2026 7.68313 12.1093C6.45805 12.004 5.22339 11.6847 4.18437 11.0087C3.65321 11.2445 3.06723 11.36 2.48553 11.3123L2.42378 11.2477C2.73987 10.9105 2.97349 10.4662 2.95494 9.99447C2.95236 9.77474 2.78695 9.61469 2.68763 9.43376C2.09432 8.42193 1.81444 7.25141 1.75486 6.08741C1.69481 4.65783 1.95954 3.18295 2.68593 1.93594C3.04821 1.31284 3.54962 0.77003 4.15414 0.374794L4.19388 0.338173Z"
        fill={colorFill}
      />
    </svg>
  );
};
