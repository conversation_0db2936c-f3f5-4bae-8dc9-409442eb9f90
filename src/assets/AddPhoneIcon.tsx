import React from 'react';

export const AddPhoneIcon: React.FC = () => {
  return (
    <svg width="98" height="97" viewBox="0 0 98 97" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M32.005 38.345C32.68 32.45 38.215 32.015 39.85 26.195C42.58 16.49 32.305 -0.250002 20.2 0.0499984C10.12 0.304998 4.06 12.26 3.31 13.76C1.84 16.76 -1.145 24.245 1.975 37.37C10.675 73.985 53.71 99.23 73.975 96.8C80.935 95.96 86.395 92.42 86.395 92.42C88.78 90.875 97.06 85.505 97.375 77.285C97.735 67.505 86.59 60.29 79.015 58.385C77.14 57.905 74.665 57.305 71.635 57.95C65.23 59.285 64.45 64.415 59.725 65.57C53.8 67.01 47.635 60.755 41.83 54.89C36.235 49.19 31.33 44.24 32.005 38.345Z"
        fill="url(#paint0_linear_57_1956)"
      />
      <mask
        id="mask0_57_1956"
        style={{ maskType: 'luminance' }}
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="98"
        height="97"
      >
        <path
          d="M32.005 38.345C32.68 32.45 38.215 32.015 39.85 26.195C42.58 16.49 32.305 -0.250002 20.2 0.0499984C10.12 0.304998 4.06 12.26 3.31 13.76C1.84 16.76 -1.145 24.245 1.975 37.37C10.675 73.985 53.71 99.23 73.975 96.8C80.935 95.96 86.395 92.42 86.395 92.42C88.78 90.875 97.06 85.505 97.375 77.285C97.735 67.505 86.59 60.29 79.015 58.385C77.14 57.905 74.665 57.305 71.635 57.95C65.23 59.285 64.45 64.415 59.725 65.57C53.8 67.01 47.635 60.755 41.83 54.89C36.235 49.19 31.33 44.24 32.005 38.345Z"
          fill="white"
        />
      </mask>
      <g mask="url(#mask0_57_1956)">
        <path
          d="M1.075 20.81C2.77 35 7.345 45.32 10.855 51.65C25.855 78.83 54.505 93.635 73.18 92.51C76.51 92.315 83.425 91.895 89.47 86.885C94.36 82.82 96.4 77.63 97.255 74.84C98.035 84.785 98.815 94.715 99.595 104.66H40.795C39.13 104.72 11.185 105.17 -3.755 82.7C-15.875 64.46 -14.165 39.065 1.075 20.81Z"
          fill="url(#paint1_linear_57_1956)"
        />
      </g>
      <path
        d="M76.685 32.05C85.5216 32.05 92.685 24.8866 92.685 16.05C92.685 7.21345 85.5216 0.0500031 76.685 0.0500031C67.8484 0.0500031 60.685 7.21345 60.685 16.05C60.685 24.8866 67.8484 32.05 76.685 32.05Z"
        fill="url(#paint2_linear_57_1956)"
      />
      <path
        d="M76.685 9.84571V22.2377"
        stroke="#F1F2F2"
        strokeWidth="3"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M82.8746 16.048H70.4954"
        stroke="#F1F2F2"
        strokeWidth="3"
        strokeMiterlimit="10"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <defs>
        <linearGradient
          id="paint0_linear_57_1956"
          x1="0.605049"
          y1="48.5"
          x2="97.395"
          y2="48.5"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#A585FF" />
          <stop offset="1" stopColor="#5A18BF" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_57_1956"
          x1="-11.7459"
          y1="62.7434"
          x2="99.5755"
          y2="62.7434"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#5A18BF" />
          <stop offset="1" stopColor="#A585FF" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_57_1956"
          x1="88.0011"
          y1="27.358"
          x2="65.3733"
          y2="4.73026"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#E06C14" />
          <stop offset="1" stopColor="#F48E2F" />
        </linearGradient>
      </defs>
    </svg>
  );
};
