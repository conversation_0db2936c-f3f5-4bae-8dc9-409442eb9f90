import styled, { keyframes } from 'styled-components';
const AppLoadIcon = () => {
  return (
    <div className="relative flex w-full h-full justify-center items-center min-h-screen">
      <div className="relative w-20 h-20">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
          <circle fill="none" stroke="#7051F0" strokeWidth="5" cx="50" cy="50" r="40" />
          <circle fill="#924FE8" cx="50" cy="50" r="24" />
          <text
            x="50%"
            y="50%"
            dominantBaseline="middle"
            width="34"
            textAnchor="middle"
            fill="#fff"
          >
            3ig
          </text>
        </svg>
        <Electron xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" id="electron">
          <circle fill="#31F7BA" cx="10" cy="90" r="10" />
        </Electron>
      </div>
    </div>
  );
};
export default AppLoadIcon;
const rotate = keyframes`
  to {
    transform: rotate(360deg);
  }
`;
const Electron = styled.svg`
  animation: ${rotate} 2.5s linear infinite;
  position: absolute;
  top: -50%;
  left: 0;
  transform-origin: bottom center;
`;
