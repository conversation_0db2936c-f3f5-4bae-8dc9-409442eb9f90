import React from 'react';

export const SortIcon: React.FC<{
  colorAsc?: string;
  colorDesc?: string;
  size?: number;
  className?: string;
}> = ({ colorAsc, colorDesc, size = 18, className }) => {
  const asc = colorAsc || '#515667';
  const desc = colorDesc || '#515667';
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 18 21"
      fill="none"
      className={`-ml-1 ${className}`}
    >
      <path
        d="M10.0004 16.8943L6.46484 13.3588L7.64336 12.1803L10.0004 14.5373L12.3574 12.1803L13.5359 13.3588L10.0004 16.8943Z"
        fill={asc}
      />
      <path
        d="M10.0004 5.91787L7.64336 8.27495L6.46484 7.09637L10.0004 3.56087L13.5359 7.09637L12.3574 8.27495L10.0004 5.91787Z"
        fill={desc}
      />
    </svg>
  );
};
