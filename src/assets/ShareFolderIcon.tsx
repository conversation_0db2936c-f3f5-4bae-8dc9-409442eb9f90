import { cn } from '@/lib/utils';

interface IShareFolderIcon {
  isActive: boolean;
}

export const ShareFolderIcon = ({ isActive }: IShareFolderIcon) => {
  return (
    <div className="relative">
      <svg width="18" height="16" viewBox="0 0 18 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M17.2763 8.88408H15.6189L15.6158 3.90501H8.63513L6.95672 2.24862H2.33606V13.8727H9.79878V15.5287H5.23362H1.66846C1.11617 15.5287 0.668457 15.081 0.668457 14.5287V8.0583V1.58789C0.668457 1.03561 1.11617 0.587891 1.66846 0.587891H7.6665L9.33271 2.2541H13.3045H16.2763C16.8285 2.2541 17.2763 2.70182 17.2763 3.2541V5.56909V8.88408Z"
          fill="#20232C"
        />
      </svg>
      <svg
        width="8"
        height="8"
        viewBox="0 0 8 8"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={cn('absolute -bottom-0.5 -right-0.5', isActive && 'animate-bounce-folder-share')}
      >
        <path
          d="M2.95527 3.20249H0.464478L0.464975 4.85646H2.95527V7.78593L7.10659 4.02943L2.95527 0.302734V3.20249Z"
          fill="#20232C"
        />
      </svg>
    </div>
  );
};
