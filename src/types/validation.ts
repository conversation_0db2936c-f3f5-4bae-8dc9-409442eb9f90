import { z } from 'zod';
import i18n from '@/config-translation';
import { regexPhone } from '@/utils/constants';
import { TContactItems } from '@/types/contactList';

const nameContactDetail = (key: keyof TContactItems) => key;
export const formSchemaContactDetail = z.object({
  [nameContactDetail('full_name')]: z.string().min(2, {
    message: i18n.t('common.validate.userNameMinLength', { value: 2 }),
  }).refine((val) => val.trim().length > 2, {
    message: i18n.t('common.validate.userNameMinLength', { value: 2 }),
  }),
  [nameContactDetail('phone_number')]: z
    .string()
    .min(10, {
      message: i18n.t('common.validate.phoneNumberMinLength', { value: 10 }),
    })
    .max(15, {
      message: i18n.t('common.validate.phoneNumberMaxLength', { value: 15 }),
    })
    .regex(regexPhone, {
      message: i18n.t('common.validate.phoneNumberInvalid'),
    }),
  [nameContactDetail('email')]: z
    .string()
    .email({ message: i18n.t('common.validate.emailInvalid') })
    .optional()
    .or(z.literal('')),
  [nameContactDetail('gender')]: z.optional(z.string()),
  [nameContactDetail('dob')]: z
    .union([z.string(), z.date()])
    .optional()
    .transform((e) => (!!e ? e : '')),
  [nameContactDetail('person_address')]: z.optional(z.string()),
  [nameContactDetail('position')]: z.optional(z.string()),
  [nameContactDetail('company_name')]: z.optional(z.string()),
  [nameContactDetail('person_province')]: z.optional(z.string()),
  [nameContactDetail('reminder')]: z
    .union([
      z.object({
        activity: z.string().optional(),
        note: z.string().optional(),
        time_reminder: z
          .union([z.string(), z.date()])
          .optional()
          .transform((e) => (!!e ? e : '')),
        id: z.number().optional(),
      }),
      z.string(),
    ])
    .optional(),
  [nameContactDetail('note')]: z
    .union([
      z.object({
        date_created: z.string().optional(),
        note: z.string().optional(),
        id: z.number().optional(),
      }),
      z.string(),
    ])
    .optional(),
  [nameContactDetail('note_content')]: z.optional(z.string().trim()),
  [nameContactDetail('segment_ids')]: z.union([z.string(), z.array(z.string())]).optional(),
});

export const formSchemaReminder = z.object({
  [nameContactDetail('reminder')]: z
  .union([
    z.object({
      activity: z.string().optional(),
      note: z.string().optional(),
      time_reminder: z
      .union([z.string(), z.date()])
      .optional()
      .transform((e) => (!!e ? e : '')),
      id: z.number().optional(),
    }),
    z.string(),
  ])
  .optional(),
});
