import { IAudienceDetail } from '@/types/audience';

export type IFbCurrentPage = {
  id: string;
  name: string;
  access_token: string;
};

interface TBusiness {
  id: string;
  name: string;
}

export interface IFbPage {
  id: string;
  account_id: string;
  name: string;
  account_status: number;
  is_accepted_term: boolean;
  business: TBusiness | '';
  link_accept_tos: string;
  selected: boolean;
  account_status_name: string;
}

export interface TFilterAudience {
  search: string;
  date_created_from: string;
  date_created_to: string;
  page: number;
  limit: number;
}

export interface TFilterAudienceTiktok extends TFilterAudience {
  platform: 'FB' | 'TT';
}

export interface TFilterCampaign {
  search: string;
  status: '' | 'ACTIVE' | 'PAUSED' | 'DELETED' | 'ARCHIVED';
  date_preset?:
    | ''
    | 'today'
    | 'yesterday'
    | 'last_3d'
    | 'last_7d'
    | 'last_14d'
    | 'last_28d'
    | 'last_30d'
    | 'this_month'
    | 'last_month'
    | 'maximum';
  page: number;
  limit: number;
  since?: string;
  until?: string;
}

export type TFacebookLogResponse = {
  data: TFacebookLogData;
  message: string;
  status: number;
};

export type TFacebookLogData = {
  items: IAudienceDetail[];
  limit: number;
  skip: number;
  total: number;
}
