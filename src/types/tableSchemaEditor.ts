import { ReactNode } from 'react';

export interface TableRow {
  key: string;
  value: string;
}

export interface TActionButton {
  key: string;
  label: string;
  value: string;
  btnType: 'main' | 'sub';
}

export type TSelectOption = {
  value: string;
  label: string;
  type?: 'link' | 'text' | 'image' | 'button' | 'table' | 'singleSelect' | 'multiSelect' | 'number';
};

export type TItemType = 'select' | 'object';

export type TTableSchema = {
  addButton?: string | ReactNode;
  onChange: (data: TableRow[]) => void;
  options?: TSelectOption[];
  itemType?: TItemType;
};

export type TTableSchemaItem = {
  data: TableRow;
  onChangeKey: (key: string) => void;
  onChangeValue: (value: string) => void;
  onDelete: () => void;
  options?: TSelectOption[];
  title?: string;
};
