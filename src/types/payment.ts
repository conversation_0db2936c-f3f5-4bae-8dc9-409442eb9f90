export type PLAN_CODE = 'TRIAL' | 'BASIC' | 'PRO';
export type PAYMENT_TYPE_CREATE_LINK = 'crm_contact';
export type CONTACT_CODE = 'CONTACT_5000' | 'CONTACT_10000' | 'CONTACT_50000';

export type TContactPurchase = {
  contact_code: CONTACT_CODE;
  value: string;
  price: string;
};

export const CONTACT_PURCHASE: TContactPurchase[] = [
  {
    contact_code: 'CONTACT_5000',
    value: Number(5000).toLocaleString(),
    price: '$2.50'
  },
  {
    contact_code: 'CONTACT_10000',
    value: Number(10000).toLocaleString(),
    price: '$4'
  },
  {
    contact_code: 'CONTACT_50000',
    value:  Number(50000).toLocaleString(),
    price: '$15'
  },
];

export interface ICretePaymentRequest {
  plan_code?: PLAN_CODE;
  credit_code?: string;
  contact_code: CONTACT_CODE
  type: PAYMENT_TYPE_CREATE_LINK;
  currency?: string;
  success_url: string;
  cancel_url: string;
}

export interface ICreatePaymentResponse {
  url: string;
}
