import { TSegment } from './segment';

export type Option = {
  value: string;
  label: string;
};

export type TEMPLATE_STATUS = 'ENABLE' | 'PENDING_REVIEW' | 'DELETE' | 'DISABLE' | 'REJECT';
export interface Template {
  type: string;
  template_id: string;
  template_name: string;
  status: TEMPLATE_STATUS;
  template_quality: string | null;
  timeout: number;
  preview_url: string;
  tag: string;
  price: number;
  data: TemplateData;
  id: string;
  date_created: string;
}

export interface TemplateData {
  template_name: string;
  template_type: string;
  tag: string;
  layout: Layout;
  note: string;
  params: Param[];
  tracking_id: string;
}

interface Layout {
  header: {
    components: HeaderComponent[];
  };
  body: {
    components: BodyComponent[];
  };
  footer: {
    components: FooterComponent[];
  };
}

interface HeaderComponent {
  LOGO: {
    light: LogoImage;
    dark: LogoImage;
  };
}

interface LogoImage {
  type: string;
  media_id: string;
}

export type BodyComponent = {
  TITLE?: {
    value: string;
  };
  PARAGRAPH?: {
    value: string;
  };
  TABLE?: {
    rows: TableRow[];
  };
};

interface TableRow {
  value: string;
  title: string;
  row_type?: number;
}

interface FooterComponent {
  BUTTONS: {
    items: ButtonItem[];
  };
}

interface ButtonItem {
  content: string;
  type: number;
  title: string;
}

interface Param {
  sample_value: string;
  name: string;
  type: string | number;
}
export interface TFilterZalo {
  search?: string;
  date_created_from?: string;
  date_created_to?: string;
  page: number;
  limit: number;
  status_in?: string;
}

export interface TCampaignPayload {
  name?: string;
  total_contact?: number;
  expected_amount?: number;
  send_time?: string;
  template_id?: number;
  segment_id?: number;
  data?: TemplateData;
  status?: STATUS_CAMPAIGN;
}

export type STATUS_CAMPAIGN = 'completed' | 'pending' | 'processing';
export interface TCampaignZalo {
  id: number;
  name: string;
  total_contact: number;
  expected_amount: number;
  is_sent: boolean;
  send_time: string;
  template: Template;
  segment: TSegment;
  data: Record<string, unknown>;
  success: number;
  failure: number;
  date_created: string;
  status: STATUS_CAMPAIGN;
}
