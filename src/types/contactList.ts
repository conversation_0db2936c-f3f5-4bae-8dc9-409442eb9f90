/* eslint-disable @typescript-eslint/no-explicit-any */

import { CONTACT_FIELD, CONTACT_STATUS } from '@/constants/contact-list';
import { ColumnDef, SortingState } from '@tanstack/react-table';
import React from 'react';
import { TBaseResponse } from './ResponseApi';
import { TFilter, TPagination } from '@/types/table';

export type Option = {
  value: string;
  label: string;
  type?: string;
  color?: string;
};

export interface IOptionsFilter {
  title: string;
  titleCode: string;
  type: 'RANGE' | 'CHECKBOX' | 'MULTI_COL_CHECKBOX';
  isHideSearch?: boolean;
  options: Option[];
}

export interface IOptions {
  label: string;
  value: string;
  color?: string;
}

export interface IContactFilter {
  isSearch: boolean;
  isFilter: boolean;
  leftNode?: React.ReactNode;
  centerNode?: React.ReactNode;
  rightNode?: React.ReactNode;
  bottomNode?: React.ReactNode;
  filterNode?: React.ReactNode;
  searchPlaceHolder?: string;
}

export interface IResultFilter<T = any> {
  total: number;
  selectFilter: T;
  setSelectFilter: (selectFilter: T) => void;
}

export interface ITableContactList<T = any> {
  columns: ColumnDef<T>[];
  count: number;
  data: TContactItems[] | undefined;
  pagination: TPagination;
  setPagination: (pagination: TPagination) => void;
  isLoading: boolean;
  filter?: TFilter;
  getRowSelected?: (row: TContactItems[]) => void;
  onResetFilter?: (ref: () => void) => void;
  isShowHistory?: boolean;
  rightNodeFilterContact?: React.ReactNode;
  className?: string;
  filterNode?: React.ReactNode;
  sortContactList?: SortingState;
  setSortContactList: (value: SortingState) => void;
  idExpand?: string;
  isOverdue?: boolean;
  isDataSet?: boolean;
  historyData?: {
    loadingExpand: boolean;
  };
}

export interface ISelectFilter {
  demographic: string[];
  dob: string[];
  location: string[];
  segment: string[];
  status: string[];
  contactFrequency: string[];
}

export type TPhoneBought = {
  id: string;
  phone_number: string;
  telecom_provider: string;
  status: CONTACT_STATUS;
  date_created: string;
  date_expired: string;
  is_renewal: boolean;
};

export type TPhoneBoughtResponse = TBaseResponse<{ count: number; items: TPhoneBought[] }>;

export type TTimeReminder = {
  time_reminder: string;
  id: number;
  note: string;
  activity: string;
};

export type TContactListResponse = {
  code: number;
  message: string;
  data: {
    count: number;
    items: TContactItems[];
  };
};

export type TContactCreate = Partial<TContactItems>;

export type TContactDetailResponse = {
  code: number;
  data: {
    data: TContactItems;
  };
  error: string;
  message: string;
};

export type TSegments = {
  segment: {
    id: number;
    name: string;
    color?: string;
  };
};

export type TNoteContact = {
  id: number;
  note: string;
  date_created: string;
};

export type TContactItems = {
  id: number;
  full_name: string;
  email: string;
  phone_number: string;
  dob: string;
  person_address: string;
  person_province: string;
  person_district: string;
  person_ward: string;
  person_region: string;
  person_address_latitude: number;
  person_address_longitude: number;
  position: string;
  position_department: string;
  position_level: string;
  company_name: string;
  company_size: string;
  company_province: string;
  company_region: string;
  company_industry: string;
  company_keywords: string;
  gender: 'male' | 'female' | 'other' | string;
  status: 'confirmed' | 'unconfirm' | 'spam';
  uid: number;
  fb_uid: string;
  segments: TSegments[];
  reminder: TTimeReminder | null;
  date_created: string;
  note?: TNoteContact;
  note_content?: string;
  segment_ids?: string[];
  history?: TContactHistoryItems[];
  datatype?: 'DATASET' | 'AUDIENCE';
};

export interface TUploadContact {
  [CONTACT_FIELD.Name]: string;
  [CONTACT_FIELD.PhoneNumber]: string;
  [CONTACT_FIELD.Gender]: 'male' | 'female' | 'other';
  [CONTACT_FIELD.DOB]: string;
  [CONTACT_FIELD.Email]: string;
  [CONTACT_FIELD.Company]: string;
  [CONTACT_FIELD.Position]: string;
  [CONTACT_FIELD.Address]: string;
}

export type TContactHistoryItems = {
  id: number;
  type: string;
  time_call: string;
  duration: number;
  recording_url: string;
  date_created: string;
  status: string;
  content?: string | undefined;
  time_activity?: string;
};

export interface TContactRestoreTable extends TContactHistoryItems {
  date: string;
  time: string;
  description: string;
}

export type TContactHistory = {
  count: number;
  items: TContactHistoryItems[];
};

export type TContactRestore = {
  contact_ids: number[];
};

export type TSearchFilter = {
  gender__in: string;
  age__in: string;
  status__in: string;
  dob_month__in: string;
  segment__in: string;
  person_province__in: string;
  search: string;
  frequency?: string;
  frequency_min: string;
  frequency_max: string;
};

export type TSegmentDetail = {
  id: number;
  name: string;
  filter: Record<string, unknown>;
  content: string;
  script_content: string;
  color: string;
  date_created: string;
  contact_quantity: number;
  unconfirm_contact_quantity: number;
};
export type TReminderDetail = { date: string; time: string };
export type TErrorReminderDetail = { date: string; time: string; common: string };

export interface ITypeHistoryCall {
  label: string;
  type__in: string | undefined;
}

export type TFilterPhone = {
  search: string;
  telecom_provider__in: string;
  status__in: string[];
  page: number;
  limit: number;
};

export interface TContactLimit {
  id: number;
  limit: number;
  limit_by_sub: number;
  used: number;
  user_id: number;
}

export type TContactLimitResponse = {
  contact_limit: TContactLimit
}
