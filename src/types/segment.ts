import { ColumnDef } from '@tanstack/react-table';
import { TPaginationSearch } from '@/types/table';

export interface TSegment {
  id: string;
  name: string;
  filter: Record<string, unknown>;
  contact_quantity: number;
  unconfirm_contact_quantity: number;
  content: string;
  script_content: string;
  date_created: string;
  color: string;
  datatype: 'DATASET' | 'AUDIENCE';
}

export type TSegmentListResponse = {
  code: number;
  message: string;
  data: TSegmentData;
  error: string;
};
export type TSegmentData = {
  count: number;
  items: TSegment[];
};

export type TSegmentDetailResponse = {
  code: number;
  message: string;
  data: TSegment;
  error: string;
};

export type TSegmentSubmit = Partial<Omit<TSegment, 'unconfirm_contact_quantity'>>;

export interface ITableSegmentList {
  columns: ColumnDef<TSegment>[];
  count: number;
  data: TSegment[] | undefined;
  pagination: TPaginationSearch;
  setPagination: (pagination: TPaginationSearch) => void;
  isLoading: boolean;
}

export const MAX_LENGTH_INPUT = {
  200: '/200',
  500: '/500',
  1000: '/1000',
};

export type TCleaning = {
  quantity: number;
  handleCleanupSuccess: (func: () => void) => void;
};

export interface ISegmentLog {
  crm_segment_id: number;
  date_created: string;
  date_import: string;
  segment_id: number;
  segment_name: string;
  status: number;
  user_id: number;
  type: string;
}

export type TSegmentLogResponse = {
  data: {
    count:number,
    items: ISegmentLog[]
  },
  message: string,
  status: number
}
