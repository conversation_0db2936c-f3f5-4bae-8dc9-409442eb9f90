export interface Preset {
  name: string;
  label: string;
}
export interface PresetButtonProps {
  preset: string;
  label: string;
  isSelected: boolean;
  setPreset: (preset: string) => void;
}

export interface PresetButtonCampaignProps {
  preset: TPreset;
  label: string;
  isSelected: boolean;
  setPreset: (preset: TPreset) => void;
}
export const PRESETS_OPTIONS: Preset[] = [
  { name: 'today', label: 'Today' },
  { name: 'yesterday', label: 'Yesterday' },
  { name: 'last7', label: 'Last 7 days' },
  { name: 'last14', label: 'Last 14 days' },
  { name: 'last30', label: 'Last 30 days' },
  { name: 'thisWeek', label: 'This Week' },
  { name: 'lastWeek', label: 'Last Week' },
  { name: 'thisMonth', label: 'This Month' },
  { name: 'lastMonth', label: 'Last Month' },
];

export const PRESETS_OPTIONS_CAMPAiGN: Preset[] = [
  { name: 'today', label: 'Today' },
  { name: 'yesterday', label: 'Yesterday' },
  { name: 'last_3d', label: 'Last 3 days' },
  { name: 'last_7d', label: 'Last 7 days' },
  { name: 'last_14d', label: 'Last 14 days' },
  { name: 'last_28d', label: 'Last 28 days' },
  { name: 'last_30d', label: 'Last 30 days' },
  { name: 'this_month', label: 'This Month' },
  { name: 'last_month', label: 'Last Month' },
  { name: 'maximum', label: 'Maximum' },
];


export interface DateRange {
  from: Date | string;
  to: Date | string;
}

export interface DateRangePickerProps {
  initialDateFrom?: Date | string;
  initialDateTo?: Date | string;
  align?: 'start' | 'center' | 'end';
  locale?: string;
  className?: string;
  disableRemove?: boolean;
  onUpdate?: (values: { range: DateRange }) => void;
  onChange: (value: DateRange) => void;
  onClear?: () => void;
  placeholder?: string;
}

export interface DateRangePickerCampaignProps {
  initialDateFrom?: Date | string;
  initialDateTo?: Date | string;
  align?: 'start' | 'center' | 'end';
  locale?: string;
  className?: string;
  disableRemove?: boolean;
  onUpdate?: (values: { range: DateRange }) => void;
  onChange: (value: DateRange) => void;
  onSelectPreset: (value: TPreset) => void;
  onClear?: () => void;
  placeholder?: string;
}

export type TPreset = '' | 'today' | 'yesterday' | 'last_3d' | 'last_7d' | 'last_14d' | 'last_28d' | 'last_30d' | 'this_month' | 'last_month' | 'maximum' | undefined;
