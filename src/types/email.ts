import { FILTER_KEY } from '@/constants/email';

export interface TFilterEmail {
  [FILTER_KEY.STATUS]: string;
  [FILTER_KEY.DATE_FROM]: string;
  [FILTER_KEY.DATE_TO]: string;
  [FILTER_KEY.PAGE]: number;
  [FILTER_KEY.LIMIT]: number;
  [FILTER_KEY.SEARCH]: string;
}

export interface TBlockDesign {
  id: string;
  type: string;
  label: string;
  icon: React.ReactNode;
}
export type TTypeBlock =
  | 'title'
  | 'text'
  | 'image'
  | 'video'
  | 'button'
  | 'html'
  | 'social'
  | 'navigation'
  | 'divider'
  | 'spacer'
  | 'link';

export interface TEmailInfo {
  id: string;
  email: string;
  brevo_email_id: string;
  is_primary: boolean;
  is_verified: boolean;
  verified_at: string; // ISO date string format
  name: 'string';
}
