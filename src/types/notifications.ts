export type TNotificationResponse = {
  code: number;
  message: string;
  data: TNotificationData;
  error: string;
};

export type TNotiContent = {
  en: string;
};

export type TNotification = {
  id: number;
  headings: TNotiContent;
  contents: TNotiContent;
  is_read: boolean;
  date_created: string;
  data: TDataNotification;
};

type TDataNotification = {
  action: 'reminder' | 'clean_up' | 'zalo';
  contact_id?: number;
  reminder_id?: number;
};

export type TNotificationData = {
  count: number;
  items: TNotification[];
};
