@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url('https://rsms.me/inter/font-files/InterVariable.woff2?v=4.1') format('woff2');
}

@font-face {
  font-family: 'Inter';
  font-style: italic;
  font-weight: 100 900;
  font-display: swap;
  src: url('https://rsms.me/inter/font-files/InterVariable-Italic.woff2?v=4.1') format('woff2');
}

body {
  box-sizing: border-box;
  height: 100%;
  font-family: 'Inter', sans-serif;
  font-feature-settings: "ss04", "cv05", "cv06", "cv10", "cv12", "cv13" off;
  font-weight: 400;
}

#crm-root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0;
  text-align: center;
  /*height: 100vh;*/
}
.ck-editor__editable {
  min-height: 200px;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* ck5 editor css */

.ck-balloon-panel {
  display: none !important;
}
.ck.ck-editor__editable_inline {
  padding: 0 !important;
}
.ck.ck-editor__editable_inline > *:last-child {
  margin-bottom: 0 !important;
}
.ck.ck-editor__editable_inline > *:first-child {
  margin-top: 0 !important;
}
.ck.ck-editor__editable.ck-focused:not(.ck-editor__nested-editable) {
  border: none !important;
  box-shadow: none !important;
}
.ck.ck-toolbar {
  border: none !important;
  box-shadow: none !important;
  background: inherit !important;
  border-radius: 10px !important;
}
.ck.ck-toolbar .ck-dropdown__panel {
  max-height: 200px !important;
  overflow-x: hidden !important;
  border-radius: 6px !important;
}
/* ---------------------------------- */
@keyframes bounce-right-to-left {
  0%,
  100% {
    transform: translateX(25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: translateX(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

.animate-bounce-folder-share {
  animation: bounce-right-to-left 1s infinite;
}
