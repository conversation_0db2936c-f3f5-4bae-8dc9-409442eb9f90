import { SuspenseWrapper } from '@/components/SuspenseWrap';
import { TIKTOK_ADS_ROUTER } from '@/constants/router';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import { TiktokAuthProvider } from '@/pages/TiktokAds/context/TiktokAuthContext';
import { AccessDenied } from '@/pages/TiktokAds/components/AccessDenied';
import Breadcrumb from '@/components/Breadcrumb';
import { TitleTiktok } from '@/pages/TiktokAds/components/TitleTiktok';
import { AuthorizeFailed } from '@/pages/TiktokAds/components/AuthorizeFailed';

const TikTokPage = lazy(() => import('@/pages/TiktokAds'));

const useTiktokAdsRouter = (): RouteObject[] => [
  {
    path: TIKTOK_ADS_ROUTER[''],
    element: (
      <SuspenseWrapper
        component={
          <TiktokAuthProvider>
            <TikTokPage />
          </TiktokAuthProvider>
        }
      />
    ),
  },
  {
    path: TIKTOK_ADS_ROUTER.PERMISSION_FAILED,
    element: (
      <SuspenseWrapper
        component={
          <TiktokAuthProvider>
            <Breadcrumb />
            <TitleTiktok />
            <AccessDenied />
          </TiktokAuthProvider>
        }
      />
    ),
  },
  {
    path: TIKTOK_ADS_ROUTER.AUTHORIZE_FAILED,
    element: (
      <SuspenseWrapper
        component={
          <TiktokAuthProvider>
            <Breadcrumb />
            <TitleTiktok />
            <AuthorizeFailed />
          </TiktokAuthProvider>
        }
      />
    ),
  },
];

export default useTiktokAdsRouter;
