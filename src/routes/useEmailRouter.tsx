import { SuspenseWrapper } from '@/components/SuspenseWrap';
import { EMAIL_ROUTER, EMAIL_ROUTER_CONFIG } from '@/constants/router';
import { lazy } from 'react';
import { Navigate, RouteObject } from 'react-router-dom';

const useEmailRouter = (): RouteObject[] => [
  {
    path: EMAIL_ROUTER.email,
    element: <SuspenseWrapper component={<Layout />} />,
    children: [
      {
        path: '',
        element: <SuspenseWrapper component={<EmailPage />} />,
      },
      {
        path: EMAIL_ROUTER.email_campaign,
        element: <SuspenseWrapper component={<EmailCampaignPage />} />,
      },
      {
        path: EMAIL_ROUTER.design,
        element: <SuspenseWrapper component={<DesignCampaignPage />} />,
      },
      {
        path: EMAIL_ROUTER.create_sender,
        element: <SuspenseWrapper component={<CreateSenderPage />} />,
      },
      {
        path: EMAIL_ROUTER.sender + '/:id',
        element: <SuspenseWrapper component={<UpdateSenderPage />} />,
      },
      {
        path: EMAIL_ROUTER.verifySender + '/:id',
        element: <SuspenseWrapper component={<VerifySenderPage />} />,
      },
      {
        path: EMAIL_ROUTER.multiple_config,
        element: <SuspenseWrapper component={<MultipleConfigPage />} />,
        children: [
          {
            path: '',
            element: <SuspenseWrapper component={<Navigate to={EMAIL_ROUTER_CONFIG.SENDER} />} />,
          },
          {
            path: EMAIL_ROUTER_CONFIG.SENDER,
            element: <SuspenseWrapper component={<SenderConfigPage />} />,
          },
          {
            path: EMAIL_ROUTER_CONFIG.DOMAINS,
            element: <SuspenseWrapper component={<DomainsConfigPage />} />,
          },
          {
            path: EMAIL_ROUTER_CONFIG.DEDICATED_IPS,
            element: <SuspenseWrapper component={<DedicatedIpsConfigPage />} />,
          },
        ],
      },
    ],
  },
];
export default useEmailRouter;

const Layout = lazy(() => import('@/pages/Email'));
const EmailPage = lazy(() => import('@/pages/Email/views/GetList'));
const EmailCampaignPage = lazy(() => import('@/pages/Email/views/Campaign'));
const DesignCampaignPage = lazy(() => import('@/pages/Email/views/Design'));
const CreateSenderPage = lazy(() => import('@/pages/Email/views/CreateSender'));
const UpdateSenderPage = lazy(() => import('@/pages/Email/views/UpdateSender'));
const MultipleConfigPage = lazy(() => import('@/pages/Email/views/MultipleConfig'));
const VerifySenderPage = lazy(() => import('@/pages/Email/views/VerifySender'));
const SenderConfigPage = lazy(() => import('@/pages/Email/components/MultipleConfig/views/Sender'));
const DomainsConfigPage = lazy(
  () => import('@/pages/Email/components/MultipleConfig/views/Domains'),
);
const DedicatedIpsConfigPage = lazy(
  () => import('@/pages/Email/components/MultipleConfig/views/DedicatedIPs'),
);
