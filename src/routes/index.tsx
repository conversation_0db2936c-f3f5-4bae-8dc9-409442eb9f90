import { Navigate, Outlet, useRoutes } from 'react-router-dom';
import useContactRouter from './useContactRouter';
import { ROOT_PATH, ROOT_ROUTE } from '@/constants/router';
import useFacebookRouter from './useFacebookRouter';
import useZaloRouter from './useZaloRouter';
import usePushNotice from '@/hooks/usePushNotice';
import NotFound from '@/pages/NotFound';
import useTiktokAdsRouter from './useTiktokAdsRouter';
// import useEmailRouter from './useEmailRouter';

const AppRouter = () => {
  usePushNotice();

  const contactRoutes = useContactRouter();
  const facebookRoutes = useFacebookRouter();
  const tiktokAdsRoutes = useTiktokAdsRouter();
  const zaloRoutes = useZaloRouter();
  // const emailRoutes = useEmailRouter();

  return useRoutes([
    {
      path: '/',
      element: <Outlet />,
      children: [
        {
          path: '/',
          element: <Navigate to={`${ROOT_PATH}/${ROOT_ROUTE.contact['']}`} />,
        },
        ...contactRoutes,
        ...facebookRoutes,
        ...tiktokAdsRoutes,
        ...zaloRoutes,
        // ...emailRoutes,
      ],
    },
    {
      path: '*',
      element: <NotFound />,
    },
  ]);
};

export default AppRouter;
