import { SuspenseWrapper } from '@/components/SuspenseWrap';
import { FACEBOOK_ROUTER } from '@/constants/router';
import { FbAuthProvider } from '@/pages/Facebook/context/FbAuthContext';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const FacebookPage = lazy(() => import('@/pages/Facebook'));

const useFacebookRouter = (): RouteObject[] => [
  {
    path: FACEBOOK_ROUTER[''],
    element: (
      <SuspenseWrapper
        component={
          <FbAuthProvider>
            <FacebookPage />
          </FbAuthProvider>
        }
      />
    ),
  },
];

export default useFacebookRouter;
