import { SuspenseWrapper } from '@/components/SuspenseWrap';
import { ROOT_PATH, ROOT_ROUTE, ZALO_ROUTER } from '@/constants/router';
import { OauthProvider } from '@/pages/Zalo/Context/OauthContext';
import TemplateDetail from '@/pages/Zalo/Views/TemplateDetail';
import { TemplateContextProvider } from '@/pages/Zalo/Context/TemplateContext';
import { lazy } from 'react';
import { Navigate, RouteObject } from 'react-router-dom';

const ZaloPage = lazy(() => import('@/pages/Zalo'));
const ZNSPage = lazy(() => import('@/pages/Zalo/Views/ZNSPage'));
const CreateTemplateZNS = lazy(() => import('@/pages/Zalo/Views/CreateTemplateZNS'));
const CampaignPage = lazy(() => import('@/pages/Zalo/Views/Campaign'));
const HowToUsePage = lazy(() => import('@/pages/Zalo/Views/HowToUse'));

const useZaloRouter = (): RouteObject[] => [
  {
    path: ZALO_ROUTER.howToConnect,
    element: <SuspenseWrapper component={<HowToUsePage />} />,
  },
  {
    path: ZALO_ROUTER.zalo,
    element: (
      <SuspenseWrapper
        component={
          <OauthProvider>
            <ZaloPage />
          </OauthProvider>
        }
      />
    ),
    children: [
      {
        path: '',
        element: <Navigate to={`${ROOT_PATH}/${ROOT_ROUTE.zalo.zns}`} />,
      },
      {
        path: ZALO_ROUTER.zns,
        // element: <SuspenseWrapper component={<ZNSPage />} />,
        children: [
          {
            path: '',
            element: <SuspenseWrapper component={<ZNSPage />} />,
          },
          {
            path: ZALO_ROUTER.createTemplate,
            element: <SuspenseWrapper component={<CreateTemplateZNS />} />,
          },
          {
            path: ':id',
            element: <SuspenseWrapper component={<TemplateDetail />} />,
          },
        ],
      },
      {
        path: ZALO_ROUTER.campaign,
        element: (
          <SuspenseWrapper
            component={
              <TemplateContextProvider>
                <CampaignPage />
              </TemplateContextProvider>
            }
          />
        ),
      },
    ],
  },
];
export default useZaloRouter;
