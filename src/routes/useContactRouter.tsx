import { SuspenseWrapper } from '@/components/SuspenseWrap';
import { CONTACT_ROUTER, GET_PHONE_ROUTER } from '@/constants/router';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const GetPhonePage = lazy(() => import('@/pages/ContactList/GetPhone'));
const ContactListPage = lazy(() => import('@/pages/ContactListContainer'));
const SegmentList = lazy(() => import('@/pages/Segment'));
const SegmentDetail = lazy(() => import('@/pages/Segment/DetailSegment'));
const ContactDetail = lazy(() => import('@/pages/Detail'));

const useContactRouter = (): RouteObject[] => [
  {
    path: CONTACT_ROUTER.CONTACT,
    element: <SuspenseWrapper component={<ContactListPage />} />,
  },
  {
    path: GET_PHONE_ROUTER.GET_PHONE_NUMBER,
    element: <SuspenseWrapper component={<GetPhonePage />} />,
  },
  {
    path: CONTACT_ROUTER.SEGMENT,
    element: <SuspenseWrapper component={<SegmentList />} />,
  },
  {
    path: `${CONTACT_ROUTER.SEGMENT_DETAIL}/:id`,
    element: <SuspenseWrapper component={<SegmentDetail />} />,
  },
  {
    path: `${CONTACT_ROUTER.CONTACT_DETAIL}/:id`,
    element: <SuspenseWrapper component={<ContactDetail />} />,
  },
];

export default useContactRouter;
