import { createSlice } from '@reduxjs/toolkit';
import { TSegment, TSegmentData } from '@/types/segment';
import {
  createSegment,
  deleteSegment,
  getAllDatasetSegment,
  getAllAudienceSegment,
  getSegment,
  updateSegment,
  getDatasetSegment
} from '@/store/segment/action';

export type TInitialSegmentsState = {
  segments: TSegmentData;
  datasetSegments: TSegmentData;
  allSegments: TSegmentData;
  allDatasetSegments: TSegmentData;
  loadingSegmentsDataset: boolean;
  loadingSegmentsAudience: boolean;
  segmentCreated: TSegment | undefined;
};

const initialStateValues: TInitialSegmentsState = {
  loadingSegmentsDataset: false,
  loadingSegmentsAudience: false,
  segments: {
    items: [],
    count: 0
  },
  datasetSegments: {
    items: [],
    count: 0
  },
  allSegments: {
    items: [],
    count: 0
  },
  allDatasetSegments: {
    items: [],
    count: 0
  },
  segmentCreated: undefined
};

export const segmentsSlice = createSlice({
  name: 'segment',
  initialState: initialStateValues,
  reducers: {
    removeSegment: (state) => {
      state.segmentCreated = undefined;
    },
    removeItem: (state, action) => {
      state.allSegments.items = state.allSegments.items.filter(
        (item) => item.id !== action.payload
      );
    },
  },
  extraReducers: (builder) => {
    // all segment audience
    builder.addCase(getAllAudienceSegment.pending, (state) => {
      state.loadingSegmentsAudience = true;
    });
    builder.addCase(getAllAudienceSegment.fulfilled, (state, action) => {
      state.allSegments = action.payload;
      state.segments = {
        count: action.payload.count,
        items: action.payload.items.slice(0, 10)
      };
      state.loadingSegmentsAudience = false;
    });
    builder.addCase(getAllAudienceSegment.rejected, (state) => {
      state.loadingSegmentsAudience = false;
    });

    // all segment dataset
    builder.addCase(getAllDatasetSegment.pending, (state) => {
      state.loadingSegmentsDataset = true;
    });
    builder.addCase(getAllDatasetSegment.fulfilled, (state, action) => {
      state.allDatasetSegments = action.payload;
      state.datasetSegments = {
        count: action.payload.count,
        items: action.payload.items.slice(0, 10)
      };
      state.loadingSegmentsDataset = false;
    });
    builder.addCase(getAllDatasetSegment.rejected, (state) => {
      state.loadingSegmentsDataset = false;
    });

    // get list segment audience
    builder.addCase(getSegment.pending, (state) => {
      state.loadingSegmentsAudience = true;
    });
    builder.addCase(getSegment.fulfilled, (state, action) => {
      state.loadingSegmentsAudience = false;
      state.segments = action.payload ?? { count: 0, items: [] };
    });
    builder.addCase(getSegment.rejected, (state) => {
      state.loadingSegmentsAudience = false;
    });

    // get list segment dataset
    builder.addCase(getDatasetSegment.pending, (state) => {
      state.loadingSegmentsDataset = true;
    });
    builder.addCase(getDatasetSegment.fulfilled, (state, action) => {
      state.loadingSegmentsDataset = false;
      state.datasetSegments = action.payload ?? { count: 0, items: [] };
    });
    builder.addCase(getDatasetSegment.rejected, (state) => {
      state.loadingSegmentsDataset = false;
    });

    // create
    builder.addCase(createSegment.pending, (state) => {
      state.loadingSegmentsDataset = true;
      state.loadingSegmentsAudience = true;
    });
    builder.addCase(createSegment.fulfilled, (state, action) => {
      state.loadingSegmentsDataset = false;
      state.loadingSegmentsAudience = false;
      state.segments = {
        count: state.segments.count + 1,
        items: [action.payload, ...state.segments.items]
      };
      state.allSegments = {
        count: state.allSegments.count + 1,
        items: [action.payload, ...state.allSegments.items]
      };
      state.segmentCreated = action.payload;
    });
    builder.addCase(createSegment.rejected, (state) => {
      state.loadingSegmentsDataset = false;
      state.loadingSegmentsAudience = false;
    });

    // update
    builder.addCase(updateSegment.pending, (state) => {
      state.loadingSegmentsDataset = true;
      state.loadingSegmentsAudience = true;
    });
    builder.addCase(updateSegment.fulfilled, (state, action) => {
      const updateItems = (items: TSegment[], payload: TSegment) => {
        return items.map((item) => ( item.id === payload.id ? payload : item ));
      };

      if (action.payload.datatype === 'DATASET') {
        state.datasetSegments = {
          count: state.segments.count,
          items: updateItems(state.segments.items, action.payload)
        };
        state.allDatasetSegments = {
          count: state.allSegments.count,
          items: updateItems(state.allSegments.items, action.payload)
        };
      } else {
        state.segments = {
          count: state.segments.count,
          items: updateItems(state.segments.items, action.payload)
        };
        state.allSegments = {
          count: state.allSegments.count,
          items: updateItems(state.allSegments.items, action.payload)
        };
      }

      state.loadingSegmentsDataset = false;
      state.loadingSegmentsAudience = false;
    });
    builder.addCase(updateSegment.rejected, (state) => {
      state.loadingSegmentsDataset = false;
      state.loadingSegmentsAudience = false;
    });

    //   delete
    builder.addCase(deleteSegment.pending, (state) => {
      state.loadingSegmentsDataset = true;
      state.loadingSegmentsAudience = true;
    });
    builder.addCase(deleteSegment.fulfilled, (state) => {
      state.loadingSegmentsDataset = false;
      state.loadingSegmentsAudience = false;
    });
    builder.addCase(deleteSegment.rejected, (state) => {
      state.loadingSegmentsDataset = false;
      state.loadingSegmentsAudience = false;
    });
  }
});

export const { removeSegment, removeItem } = segmentsSlice.actions;

export default segmentsSlice.reducer;
