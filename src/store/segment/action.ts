import { createAsyncThunk } from '@reduxjs/toolkit';
import { TSegment, TSegmentData, TSegmentDetailResponse, TSegmentSubmit } from '@/types/segment';
import { create, get, remove, update } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { MAX_LIMIT } from '@/utils/constants';
import { TPaginationSearch } from '@/types/table';
import { TMultiResponse } from '@/types/ResponseApi';

export const getAllAudienceSegment = createAsyncThunk(
  'getAllAudienceSegment/get',
  async (_, { rejectWithValue }) => {
    try {
      let hasMore = true;
      let currentPage = 1;
      let allSegmentData: TSegmentData = { count: 0, items: [] };
      while (hasMore) {
        const res = await get<TSegment>({
          endpoint: ENDPOINTS.segments.getList,
          params: {
            page: currentPage,
            limit: MAX_LIMIT,
            datatype: 'AUDIENCE',
            order_by: '-date_created',
          },
        });

        const resData = (res.data as TMultiResponse<TSegment>).data;
        allSegmentData = {
          count: resData?.count ?? 0,
          items: [...allSegmentData.items, ...(resData?.items ?? ([] as TSegment[]))],
        };
        if (
          (resData?.items?.length && resData?.items?.length < MAX_LIMIT) ||
          resData?.items?.length === 0
        ) {
          hasMore = false;
        } else {
          currentPage++;
        }
      }
      return allSegmentData;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const getAllDatasetSegment = createAsyncThunk(
  'getAllDatasetSegment/get',
  async (_, { rejectWithValue }) => {
    try {
      let hasMore = true;
      let currentPage = 1;
      let allSegmentData: TSegmentData = { count: 0, items: [] };
      while (hasMore) {
        const res = await get<TSegment>({
          endpoint: ENDPOINTS.segments.getList,
          params: {
            page: currentPage,
            limit: MAX_LIMIT,
            datatype: 'DATASET',
            order_by: '-date_created',
          },
        });

        const resData = (res.data as TMultiResponse<TSegment>).data;
        allSegmentData = {
          count: resData?.count ?? 0,
          items: [...allSegmentData.items, ...(resData?.items ?? ([] as TSegment[]))],
        };
        if (
          (resData?.items?.length && resData?.items?.length < MAX_LIMIT) ||
          resData?.items?.length === 0
        ) {
          hasMore = false;
        } else {
          currentPage++;
        }
      }
      return allSegmentData;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const getSegment = createAsyncThunk(
  'getSegment/get',
  async (payload: TPaginationSearch, { rejectWithValue }) => {
    try {
      const res = await get<TSegment>({
        endpoint: ENDPOINTS.segments.getList,
        params: {
          page: payload.currentPage,
          limit: payload.pageSize,
          search: payload.search,
          order_by: '-date_created',
          datatype: 'AUDIENCE',
        },
      });
      return (res.data as TMultiResponse<TSegment>).data;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const getDatasetSegment = createAsyncThunk(
  'getDatasetSegment/get',
  async (payload: TPaginationSearch, { rejectWithValue }) => {
    try {
      const res = await get<TSegment>({
        endpoint: ENDPOINTS.segments.getList,
        params: {
          page: payload.currentPage,
          limit: payload.pageSize,
          search: payload.search,
          order_by: '-date_created',
          datatype: 'DATASET',
        },
      });
      return (res.data as TMultiResponse<TSegment>).data;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const createSegment = createAsyncThunk(
  'create/post',
  async (payload: TSegmentSubmit, { rejectWithValue }) => {
    try {
      const res = await create<TSegmentDetailResponse>({
        endpoint: ENDPOINTS.segments.getList,
        params: payload,
      });
      return (res.data as TSegmentDetailResponse).data;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const updateSegment = createAsyncThunk(
  'updateSegment/post',
  async (payload: TSegmentSubmit, { rejectWithValue }) => {
    try {
      const res = await update<TSegmentDetailResponse>({
        endpoint: ENDPOINTS.segments.update + payload.id + '/',
        params: payload,
      });
      return (res.data as TSegmentDetailResponse).data;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const deleteSegment = createAsyncThunk(
  'deleteSegment/delete',
  async (id: string, { rejectWithValue }) => {
    try {
      return await remove({
        endpoint: ENDPOINTS.segments.update + id + '/',
      });
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);
