import { createSlice } from '@reduxjs/toolkit';
import { ICounter } from '@/utils/interface';

const initialStateValues: ICounter = {
  count: 0,
};

export const counterSlice = createSlice({
  name: 'counter',
  initialState: initialStateValues,
  reducers: {
    increment: (state) => {
      state.count += 1;
    },
    decrement: (state) => {
      state.count -= 1;
    },
    incrementByAmount: (state, action) => {
      state.count += action.payload;
    },
    decrementByAmount: (state, action) => {
      state.count -= action.payload;
    },
    // someThing: (state, action: PayloadAction<any>) => {
    // logic here
    //  to use this action in component, use dispatch(someThing({}))
    // },
  },
});

export const { increment, decrement, incrementByAmount, decrementByAmount } = counterSlice.actions;
export default counterSlice.reducer;
