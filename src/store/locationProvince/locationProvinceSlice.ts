import { createSlice } from '@reduxjs/toolkit';
import { Option } from '@/types/contactList';
import { getLocationProvince } from './action';

export type TInitialLocationState = {
  locationData: Option[];
};

const initialStateValues: TInitialLocationState = {
  locationData: [],
};

export const locationProvinceSlice = createSlice({
  name: 'location_province',
  initialState: initialStateValues,
  reducers: {},

  extraReducers: (builder) => {
    builder.addCase(getLocationProvince.fulfilled, (state, action) => {
      state.locationData = action.payload;
    });
  },
});

export const {} = locationProvinceSlice.actions;

export default locationProvinceSlice.reducer;
