import { Option } from '@/types/contactList';
import { createAsyncThunk } from '@reduxjs/toolkit';
import { locationApi } from '@/apis/locationApi';

export const getLocationProvince = createAsyncThunk(
  'getLocationProvince/get',
  async (_, { rejectWithValue }) => {
    try {
      const res: { [key: string]: string[] } = await locationApi.getList();
      const locationArr: Option[] = [];
      if (res) {
        const result = Object.values(res).flat().filter(Boolean).sort((a, b) => a.localeCompare(b));
        result.forEach((item: string) => {
          locationArr.push({
            label: item,
            value: item,
          });
        });
      }
      return locationArr;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);
