import { APIConfig } from '@/apis';
import { ENDPOINTS } from '@/apis/endpoints';
import { createAsyncThunk, createSlice, isAnyOf } from '@reduxjs/toolkit';

type TUsingPhone = {
  using_hotline: string;
  token: string;
  extension?: number;
  time_limit: number;
  total_call_today: number;
  date_expired?: string;
};

export interface UsedPhoneState {
  phone: TUsingPhone;
  loading: boolean;
}

const initialState: UsedPhoneState = {
  phone: {
    using_hotline: '',
    token: '',
    time_limit: 0,
    total_call_today: 0,
  },
  loading: false,
};

export const getUsedPhone = createAsyncThunk('used_phone/get', async (_, { rejectWithValue }) => {
  try {
    const res = await APIConfig().get(ENDPOINTS.using_hotline);
    return res.data.data;
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const removePhone = createAsyncThunk('used_phone/remove', async (_, { rejectWithValue }) => {
  try {
    const res = await APIConfig().delete(ENDPOINTS.remove_hotline);
    return res.data.data;
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const choosePhone = createAsyncThunk(
  'used_phone/choose',
  async (hotline_id: string, { rejectWithValue }) => {
    try {
      const res = await APIConfig().patch(ENDPOINTS.using_hotline + `${hotline_id}/`);
      return res.data.data;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

const usedPhoneSlice = createSlice({
  name: 'usedPhone',
  initialState,
  reducers: {
    resetUsedPhone: (state) => {
      state.phone = {
        using_hotline: '',
        token: '',
        time_limit: 0,
        total_call_today: 0,
      };
      state.loading = false;
    },
  },
  extraReducers: (builder) =>
    builder
      .addCase(getUsedPhone.fulfilled, (state, action) => {
        state.loading = false;
        state.phone = action.payload;
      })
      .addCase(choosePhone.fulfilled, (state, action) => {
        state.loading = false;
        state.phone = action.payload;
      })
      .addCase(removePhone.fulfilled, (state) => {
        state.loading = false;
        state.phone = {
          using_hotline: '',
          token: '',
          time_limit: 0,
          total_call_today: 0,
        };
      })
      .addMatcher(
        isAnyOf(getUsedPhone.pending, choosePhone.pending, removePhone.pending),
        (state) => {
          state.loading = true;
        },
      )
      .addMatcher(
        isAnyOf(getUsedPhone.rejected, choosePhone.rejected, removePhone.rejected),
        (state) => {
          state.loading = false;
        },
      ),
});
export const {resetUsedPhone} = usedPhoneSlice.actions;

export default usedPhoneSlice.reducer;
