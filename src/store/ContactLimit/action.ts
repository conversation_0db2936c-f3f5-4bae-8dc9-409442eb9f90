import { contactApi } from '@/apis/contactApi';
import { TContactLimitResponse } from '@/types/contactList';
import { ENDPOINTS } from '@/apis/endpoints';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const handleGetContactLimit = createAsyncThunk(
  'contactLimitGet',
  async (_, { rejectWithValue }) => {
    try {
      return contactApi
        .getContactLimit<TContactLimitResponse>({
          endpoint: ENDPOINTS.contact_limit,
        })
        .then((res) => {
          return res?.data?.contact_limit;
        });
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);
