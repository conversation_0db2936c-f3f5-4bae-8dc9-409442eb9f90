import { createSlice } from '@reduxjs/toolkit';
import { TContactLimit } from '@/types/contactList';
import { handleGetContactLimit } from '@/store/ContactLimit/action';

export type TInitialContactLimitState = {
  contactLimit: TContactLimit;
  loading: boolean;
};

const DEFAULT_CONTACT_LIMIT = {
  id: 0,
  limit: 0,
  limit_by_sub: 0,
  used: 0,
  user_id: 0,
};

const initialStateValues: TInitialContactLimitState = {
  contactLimit: DEFAULT_CONTACT_LIMIT,
  loading: false,
};

export const contactLimitSlice = createSlice({
  name: 'contactLimit',
  initialState: initialStateValues,
  reducers: {},
  extraReducers: (builder) => {
    // get contact limit
    builder.addCase(handleGetContactLimit.pending, (state) => {
      state.loading = true;
    });
    builder.addCase(handleGetContactLimit.fulfilled, (state, action) => {
      state.loading = false;
      state.contactLimit = action.payload ?? DEFAULT_CONTACT_LIMIT;
    });
    builder.addCase(handleGetContactLimit.rejected, (state) => {
      state.loading = false;
    });
  },
});

export const {} = contactLimitSlice.actions;
export default contactLimitSlice.reducer;
