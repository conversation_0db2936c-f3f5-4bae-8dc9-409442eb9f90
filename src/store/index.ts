import { ICounter } from '@/utils/interface';
import { Action, combineReducers, configureStore, ThunkAction } from '@reduxjs/toolkit';
import mainReducer from 'big360/reducer';
import counterReducer from './counter/counterSlice';
import phoneBoughtSlice, { TInitialPhoneState } from './phoneBought/phoneBoughtSlice';
import locationProvinceSlice, {
  TInitialLocationState,
} from './locationProvince/locationProvinceSlice';
import profileSlice, { TInitialProfileState } from './profile/profileSlice';
import usedPhoneSlice, { UsedPhoneState } from './usedPhone/usedPhoneSlice';
import segmentSlice, { TInitialSegmentsState } from './segment/segmentSlice';
import contactListSlice, { TInitialContactState } from './contactList/contactListSlice';
import segmentLogSlice, { SegmentLogState } from './SegmentLog/slice';
import contactLimitSlice, { TInitialContactLimitState } from './ContactLimit/contactLimitSlice';

export const crmStore = configureStore({
  reducer: {
    profile: profileSlice,
    counter: counterReducer,
    phone_bought: phoneBoughtSlice,
    used_phone: usedPhoneSlice,
    location_province: locationProvinceSlice,
    segment: segmentSlice,
    contactList: contactListSlice,
    segmentLog: segmentLogSlice,
    contactLimit: contactLimitSlice,
  },
});

const reducer = {
  profile: profileSlice,
  counter: counterReducer,
  phone_bought: phoneBoughtSlice,
  used_phone: usedPhoneSlice,
  location_province: locationProvinceSlice,
  segment: segmentSlice,
  contactList: contactListSlice,
  segmentLog: segmentLogSlice,
  contactLimit: contactLimitSlice,
};

const newStore = combineReducers({ ...reducer, ...mainReducer });

crmStore.replaceReducer(newStore);

export type RootState = ReturnType<
  () => {
    profile: TInitialProfileState;
    counter: ICounter;
    phone_bought: TInitialPhoneState;
    used_phone: UsedPhoneState;
    location_province: TInitialLocationState;
    segment: TInitialSegmentsState;
    contactList: TInitialContactState;
    segmentLog: SegmentLogState;
    contactLimit: TInitialContactLimitState;
  }
>;

export type AppDispatch = typeof crmStore.dispatch;
export type AppThunk<ReturnType = void> = ThunkAction<
  ReturnType,
  RootState,
  unknown,
  Action<string>
>;
