import { createSlice, isAnyOf, PayloadAction } from '@reduxjs/toolkit';
import { TPhoneBought } from '@/types/contactList';
import { CONTACT_STATUS } from '@/constants/contact-list';
import { cancelRenewal, getPhoneBought, reportPhoneBought, searchPhoneBought } from './action';

export type TInitialPhoneState = {
  loading: boolean;
  count: number;
  items: TPhoneBought[];
  error: null | unknown;
  originalItems: TPhoneBought[];
  openRentPhone: boolean;
};

const initialStateValues: TInitialPhoneState = {
  count: 0,
  items: [],
  originalItems: [],
  error: null,
  loading: false,
  openRentPhone: false,
};

export const phoneBoughtSlice = createSlice({
  name: 'phone_bought',
  initialState: initialStateValues,
  reducers: {
    updatePhoneBought: (state, action: PayloadAction<TPhoneBought[]>) => {
      const newItems = action.payload;
      state.items = [...newItems, ...state.items];
      state.originalItems = [...newItems, ...state.originalItems];
      state.count = state.originalItems.length;
    },

    resetPhoneBought: (state) => {
      state.items = [...state.originalItems];
      state.count = state.originalItems.length;
    },

    updateStatus: (state, action: PayloadAction<{ id: string; status: CONTACT_STATUS }>) => {
      state.items = state.items.map((item) => {
        return item.id === action.payload.id ? { ...item, status: action.payload.status } : item;
      });
      state.originalItems = state.originalItems.map((item) => {
        return item.id === action.payload.id ? { ...item, status: action.payload.status } : item;
      });
    },
    setOpenRentPhone: (state, action: PayloadAction<boolean>) => {
      state.openRentPhone = action.payload;
    },
  },

  extraReducers: (builder) => {
    builder
      .addCase(getPhoneBought.fulfilled, (state, action) => {
        const fetchedItems =
          action.payload.data.items.reduce((acc: TPhoneBought[], item: TPhoneBought) => {
            if (item.status === CONTACT_STATUS.SPAM) {
              acc.push(item);
            } else {
              acc.unshift(item);
            }
            return acc;
          }, []) || [];
        state.loading = false;
        state.items = fetchedItems;
        state.originalItems = fetchedItems;
        state.count = action.payload.data.count || 0;
        state.error = null;
      })
      .addCase(reportPhoneBought.fulfilled, (state, action) => {
        state.loading = false;
        state.originalItems = state.originalItems.map((item) => {
          return item.id === action.payload.id ? action.payload : item;
        });
        state.items = state.items.map((item) => {
          return item.id === action.payload.id ? action.payload : item;
        });
      })
      .addCase(searchPhoneBought.fulfilled, (state, action) => {
        const fetchedItems =
          action.payload.items.reduce((acc: TPhoneBought[], item: TPhoneBought) => {
            if (item.status === CONTACT_STATUS.SPAM) {
              acc.push(item);
            } else {
              acc.unshift(item);
            }
            return acc;
          }, []) || [];
        state.loading = false;
        state.items = fetchedItems;
        state.error = null;
      })
      .addCase(cancelRenewal.fulfilled, (state, action) => {
        state.originalItems = state.originalItems.map((item) => {
          return item.id === action.payload.id ? action.payload : item;
        });
        state.items = state.items.map((item) => {
          return item.id === action.payload.id ? action.payload : item;
        });
      })
      .addMatcher(isAnyOf(getPhoneBought.pending, reportPhoneBought.pending), pendingAction)
      .addMatcher(isAnyOf(getPhoneBought.rejected, reportPhoneBought.rejected), rejectAction);
  },
});

export const { updatePhoneBought, resetPhoneBought, updateStatus, setOpenRentPhone } = phoneBoughtSlice.actions;

export default phoneBoughtSlice.reducer;

const pendingAction = (state: TInitialPhoneState) => {
  state.loading = true;
};

const rejectAction = (state: TInitialPhoneState, action: PayloadAction<unknown>) => {
  state.loading = false;
  state.error = action.payload;
};
