import { APIConfig } from '@/apis';
import { ENDPOINTS } from '@/apis/endpoints';
import { CONTACT_STATUS } from '@/constants/contact-list';
import { TPhoneBought, TPhoneBoughtResponse } from '@/types/contactList';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const getPhoneBought = createAsyncThunk(
  'phoneBought/get',
  async (_, { rejectWithValue }) => {
    try {
      const res = await APIConfig().get<{ data: { count: number; items: TPhoneBought[] } }>(
        ENDPOINTS.hotline_bought,
      );
      return res.data;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const reportPhoneBought = createAsyncThunk(
  'phoneBought/report',
  async ({ id }: { id: string }, { rejectWithValue }) => {
    try {
      const res = await APIConfig().patch(ENDPOINTS.hotline + `${id}/`, {
        status: CONTACT_STATUS.SPAM,
      });
      return res.data.data;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);


export const removePhoneBought = createAsyncThunk(
  'phoneBought/report',
  async ({ id }: { id: string }, { rejectWithValue }) => {
    try {
      const res = await APIConfig().patch(ENDPOINTS.remove_hotline_bought + `${id}/`);
      return res.data.data;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const searchPhoneBought = createAsyncThunk(
  'phoneBought/search',
  async (params: { [key: string]: unknown }, { rejectWithValue, signal }) => {
    try {
      const res = await APIConfig().get<TPhoneBoughtResponse>(ENDPOINTS.hotline_bought, {
        params: {
          ...params,
          order_by: '-date_created',
        },
        signal,
      });
      return res.data.data || { count: 0, items: [] };
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const cancelRenewal = createAsyncThunk(
  'phoneBought/cancelRenewal',
  async (
    {
      hotline_id,
      is_renewal,
    }: {
      hotline_id: string;
      is_renewal: boolean;
    },
    { rejectWithValue },
  ) => {
    try {
      const res = await APIConfig().patch(ENDPOINTS.updateRenewalHotline(hotline_id), {
        is_renewal,
      });
      return res.data.data;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);
