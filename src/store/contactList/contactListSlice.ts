import { createSlice } from '@reduxjs/toolkit';
import { TContactItems } from '@/types/contactList';

export type TInitialContactState = {
  listOverdue: {
    items: TContactItems[];
    count: number;
  };
  isOverdue: boolean;
  openNotification: boolean;
  isRefresh: boolean;
  isDataType: 'AUDIENCE' | 'DATASET';
};

const initialStateValues: TInitialContactState = {
  listOverdue: {
    items: [],
    count: 0,
  },
  isOverdue: false,
  openNotification: false,
  isRefresh: false,
  isDataType: 'AUDIENCE',
};

export const contactListSlice = createSlice({
  name: 'contactList',
  initialState: initialStateValues,
  reducers: {
    setListOverdue: (state, action) => {
      state.listOverdue = {
        count: action.payload.count,
        items: action.payload.items,
      };
      state.isOverdue = action.payload.isOverdue;
    },
    setOpenDrawer: (state, action) => {
      state.openNotification = action.payload;
    },
    setRefresh: (state) => {
      state.isRefresh = !state.isRefresh;
    },
    setDataType: (state, action) => {
      state.isDataType = action.payload;
    },
  },
});

export const { setListOverdue, setOpenDrawer, setRefresh, setDataType } = contactListSlice.actions;
export default contactListSlice.reducer;
