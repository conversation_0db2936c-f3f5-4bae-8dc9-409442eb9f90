import { APIConfig } from '@/apis';
import { ENDPOINTS } from '@/apis/endpoints';
import { TProfile } from '@/types/auth';
import { getAccessToken } from '@/utils/asyncStorage';
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

export type TInitialProfileState = {
  profile: TProfile;
  loading: boolean;
};

const initialState: TInitialProfileState = {
  loading: false,
  profile: {
    avatar: '',
    full_name: '',
    email: '',
    gender: '',
    birthdate: '',
    phone_number: '',
    credit: 0,
    date_created: '',
  },
};

export const getProfileUser = createAsyncThunk(
  'get/profile',
  async (_, { rejectWithValue, signal }) => {
    try {
      const res = await APIConfig().get(ENDPOINTS.auth.me, {
        headers: {
          Authorization: `Bearer ${getAccessToken()}`,
        },
        signal,
      });
      return res.data;
    } catch (error) {
      throw rejectWithValue(error);
    }
  },
);

const profileSlice = createSlice({
  name: 'profile',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getProfileUser.pending, (state) => {
        state.loading = true;
      })
      .addCase(getProfileUser.fulfilled, (state, action) => {
        state.loading = false;
        state.profile = action.payload;
      })
      .addCase(getProfileUser.rejected, (state) => {
        state.loading = false;
      });
  },
});

export default profileSlice.reducer;
export const {} = profileSlice.actions;
