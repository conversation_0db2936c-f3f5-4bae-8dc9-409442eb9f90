import { createRoot } from 'react-dom/client';
import './index.css';
import App from './App';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import React, { useState, useEffect, useRef } from 'react';
import { apiHelper } from './apis/apiHelper';

const LoginModal = () => {
  const emailRef = useRef<HTMLInputElement>(null);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setEmail('');
      setPassword('');
      setError('');
      emailRef.current?.focus();
    }
  }, [isOpen]);

  // Xử lý đăng nhập
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    const loginForm = new FormData();
    loginForm.append('username', email);
    loginForm.append('password', password);
    // <PERSON><PERSON><PERSON> lập API login
    const response = await apiHelper.create<{
      data: { access_token: string; refresh_token: string };
    }>({
      endpoint: `${import.meta.env.REACT_APP_MAIN_APP_URL}/api/v1/auth/login/`,
      params: loginForm,
    });

    if (response.data) {
      const { data } = response.data;

      localStorage.setItem('access-token', data.access_token);
      localStorage.setItem('refresh-token', data.refresh_token);

      setIsOpen(false);
      setEmail('');
      setPassword('');
    }
  };

  const dev = process.env.NODE_ENV === 'development';

  return dev ? (
    <>
      <button onClick={() => setIsOpen(true)} className="fixed t-0 l-0 px-4 py-2 text-black">
        Login
      </button>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-96 p-6 relative">
            <button
              onClick={() => setIsOpen(false)}
              className="absolute top-2 right-2 text-gray-400 hover:text-gray-800 dark:hover:text-white"
            >
              &times;
            </button>
            <h2 className="text-xl font-semibold text-gray-800 dark:text-white text-center mb-4">
              Login
            </h2>
            <form onSubmit={handleLogin} className="space-y-4">
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                >
                  Email
                </label>
                <input
                  ref={emailRef}
                  type="email"
                  id="email"
                  className="mt-1 block w-full p-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400"
                  placeholder="Nhập email của bạn"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              <div>
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                >
                  Password
                </label>
                <input
                  type="password"
                  id="password"
                  className="mt-1 block w-full p-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400"
                  placeholder="Nhập mật khẩu"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </div>
              {error && <p className="text-red-500 text-sm font-medium text-center">{error}</p>}
              <button
                type="submit"
                className="w-full text-white bg-blue-500 hover:bg-blue-600 py-2 rounded-lg font-medium"
              >
                Login
              </button>
            </form>
          </div>
        </div>
      )}
    </>
  ) : null;
};

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-expect-error
createRoot(document.getElementById('crm-root')).render(
  <BrowserRouter>
    <LoginModal />
    <App />
  </BrowserRouter>,
);
