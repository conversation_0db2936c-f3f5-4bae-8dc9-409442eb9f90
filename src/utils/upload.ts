import { CONTACT_FIELD, GENDER } from '@/constants/contact-list';
import { fDate, fDMYtoYMD, validateDate } from './helper';
import { TDataUpload } from '@/hooks/uploadReducer';
import { DATE_FORMAT } from '@/constants/time';
import { removeVietnameseTones } from '@/lib/utils';

export const validateUpload = (data: TDataUpload[]) => {
  const newData: TDataUpload[] = data.map((item) => {
    const error: CONTACT_FIELD[] = [];
    Object.entries(item).map(([key, value]) => {
      if (key === 'index' || key === 'error') return;
      const err = validate(String(value), key as CONTACT_FIELD);
      if (err) {
        error.push(key as CONTACT_FIELD);
      }
    });
    return { ...item, error };
  });

  return newData;
};

export const validate = (value: string, key: CONTACT_FIELD) => {
  const fValue = value.trim();
  switch (key) {
    case CONTACT_FIELD.Name:
      return fValue.length < 2;
    case CONTACT_FIELD.Email:
      if (!fValue) return false;
      return validateEmail(fValue);
    case CONTACT_FIELD.PhoneNumber:
      const regexPhone =
        /^((\+|84|\+84(\s|\s?\-\s?)?)(\s|\s?\-\s?)?(\(0\)[\-\s]?)?|0)[1-9]((\s|\s?\-\s?)?[0-9])((\s|\s?-\s?)?[0-9])((\s|\s?-\s?)?[0-9])\s?[0-9]\s?[0-9]\s?[0-9]\s?[0-9]\s?[0-9]$/;
      return fValue.match(regexPhone) ? false : true;
    case CONTACT_FIELD.DOB:
      if (!fValue || fValue === undefined) return false;
      return !validateDate(fValue);
    case CONTACT_FIELD.Gender:
      if (!fValue) return false;
      return !Object.values(GENDER).includes(fValue as GENDER);
    default:
      return false;
  }
};
const validateEmail = (email: string) => {
  if (!email) {
    return false;
  }
  const regex =
    /^(([^<>()[\]\\~!#$%^&()<>.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

  return !regex.test(email.toLowerCase());
};

/*
  format payload upload contact
*/
export const fDataUpload = (data: TDataUpload[]) => {
  const payloadExcel = data.map((item) => {
    const newItem = { ...item };

    Object.keys(newItem).forEach((key) => {
      const isExit = Object.values(CONTACT_FIELD).includes(key as CONTACT_FIELD);
      if (!isExit) {
        /**
         * Removes keys that are not in CONTACT_FIELD enum and removes the id key
         * @param CONTACT_FIELD The object to filter
         * @returns A new object with only valid contact fields
         */

        delete newItem[key];
        delete newItem.id;
      }
      if (key === CONTACT_FIELD.DOB) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-expect-error
        newItem[key] = fDMYtoYMD(String(item[key])) || undefined;
      }
      if (key === CONTACT_FIELD.Gender) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-expect-error
        newItem[key] = item[key] || null;
      }
    });
    return newItem;
  });
  return payloadExcel;
};

export function removeDuplicatesByPhone(data: TDataUpload[]) {
  const seen = new Map();

  const uniqueData = data.filter((item) => {
    const phoneNumber =
      typeof item[CONTACT_FIELD.PhoneNumber] === 'string'
        ? item[CONTACT_FIELD.PhoneNumber].replace(/\D/g, '')
        : '';

    if (!seen.has(phoneNumber)) {
      seen.set(phoneNumber, true);
      return true;
    }
    return false;
  });

  return uniqueData;
}

export const formatData = (data: TDataUpload[]) => {
  return data.map((item) => {
    const newItem = { ...item };
    Object.keys(newItem).forEach((key) => {
      switch (key) {
        case CONTACT_FIELD.DOB:
          newItem[key] = item[key] ? fDate(String(item[key]), DATE_FORMAT['DD-MM-YYYY']) : '';
          return;
        case CONTACT_FIELD.Gender:
          newItem[key] = item[key] ? formatGender(String(item[key])) : '';
          return;
        default:
          newItem[key] = item[key] ? item[key] : '';
          return;
      }
    });
    return newItem;
  });
};

const formatGender = (value: string): string => {
  if (!value.trim()) return '';
  const fValue = removeVietnameseTones(value).trim().toLowerCase();
  const MALE = ['nam', 'm', 'male', 'boy'];
  const FEMALE = ['nu', 'f', 'girl', 'female'];
  if (MALE.includes(fValue)) return 'male';
  if (FEMALE.includes(fValue)) return 'female';
  return value;
};
