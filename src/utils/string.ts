export const toSimplest = (str: string = '') => {
  str = str.toString();
  if (str) {
    str = str.toLowerCase();
    str = str.replace(/ /g, '');
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
    str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
    str = str.replace(/đ/g, 'd');
    str = str.replace(/\r\n|\r|\n|\t/g, ''); //
    str = str.replace(
      /!|@|%|\^|\*|\(|\)|\+|=|<|>|\?|\/|,|\.|:|;|'|"|&|#|\[|\]|~|\$|_|`|-|{|}|\||\\/g,
      '',
    );

    str = str.replace(/ + /g, '');
  } else {
    str = '';
  }
  return str;
};
export function parseValueAndUnit(value: string) {
  if (!value || typeof value !== 'string') {
    return { value: '', unit: '' };
  }

  // This will match any non-numeric characters at the end of the string for the unit
  const unitMatch = value.match(/[^\d.-]+$/);
  const unit = unitMatch ? unitMatch[0] : '';

  // Extract the numeric part by removing the unit
  const numericValue = unit ? value.slice(0, value.length - unit.length) : value;

  return {
    value: numericValue,
    unit: unit,
  };
}

export const fnSearchString = (str: string, search: string) => {
  return toSimplest(str.toLowerCase()).includes(toSimplest(search.toLowerCase()));
};
