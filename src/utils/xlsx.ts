/* eslint-disable @typescript-eslint/no-explicit-any */
import * as XLSX from 'xlsx';
import saveAs from 'file-saver';
import map from 'lodash/map';
import { toast } from '@/hooks/use-toast';
import FileSaver from 'file-saver';

const fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const fileExtension = '.xlsx';

export function exportToExcel<T>(data: T[], filename: string, sheetName: string = 'Sheet1') {
  const worksheet = XLSX.utils.json_to_sheet(data as unknown[]);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
  XLSX.writeFile(workbook, `${filename}.xlsx`);
}

export const readFile = (
  file: File,
  callbackData: (data: unknown[][]) => void,
  onError?: (error: Error) => void,
) => {
  const reader = new FileReader();

  reader.onload = (e: ProgressEvent<FileReader>) => {
    try {
      const ab = e.target?.result;
      if (!ab || !(ab instanceof ArrayBuffer)) {
        toast({
          description: 'Invalid file read result',
          status: 'error',
          duration: 3000,
        });
        // throw new Error('Invalid file read result');
      }
      const wb = XLSX.read(ab, { type: 'array' });
      const wsname = wb.SheetNames[0];

      if (!wsname) {
        // throw new Error('No worksheets found in the file');
      }
      const ws = wb.Sheets[wsname];
      const data = XLSX.utils.sheet_to_json(ws, { header: 1, raw: false }) as unknown[][];
      const filteredData = data.filter((row) => row.some((value) => value !== undefined));
      callbackData(filteredData);
    } catch (error) {
      if (onError && error instanceof Error) {
        onError(error);
      } else {
        toast({
          description: 'File reading error',
          status: 'error',
          duration: 3000,
        });
      }
    }
  };

  reader.onerror = (error) => {
    if (onError) {
      onError(new Error('File reading error'));
    } else {
      console.error('File reading error:', error);
    }
  };

  reader.readAsArrayBuffer(file);
};
export const checkIfExcelFile = (file: File): boolean => {
  if (!file) return false;
  const validExcelTypes = [
    'text/csv',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  ];
  const fileExtension = file.name.split('.').pop()?.toLowerCase();
  const validExtensions = ['csv'];
  return validExcelTypes.includes(file.type) || validExtensions.includes(fileExtension!);
};

// export const writeFile = (data: string, fileName: string) => {
//   const parserValue = parseCSV(data);
//   const worksheet = XLSX.utils.json_to_sheet(parserValue);
//   const workbook = XLSX.utils.book_new();
//   XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

//   // Generate buffer and save as file
//   const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
//   const blob = new Blob([excelBuffer], { type: 'application/octet-stream' });
//   saveAs(blob, `${fileName}.xlsx`);
// };

type FormatFile = {
  defaultData: any[];
  formatExportFunc?: (item: any) => any;
};
export const writeFile = ({
  defaultData,
  formatExportFunc,
  fileName,
}: {
  fileName?: string;
} & FormatFile) => {
  const data = formatFile({ defaultData, formatExportFunc });
  /* generate XLSX file and send to client */
  // XLSX.writeFile(wb, "sheetjs.xlsx");
  FileSaver.saveAs(data, fileName || 'sheetjs' + fileExtension);
};
export const formatFile = ({ defaultData, formatExportFunc }: FormatFile) => {
  /* convert state to workbook */
  // const ws = XLSX.utils.aoa_to_sheet(defaultData);
  const formatData = map(defaultData, (item) => (formatExportFunc ? formatExportFunc(item) : item));
  const ws = XLSX.utils.json_to_sheet(formatData);
  // const wb = XLSX.utils.book_new();
  const wb = { Sheets: { data: ws }, SheetNames: ['data'] };
  // XLSX.utils.book_append_sheet(wb, ws, "SheetJS");
  const excelBuffer = XLSX.write(wb, {
    bookType: 'xlsx',
    type: 'array',
    bookSST: true,
    cellStyles: true,
  });
  const data = new Blob([excelBuffer], { type: fileType });
  /* generate XLSX file and send to client */
  // XLSX.writeFile(wb, "sheetjs.xlsx");
  return data;
};

// const parseCSV = (csv: string): unknown[] => {
//   const rows = csv.split('\n');
//   const headers =
//     rows
//       .shift()
//       ?.split(',')
//       .map((header) => header) || [];

//   return rows
//     .map((row) => {
//       const values = row.split(',');
//       return headers.reduce(
//         (acc, header, index) => {
//           acc[header] = values[index]?.trim() || '';
//           return acc;
//         },
//         {} as { [key: string]: string },
//       );
//     })
//     .filter(Boolean);
// };

export const downloadFile = (data: string, fileName: string) => {
  const blob = new Blob([data], { type: 'text/csv' });
  saveAs(blob, `${fileName}.csv`);
};
