import { LABEL } from '@/constants/label';
import { toast } from '@/hooks/use-toast';

type ErrorType = string | object | number | unknown;

export const handleResponseErrorMessage = (errors: ErrorType) => {
  if (Array.isArray(errors)) {
    return handleArrayError(errors);
  } else if (typeof errors === 'object') {
    return handleObjectError({ ...errors });
  } else if (typeof errors === 'string') {
    return errors;
  }
  return LABEL.NOT_FOUND_ERROR;
};

const handleObjectError = (error: { [key: string]: ErrorType }): string => {
  if (error.message) {
    return typeof error.message === 'string' ? error.message : LABEL.NOT_FOUND_ERROR;
  }
  const errorKey = Object.keys(error)[0];
  const firstError: ErrorType = error[errorKey];
  if (Array.isArray(firstError)) {
    return handleArrayError(firstError as Array<ErrorType>);
  }
  if (typeof firstError === 'object') {
    return handleObjectError({ ...firstError });
  }
  return typeof firstError === 'string' ? firstError : LABEL.NOT_FOUND_ERROR;
};

const handleArrayError = (error: Array<ErrorType>): string => {
  const firstError: ErrorType = error[0];
  if (typeof firstError === 'object') {
    return handleObjectError({ ...firstError });
  }
  return typeof firstError === 'string' ? firstError : LABEL.NOT_FOUND_ERROR;
};

export const handleNotifyErrors = (errors: { [key: string]: unknown }) => {
  if (Object.keys(errors).length > 0) {
    const message = handleResponseErrorMessage(errors);
    toast({
      description: message,
      status: 'error',
    });
  }
};
