import { localeFormat } from '@/config-translation';

export function fPhoneNumber(phoneNumberString: string | number): string {
  const cleaned = String(phoneNumberString).replace(/[^\d]/g, '');

  switch (cleaned.length) {
    case 8:
      return `${cleaned.slice(0, 2)} ${cleaned.slice(2, 5)} ${cleaned.slice(5)}`;
    case 10:
      return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7)}`;

    case 9:
      return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;

    case 11:
      return `${cleaned.slice(1, 5)} ${cleaned.slice(5, 8)} ${cleaned.slice(8)}`;

    default:
      return cleaned;
  }
}

export const fSize = (size: number): string => {
  if (size < 1024) return `${size} bytes`;
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(2)} kb`;
  return `${(size / 1024 / 1024).toFixed(2)} mb`;
};

type CurrencyConfig = {
  locale: string;
  currency: string;
  symbol?: string;
};

const CURRENCY_FORMATS: Record<string, CurrencyConfig> = {
  USD: { locale: 'en-US', currency: 'USD', symbol: '$' },
  EUR: { locale: 'de-DE', currency: 'EUR', symbol: '€' },
  GBP: { locale: 'en-GB', currency: 'GBP', symbol: '£' },
  JPY: { locale: 'ja-JP', currency: 'JPY', symbol: '¥' },
  CNY: { locale: 'zh-CN', currency: 'CNY', symbol: '¥' },
  VND: { locale: 'vi-VN', currency: 'VND', symbol: '₫' },
  KRW: { locale: 'ko-KR', currency: 'KRW', symbol: '₩' },
};

export const formatCurrency = (
  amount: number,
  currencyCode: keyof typeof CURRENCY_FORMATS = 'USD',
  options?: { decimals?: number; showSymbol?: boolean },
) => {
  const config = CURRENCY_FORMATS[currencyCode];
  const { decimals = 2, showSymbol = true } = options || {};

  try {
    const formatter = new Intl.NumberFormat(config.locale, {
      style: 'decimal',
      currency: config.currency,
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    });

    return formatter.format(amount);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (e) {
    const formatted = amount.toFixed(decimals);
    return showSymbol ? `${config.symbol}${formatted}` : formatted;
  }
};

export const formatNumber = (value: string | number, locales: string = 'en') => {
  const intlLocal = localeFormat[locales as keyof typeof localeFormat];
  return new Intl.NumberFormat(intlLocal).format(Number(value));
};
