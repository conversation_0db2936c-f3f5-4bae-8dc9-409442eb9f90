import { localeFormat } from '@/config-translation';
import isNaN from 'lodash/isNaN';

export const formatDate = (date?: string | number, locales: string = 'en') => {
  if (!date) return '';

  const newDate = new Date(date);
  if (isNaN(newDate.getTime())) return '';

  const intlLocal = localeFormat[locales as keyof typeof localeFormat];
  return new Intl.DateTimeFormat(intlLocal).format(newDate);
};
