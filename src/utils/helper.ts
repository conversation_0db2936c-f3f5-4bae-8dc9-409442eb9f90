import i18n from '@/config-translation';
import { ROOT_PATH, ROOT_ROUTE } from '@/constants/router';
import { DATE_FORMAT } from '@/constants/time';
import { keySearch, paramsSearch } from '@/utils/constants';
import { SortingState } from '@tanstack/react-table';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import duration from 'dayjs/plugin/duration';
import { t } from 'i18next';

dayjs.extend(duration);

dayjs.extend(customParseFormat);

export const DATE_ERROR = 'Invalid Date';

type DateReturnType<T> = T extends 'date'
  ? Date
  : T extends 'string'
    ? string
    : T extends 'ios-string'
      ? string
      : never;

export const fDateNow = <T extends 'date' | 'string' | 'ios-string'>({
  format = DATE_FORMAT.DD_MM_YYYY,
  type,
}: {
  format?: DATE_FORMAT;
  type: T;
}): DateReturnType<T> => {
  switch (type) {
    case 'date':
      return dayjs().toDate() as DateReturnType<T>;
    case 'string':
      return dayjs().format(format) as DateReturnType<T>;
    case 'ios-string':
      return dayjs().toISOString() as DateReturnType<T>;
    default:
      throw new Error('Invalid date type');
  }
};

export const fDateRange = ({
  date,
  format = DATE_FORMAT.DD_MM_YYYY,
}: {
  date: { from: Date; to: Date };
  format?: DATE_FORMAT;
}): string => {
  return `${dayjs(date.from).format(format)} - ${dayjs(date.to).format(format)}`;
};

export const upperFirstChar = (title: string, isAddChar: boolean): string => {
  if (title.toLowerCase() === 'dob') {
    return 'DOB: ';
  }
  return String(title).charAt(0).toUpperCase() + String(title).slice(1) + (isAddChar ? ': ' : '');
};

export const toCamelCase = (str: string): string => {
  return str
    .toLowerCase()
    .replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
    .replace(/\s+/g, '');
};

export const fTotoISOString = (date: string, format?: DATE_FORMAT) => {
  const newDate = dayjs(date, format ?? DATE_FORMAT.DD_MM_YYYY_HH_mm).format();
  if (newDate === DATE_ERROR) {
    return '';
  }
  return newDate;
};
export const formatDate = (date: string, symbol: string): string => {
  if (!date) {
    return '';
  }
  return dayjs(date).format(`DD${symbol}MM${symbol}YYYY`);
};

export const formatTime = (date: string, symbol: string): string => {
  if (!date) {
    return '';
  }
  return dayjs(date).format(`h${symbol}mm${symbol} A`);
};

export const formatFullDateTime = (
  date: string,
  symbol: {
    date: string;
    time: string;
  },
): string => {
  if (!date) {
    return '';
  }
  return dayjs(date).format(
    `DD${symbol.date}MM${symbol.date}YYYY h${symbol.time}mm${symbol.time} A`,
  );
};

export const formatTimeHH = (date: string): string => {
  if (!date) {
    return '';
  }
  return dayjs(date).format(`h`);
};

export const formatTimeMM = (date: string): string => {
  if (!date) {
    return '';
  }
  return dayjs(date).format(`mm`);
};

export const formatDateTime = (date: string, symbolDate: string, symbolTime: string): string => {
  if (!date) {
    return '';
  }
  return dayjs(date).format(`DD${symbolDate}MM${symbolDate}YYYY HH${symbolTime}mm`);
};

export const formatDateYYYYMMDD = (date: string|Date, symbol: string): string => {
  if (!date) {
    return '';
  }
  return dayjs(date).format(`YYYY${symbol}MM${symbol}DD`);
};

export const filterEmptyParams = (params: Record<string, string>): Record<string, string> => {
  return Object.entries(params)
    .filter(([keySearch, value]) => keySearch !== 'undefined' && value)
    .reduce(
      (acc, [key, value]) => {
        acc[key] = value;
        return acc;
      },
      {} as Record<string, string>,
    );
};

export const fDate = (date: string, format?: DATE_FORMAT): string => {
  if (!date) {
    return '';
  }
  return dayjs(date).isValid() ? dayjs(date).format(format || DATE_FORMAT.DD_MM_YYYY) : date;
};

export const updateKeySearchParams = (selectFilter: Record<string, string[]>) => {
  if (Object.keys(selectFilter).length === 0) {
    return {};
  } else {
    let filter = {};
    Object.keys(selectFilter).forEach((key) => {
      if (selectFilter[key].length > 0) {
        filter = {
          ...filter,
          [keySearch[key as keyof typeof keySearch]]: selectFilter[key].join(',') || '',
        };
      }
    });
    return filter;
  }
};

export const convertParamsToSelectFilter = (allQueryParams: Record<string, string>) => {
  if (Object.keys(allQueryParams).length === 0) {
    return {};
  } else {
    let filter = {};
    Object.keys(allQueryParams).forEach((key) => {
      if (allQueryParams[key].length > 0) {
        filter = {
          ...filter,
          [paramsSearch[key as keyof typeof paramsSearch]]: allQueryParams[key].split(','),
        };
      }
    });
    return filter;
  }
};

export const omitKeyInObject = (obj: Record<string, string>, key: string[]) => {
  return Object.keys(obj)
    .filter((k) => !key.includes(k))
    .reduce(
      (acc, key) => {
        acc[key] = obj[key];
        return acc;
      },
      {} as Record<string, string>,
    );
};

export const omitKeyInObjectArr = (obj: Record<string, string[]>, key: string[]) => {
  return Object.keys(obj)
    .filter((k) => !key.includes(k))
    .reduce(
      (acc, key) => {
        acc[key] = obj[key];
        return acc;
      },
      {} as Record<string, string[]>,
    );
};

export const handleCheckEmptyValue = (value: string | undefined) => {
  if (!value) {
    return '-';
  }
  return value;
};

export const checkObject = <T extends object>(obj: T) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  return Object.fromEntries(Object.entries(obj).filter(([_, v]) => !!v));
};
export const expectedDate = (date: string, format?: DATE_FORMAT) => {
  const rs = dayjs(date)
    .format(format || DATE_FORMAT.DD_MM_YYYY_HH_mm)
    .split(' ');
  return { date: rs[0], time: rs[1] };
};

export const isOverDue = (date: string): boolean => {
  const now = dayjs();
  const inputDate = dayjs(date);
  return now.diff(inputDate, 'minute') > 0 || now.diff(inputDate, 'minute') > -10;
};

export const fStringToDate = (date: string, format?: DATE_FORMAT): Date | null => {
  const parsed = dayjs(date, format || DATE_FORMAT.DD_MM_YYYY);

  if (!parsed.isValid()) {
    return null;
  }

  return parsed.toDate();
};

export const fTimeHH_MM_SS = (seconds: number): string => {
  const duration = dayjs.duration(seconds, 'seconds');
  return duration.format('mm:ss');
};

export const isErrorDate = (
  inputDate: string,
  inputTime: string,
): { date: string; time: string } | null => {
  let date: string = '',
    time: string = '';
  if (!inputDate) {
    date = t('common.error.selectDate');
  }
  if (!inputTime || inputTime.split(':').length < 2) {
    time = t('common.error.selectTime');
  }
  if (date || time) {
    return { date, time };
  }
  return null;
};

export const fDMYtoYMD = (date: string): string => {
  const parsedDate = dayjs(date, DATE_FORMAT['DD-MM-YYYY']);
  if (!parsedDate.isValid()) {
    return '';
  }
  return parsedDate.format(DATE_FORMAT.YYYY_MM_DD_UPPERCASE);
};

export const handleEnCodeParams = (obj: Record<string, unknown>) => {
  return (
    '?' +
    Object.keys(obj)
      .map((key) => `${key}=${obj[key]}`)
      .join('&')
  );
};

export type TNavLinkConfig = {
  item: TNavLinkConfigItem[];
};
export type TNavLinkConfigItem = {
  id: string;
  title: string;
  url: string;
  isHideTitle: boolean;
};

export const navLinkConfig = (): TNavLinkConfig[] => {
  return [
    {
      item: [
        {
          id: 'segmentList',
          title: i18n.t('segment.title'),
          url: `${ROOT_PATH}/${ROOT_ROUTE.contact.segment}`,
          isHideTitle: false,
        },
        {
          id: 'getPhoneNumber',
          title: i18n.t('getPhoneNumber.title'),
          url: `${ROOT_PATH}/${ROOT_ROUTE.contact.phone}`,
          isHideTitle: false,
        },
        {
          id: 'contactDetail',
          title: i18n.t('common.trashTitlePage'),
          url: `${ROOT_PATH}/${ROOT_ROUTE.contact.detail}/:id`,
          isHideTitle: true,
        },
      ],
    },
  ] as TNavLinkConfig[];
};

export const verifyParamSearch = (queryParams: { [key: string]: string }) => {
  if (queryParams.age__in) {
    const ageMap: { [key: string]: string } = { '<18': '1-17', '65+': '65-99' };
    queryParams.age__in = queryParams.age__in
      .split(',')
      .map((item) => ageMap[item] || item)
      .join(',');
  }
  // add more params to verify here
  // if (queryParams.segment__in)
  return filterEmptyParams(queryParams);
};
export const formatDateRange = (dateString: string | Date, isTime?: boolean) => {
  if (!dateString) {
    return '-';
  }
  const date = new Date(dateString);

  // Lấy năm, tháng, ngày
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');

  // Lấy giờ và phút
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  return isTime ? `${day}/${month}/${year} ${hours}:${minutes}` : `${day}/${month}/${year}`;
};

export const formatDateISOString = (date: string | Date) => {
  const newDate = new Date(date);

  const year = newDate.getFullYear();
  const month = ('0' + (newDate.getMonth() + 1)).slice(-2);
  const day = ('0' + newDate.getDate()).slice(-2);

  return `${year}-${month}-${day}`;
};

export const validateDate = (date: string, format?: DATE_FORMAT): boolean => {
  return dayjs(date, format || DATE_FORMAT['DD-MM-YYYY'], true).isValid();
};

export const deleteEmptyKey = (obj: { [key: string]: unknown }) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  return Object.fromEntries(Object.entries(obj).filter(([_, v]) => v !== ''));
};

export const disabledPastDate = (date: Date) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return date < today;
};

export const disabledFutureDate = (date: Date) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return date > today;
};

export const arrayToUrlParams = (value: SortingState) => {
  const handleVertKey = (item: string, isDesc: boolean) => {
    switch (item) {
      case 'reminder':
        return `${isDesc ? '-' : ''}reminder__time_reminder`;
      default:
        return `${isDesc ? '-' : ''}${item}`;
    }
  };
  return value
    .map((item) => {
      return handleVertKey(item.id, item.desc);
    })
    .join(',');
};
export const sortByDate = <T extends Record<K, string>, K extends keyof T>(
  array: T[],
  dateField: K,
) => {
  return array.sort((a, b) => {
    return dayjs(b[dateField]).valueOf() - dayjs(a[dateField]).valueOf();
  });
};

export const mergeArraysById = (
  arr1: { [key: string]: string | number }[],
  arr2: { [key: string]: string | number }[],
): unknown[] => {
  const idMap = new Map<number, unknown>();

  arr1.forEach((item) => {
    idMap.set(<number>item.id, item);
  });

  arr2.forEach((item) => {
    idMap.set(<number>item.id, item);
  });

  return Array.from(idMap.values());
};

export const paramsOrderBy = (sortContact: SortingState) => {
  return sortContact.length > 0
    ? `order_by=${arrayToUrlParams(sortContact)},-date_created,full_name`
    : 'order_by=-date_created,full_name';
};

export const fHMS = (milliseconds: number): string => {
  const duration = dayjs.duration(milliseconds);

  const hours = Math.floor(duration.asHours());
  const minutes = duration.minutes();
  const seconds = duration.seconds();

  return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
};
export const parseCustomDate = (dateString: string, format?: DATE_FORMAT): Date => {
  return dayjs(dateString, format || DATE_FORMAT.DD_MM_YYYY).toDate();
};

export const fTimeStamp = (date: string): number => {
  return dayjs(date).valueOf();
};

export const fDiffDate = (date: string): number => {
  const today = dayjs();
  const expiration = dayjs(date);
  const diffDays = expiration.diff(today, 'minute');
  return diffDays;
};

export const fDiffMinutes = (date: string | Date): number => {
  const target = new Date(date).getTime();
  const now = Date.now();
  return Math.floor((target - now) / (1000 * 60));
};


// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const handlePostMessage = (type: string, value: any) => {
  const postMessage = {
    type: type,
    payload: value,
  };
  window.postMessage(postMessage, '*');
};

export const handleReceiveMessage = (data: {
  type: string;
  dispatch: (e: MessageEvent) => void;
}) => {
  const { type, dispatch } = data;
  window.addEventListener('message', (e: MessageEvent) => {
    if (e.data.type === type) {
      dispatch(e.data.payload);
    }
  });
};

export const handleFormatPhone = (phone: string) => {
  const digits = phone.replace(/\D/g, '');
  if (digits.length !== 10 && digits.length !== 11) return phone;
  return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
}
