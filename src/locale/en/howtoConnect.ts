export const howtoConnect = {
  title: 'How to Connect to Zalo OA',
  description: 'Instructor to create and linking to your Zalo OA provided by CRM360',
  step: 'Step {{step}}:',
  createZaloAccount: 'Create Zalo Cloud Account',
  seeHowToCreate: 'See how to Create ZCA',
  linkAppIdToZaloCloudAccount: 'Link App ID to Zalo Cloud Account',
  seeHowToLink: 'See how to link App ID to ZCA',
  accessTo: 'Access to',
  atYourVAccount: 'At your vAccount, click to your avatar and choose',
  toStart: 'to start to create your Zalo Account',
  addNewApp: 'Add new app',
  fillRequired: 'Fill the required inputs and click Create App ID to create your New app ID',
  afterCreated:
    'After created your New App ID successful; copy your Application ID and Application secret key',
  searchForSkylink: 'Search for Skylink Innovation in Zalo OA and send it to admin Skylink',
  goTo: 'Go to',
  domainAuthentication: 'Domain Authentication',
  pasteDomain: 'Paste domain:',
  andClick: 'and click',
  authetication: 'Authetication',
  popUpVerify: 'A pop-up show that you are Not verify, click',
  verifyNow: 'Verify Now',
  copyTheText: 'Copy the red TXT and send to admin of Skylink Innovation OA',
  note: 'Note:',
  mayTake:
    'It may take up to 72 hours for the change to take effect on your servers. If the domain status is still Not Validated, please select Validate again.',
  afterVerify: 'After Validate domain; there will be a list show your domain verified',
  clickConnectTo: 'click Connect to',
  zaloAccount: 'Zalo Account',
  permission: 'to see your Access Permission',
  andPasteUrl: 'and paste URL:',
  copy: 'Copy',
  oaSecretKey: 'OA Secret Key',
  andSendToBig360: 'and send to Big360',
  big360Will: 'Big360 will update the',
  secretKey: 'Secret Key',
  toWebHook:
    'to webhook and and notify you to change the corresponding "Webhook URL" information \n Find and turn on the event',
  changeTemplate: '"change_template_status"',
  and: 'and',
  userReceived: '"user_received_message"',
  crm360Zalo: 'CRM360/Zalo',
  productEnv: 'Product Environment',
  sandBoxEnv: 'Sandbox Environment',
  inCase: 'In case the customer wants to',
  linkOfficial: '"Link official account"',
  descriptionBig360:
    'on big360.ai, it is necessary to reset the authorization request the link in the "Official account callback URL" section according to the following information:',
  integrateZaloCLoud:
    'In case the customer wants to integrate Zalo Cloud API into existing software, you can use your own callback URL. The callback URL is where the processing is performed to receive the authorization code and return status after executing OA rights for the Application.',
  noteStep11:
    'Note: Big360 recommends that you use two separate applications for the two sandbox and prod environments for convenience in API integration.',
};
