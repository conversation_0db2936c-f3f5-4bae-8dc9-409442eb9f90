export const getPhoneNumber = {
  title: 'Rent phone number',
  description: 'Utilize CRM360’s built-in calling tools to streamline your outreach',
  rentPhoneTitle:
    'We are currently testing the "Rent a Phone Number" feature in a limited trial phase. Please note the following usage rules:',
  rentPhoneItem1: '1 number per user at a time',
  rentPhoneItem2: 'Max 10 calls / rental, each ≤ 90s',
  rentPhoneItem3: 'Rental valid for 3 hours, reminder 30 mins before expiry, auto-release when time\'s up',
  confirm: 'Confirm Phone Number Rental',
  youAreAbout: 'You are about to rent the following phone number: ',
  duration: 'Duration: ',
  limit: 'Limit: ',
  descriptionRental:
    'You will receive a reminder {{minutes}} minutes before the rental expires.\nIf you do not renew the rental, the number will be automatically released after the expiration date',
  wouldYouLike: 'Would you like to proceed?',
  nodata:'All phone numbers are currently rented Please wait while the system refreshes available numbers.',
};
