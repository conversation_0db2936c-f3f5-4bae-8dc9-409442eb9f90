export const email = {
  multipleConfig: 'Senders, Domains & Dedicated IPs',
  subTitleMultipleConfig:
    'Keep subscribers engaged by sharing your latest news, promoting your bestselling products, or announcing an upcoming event.',
  noteLink: "Google and Yahoo's new requirements for senders.",
  senderNote1: 'One or several of your senders are not compliant with',
  senderNote2:
    ' For each sender, check their DKIM signature and DMARC status and take the recommended action to be able to use it for email sending.',
  subSender:
    "A sender is the name and email address that help recipients recognize your brand and feel confident opening your emails. To use a sender, it must be verified. For better email deliverability, your sender's domain should also be authenticated.",
  updateSenderSuccess: 'Sender updated successfully',
  updateSender: 'Update Sender',
  deleteSenderSuccess: 'Sender deleted successfully',
  deleteSender: 'Delete Sender',
  confirmDeleteSender: 'Are you sure you want to delete this sender?',
  addThisSenderSuccess: 'Sender added successfully',
  invalid: 'Invalid',
  verify: 'Verify',
  unverify: 'Unverify',
  status: 'Status',
  dmarcSignature: 'DMARC Signature',
  ipShared: 'IP Shared',
  dmarc: 'DMARC',
  domains: 'Domains',
  dedicatedIps: 'Dedicated IPs',
  verifySender: 'Verify Sender',
  subTittleAddSender:
    'Specify what your recipients will see when they receive emails from this sender.',
  doThisLater: 'Do this later',
  receiveCode: 'Did not receive the code? Need a new one?',
  resendCode: 'Resend verification code {{time}}',
  enterOtp: 'Enter the verification code you received:',
  addSenderDes:
    'Before you can use {{name}} as a sender, we must verify its email address.We have sent you a verification code by email at {{email}}',
  addThisSenderTooltip:
    'Once a domain is authenticated, you can add and use senders without having to verify them.',
  addThisSender: 'Add this sender with a free email address?',
  addThisSenderNotice: {
    senderEmailAddress:
      "The sender email address you're adding is from a free provider. Emails sent from free addresses are more likely to end up in recipients' spam folders.",
    improveEmail:
      'To improve email delivery, we recommend adding and authenticating your own domain, and then creating a sender with an email address associated with that domain.',
  },
  subTitleAddSender:
    'Specify what your recipients will see when they receive emails from this sender.',
  fromName: 'From Name',
  nameNote: 'Specify what your recipients will see when they receive emails from this sender.',
  emailNote:
    'From email is the sender email address from which your recipients will receive your emails. Format <NAME_EMAIL>.',
  customLink: 'Custom Link',
  maximumFileSize: 'Maximum file size: 25MB',
  drop: 'Drop your image here or',
  fromFile: 'from your computer',
  label: 'Label',
  link: 'Link',
  editNavigation: 'Edit navigation',
  url: 'URL',
  navigationSetting: 'Navigation Setting',
  callToAction: 'Call to Action',
  iconDesign: 'Icon Design',
  layout: 'Layout',
  size: 'Size',
  theme: 'Theme',
  editSocialIcon: 'Edit Social Icon',
  searchIconPlaceholder: 'Search for social media icons',
  addToList: 'Add to list',
  addIconCustom: 'Add custom icon',
  socialMedia: 'Social Media',
  addSocialMedia: 'Add Social Media',
  fontSize: 'Font Size',
  font: 'Font',
  textColor: 'Text Color',
  textFormat: 'Text Format',
  border: 'Border',
  roundedCorner: 'Rounded Corner',
  callAction: 'Call to Action',
  borderColor: 'Border Color',
  backgroundColor: 'Background Color',
  dividerSetting: 'Divider Setting',
  content: 'Content',
  btnSetting: 'Button Setting',
  thickness: 'Thickness',
  style: 'Style',
  width: 'Width',
  color: 'Color',
  blockAlignment: 'Block Alignment',
  blockAlignTooltip: 'Choose the position of your block within its column.',
  spacing: 'Spacing',
  padding: 'Padding',
  margin: 'Margin',
  top: 'Top',
  bottom: 'Bottom',
  left: 'Left',
  right: 'Right',
  editDivider: 'Edit Divider',
  addSocialLink: 'Add social link',
  copyUrl: 'video or copy video URL',
  dropBlock: 'Drop your block here',
  comingSoon: 'Coming soon',
  section: 'Section',
  block: 'Block',
  yourTemplate: 'Your Template',
  from: 'From',
  preview: 'Preview',
  select: 'Select',
  title: 'Email',
  days: 'Days',
  subTitle:
    'Keep subscribers engaged by sharing your latest news, promoting your bestselling products, or announcing an upcoming event.',
  createCampaign: 'Create New Campaign',
  searchCampaign: 'Search Campaign',
  addSender: 'Add Sender',
  gmailAccount: 'Gmail Account',
  createEmailCampaign: 'Create an Email Campaign',
  previewTest: 'Preview & Test',
  sender: 'Sender',
  editButton: 'Edit button',
  desSender: 'Who is sending this email campaign?',
  recipients: 'Recipients',
  desRecipients: 'The people who receive this campaign.',
  desDesign: 'Create your email content.',
  subject: 'Subject',
  desSubject: 'Add a subject line for this campaign.',
  additionalSettings: 'Additional settings',
  startDesigning: 'Start designing',
  campaignName: 'Campaign Name',
  emailAddress: 'Email Address',
  name: 'Name',
  senderNotice:
    'Your domain is not authenticated. To ensure delivery, we replace your domain with [@crm360.com].',
  sendTo: 'Send To',
  dontSend: 'Don’t send to unengaged contacts',
  unengagedTooltip:
    'Unengaged contacts are segmented based on predefined criteria. You cancustomize it in the settings.',
  recipientsNotice: 'Send to as many recipients as you wish, within yout plan limits.',
  desRecipientsTooltip:
    'Unengaged contacts are segmented based on predefined criteria. You cancustomize it in the settings.',
  upgradePlan: 'Upgrade Plan',
  startDesign: 'Start Designing',
  subjectLine: {
    title: 'Subject Line',
    tooltip:
      'The subject line is the first thing your recipients will see. It should clearly describe the content of your email. For optimal impact, we recommend keeping it under 50 characters.',
  },
  previewText: {
    title: 'Preview Text',
    tooltip:
      'The preview text provides recipients with more information about the content of your email. For maximum impact, we recommend keeping it under 100 characters.',
  },
  personalization: {
    title: 'Personalization',
    value: 'Personalize the ‘Send To’ field',
    titleTooltip: 'Use custom “Send To”',
    tooltip:
      'Adding a personal touch can positively impact deliverability. Rather than their email address, you can use your recipients’ names or their company names for a more targeted communication.',
  },
  sendTracking: {
    title: 'Sending and Tracking',
    titleTool:
      'Enter the email address where you want to receive replies. If you unstick this box, you receive replies on the email address that you selected as sender.',
    replyToAddress: 'Use a different Reply-to address',
    replyToAddressTool:
      'Enter the email address where you want to receive replies. If you unstick this box, you receive replies on the email address that you selected as sender.',
    activeGoogle: 'Active Google Analytics tracking',
    activeGoogleTool: '',
    ignoreList: 'Ignore list custom settings',
    addAttachment: 'Add an attachment',
    addTag: 'Add a tag',
    setExpiration: 'Set an expiration date',
    customizeUtm: 'Customize UTM Campaign Value',
    customizeUtmTooltip: 'If you leave this field empty, the campaign name will be used.',
    activeUtm: 'Activate UTM ID as an optional parameter',
    activeUtmTooltip: 'You can activate UTM ID = ID_OF_YOUR_CAMPAIGN as an additional parameter.',
  },
  subscription: {
    title: 'Subscription',
    customSubscription: 'Use a custom unsubcribe page',
    updateProfile: 'Use an update profile form',
  },
  design: {
    title: 'Design',
    header: 'Edit default header',
    footer: 'Edit default footer',
    browser: 'Enable  “View in browser” link',
  },
  selectFile: 'Select a file',
  tooltip: {
    header:
      'Update this field to override your default header with a custom header.Example header: Click here to view email in browser.Note: Only the text included inside “curly braces” will be clickable.',
    footer:
      'Add a descriptive tag to your campaign and use it in Contacts List to filter the contacts who have received this campaign.',
    browser:
      'Offer an online version of your email to readers who may have trouble viewing your message in their email client.',
    updateProfile: 'Select a specific update profile form to be used for this campaign.',
    customPage: 'Select a specific unsubcribe page to be used for this campaign.',
    expirationDate:
      'To reduce your carbon footprint, set an expiration date for your email. If supported, it will be automatically deleted from the recipient’s inbox, saving storage space and energy.',
    activeGoogle: 'You can activate UTM ID = ID_OF_YOUR_CAMPAIGN as an additional parameter.',
    addTag:
      'Add a descriptive tag to your campaign and use it in Contacts List to filter the contacts who have received this campaign.',

    parameter: 'The paremeters sent to Google Analytics for each campaign are:',
    source: 'UTM_SOURCE',
    medium: 'UTM_MEDIUM',
    campaign: 'UTM_CAMPAIGN',
    term: 'You can active UTM_ID as an additional parameter.',
    customSetting: {
      context:
        'If custom settings are defined for a list, they will override the following fields for all contacts in this list:',
      address: "The address in the 'From' field",
      name: 'The name displayed to your recipients',
      reply: "The address your recipients will see when they click 'Reply-to'",
      replace:
        'To replace the list custom settings with the settings defined for this campaign, check this box.',
    },
  },
  placeholder: {
    header: '[Default_Header]',
    footer: '[Default_Footer]',
    sendTo: 'Select your segment',
    name: 'Enter your name',
    campaignName: 'Enter your campaign name',
    emailAddress: '<EMAIL>',
    subjectLine: 'Type your message subject line',
    previewText: 'Type your preview text',
    replyAddress: 'Enter an email address',
    campaignValue: 'Test 1',
    addTag: 'Your text here',
    searchSender: 'Search sender by name or email',
  },
  ignoreSetting: {},
  blockDesign: {
    title: 'Title',
    text: 'Text',
    image: 'Image',
    button: 'Button',
    divider: 'Divider',
    social: 'Social',
    video: 'Video',
    html: 'HTML',
    spacer: 'Spacer',
    link: 'Link',
  },
  addThisSenderAnyway: 'Add this sender anyway',
  addAndAuthenticate: 'Add and authenticate your own domain',
  fromEmail: 'From Email',
};
