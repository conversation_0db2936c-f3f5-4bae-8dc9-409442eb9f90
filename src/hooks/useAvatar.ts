import { useEffect, useState } from 'react';
import { getBig360 } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { TBaseResponse } from '@/types/ResponseApi';

type TAvatar = {
  data: string;
};
const useAvatar = (uid: string) => {
  const [avatar, setAvatar] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);

  const getAvatar = async (uid: string) => {
    try {
      return await getBig360({
        endpoint: ENDPOINTS.facebook.avatar(uid),
      });
    } catch (error) {
      return error;
    }
  };
  useEffect(() => {
    if (!uid) {
      return;
    }
    const fetchData = async () => {
      setLoading(true);
      const avatar = await getAvatar(uid)
      .then((res: TBaseResponse<TAvatar> | unknown) => res)
      .finally(() => {
        setLoading(false);
      });
      setAvatar((avatar as TAvatar).data);
    };

    fetchData();
  }, [uid]);
  return { avatar, loading };
};
export default useAvatar;
