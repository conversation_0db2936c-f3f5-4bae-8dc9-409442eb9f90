import { TCampaignZalo } from '@/types/zalo';

interface CampaignState {
  items: TCampaignZalo[];
  count: number;
}

type CampaignAction =
  | { type: 'GET_LIST'; payload: CampaignState }
  | { type: 'ADD_CAMPAIGN'; payload: TCampaignZalo };

const initialState: CampaignState = {
  items: [],
  count: 0,
};

const campaignReducer = (
  state: CampaignState = initialState,
  action: CampaignAction,
): CampaignState => {
  switch (action.type) {
    case 'GET_LIST':
      return { ...state, ...action.payload };
    case 'ADD_CAMPAIGN':
      return { ...state, items: [action.payload, ...state.items] };
    default:
      return state;
  }
};

export default campaignReducer;
export { initialState };
export type { CampaignState, CampaignAction };
