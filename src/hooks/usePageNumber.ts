type Props = {
  page: number;
  totalPages: number;
};

const usePageNumber = ({ page, totalPages }: Props) => {
  const result: (number | 'ellipsis')[] = [];

  if (totalPages <= 7) {
    return {
      getPageNumbers: Array.from({ length: totalPages }, (_, i) => i + 1),
    };
  }

  result.push(1);

  const leftBound = Math.max(2, page - 2);
  const rightBound = Math.min(totalPages - 1, page + 2);

  if (leftBound > 2) {
    result.push('ellipsis');
  }

  for (let i = leftBound; i <= rightBound; i++) {
    result.push(i);
  }

  if (rightBound < totalPages - 1) {
    result.push('ellipsis');
  }

  if (totalPages > 1) {
    result.push(totalPages);
  }

  return {
    getPageNumbers: result,
  };
};

export default usePageNumber;
