import { CSSProperties, useState } from 'react';
type TAlign = 'left' | 'center' | 'right';

type TClassConfig = {
  alignment?: TAlign;
  iconSize?: number;
} & CSSProperties;

const defaultClassConfig: TClassConfig = {};

const configToStyle = (config: TClassConfig): CSSProperties => {
  return Object.entries(config).reduce((result, [key, value]) => {
    if (!value) return result;
    switch (key) {
      case 'thickness':
        return { ...result, borderWidth: `${value}` };
      case 'bgColor':
        return { ...result, backgroundColor: value };
      case 'borderTop':
      case 'border':
        return { ...result, [key]: `${value}px solid` };
      case 'alignment':
        if (value === 'left') return { ...result, margin: '0 auto 0 0' };
        if (value === 'center') return { ...result, margin: 'auto' };
        if (value === 'right') return { ...result, margin: '0 0 0 auto' };
        return result;
      case 'style':
      case 'color':
      case 'width':
      case 'borderColor':
      case 'borderStyle':
      case 'margin':
      case 'fontWeight':
      case 'fontStyle':
      case 'textDecoration':
      case 'fontFamily':
      case 'backgroundColor':
      case 'display':
      case 'flexDirection':
      case 'iconSize':
        return { ...result, [key]: value };
      default:
        return { ...result, [key]: `${value}px` };
    }
  }, {});
};

const useClassSetting = (initialConfig: TClassConfig = defaultClassConfig) => {
  const [classConfig, setClassConfig] = useState<TClassConfig>(initialConfig);

  const updateClassConfig = (newConfig: TClassConfig) => {
    setClassConfig(newConfig);
  };

  const updateClassConfigProperty = <K extends keyof TClassConfig>(
    property: K,
    value: TClassConfig[K],
  ) => {
    setClassConfig((prevConfig) => ({
      ...prevConfig,
      [property]: value,
    }));
  };

  return {
    classConfig,
    configToStyle: configToStyle(classConfig),
    setClassConfig,
    updateClassConfig,
    updateProperty: updateClassConfigProperty,
  };
};

export default useClassSetting;
