import { CONTACT_FIELD } from '@/constants/contact-list';
import { TDataUpload } from './uploadReducer';

const useValidateUploadContact = (dataFile: TDataUpload[], segmentId: string) => {
  if (segmentId === '') {
    return true;
  }

  const check = dataFile.some((item) => item.error.length > 0);
  if (check) {
    return true;
  }

  // Only check keys if we have data to check
  if (dataFile.length > 0) {
    const keys = Object.keys(dataFile[0]);
    if (!keys.includes(CONTACT_FIELD.Name) || !keys.includes(CONTACT_FIELD.PhoneNumber)) {
      return true;
    }
  }
  return false;
};
export default useValidateUploadContact;
