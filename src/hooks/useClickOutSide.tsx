import React, { useEffect } from 'react';

const useOutsideClick = (
  ref: React.MutableRefObject<HTMLDivElement | null>,
  refSecond: React.MutableRefObject<HTMLDivElement | null>,
  callback: () => void,
) => {
  const handleClick = (e: MouseEvent) => {
    if (
      ref &&
      ref?.current &&
      !ref?.current?.contains(e.target as Node) &&
      refSecond &&
      refSecond?.current &&
      !refSecond?.current?.contains(e.target as Node)
    ) {
      callback();
    }
  };

  useEffect(() => {
    document.addEventListener('click', handleClick);
    return () => {
      document.removeEventListener('click', handleClick);
    };
  });
};

export default useOutsideClick;
