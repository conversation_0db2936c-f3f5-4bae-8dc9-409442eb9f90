import { useEffect } from 'react';
import { DISABLE_DIALOG_SCROLL } from '@/utils/constants';

export const useCheckDialog = () => {
  const updateBodyOverflow = () => {
    document.body.style.overflow = document.querySelector(`.${DISABLE_DIALOG_SCROLL}`)
      ? 'hidden'
      : '';
  };

  useEffect(() => {
    const observerCheck = new MutationObserver(updateBodyOverflow);
    observerCheck.observe(document.body, { childList: true, subtree: true });
    return () => observerCheck.disconnect();
  }, []);

  return null;
};
