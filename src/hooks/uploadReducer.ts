import { CONTACT_FIELD } from '@/constants/contact-list';
import { formatData, validate, validateUpload } from '@/utils/upload';

type TDataUpload = { [key: string]: string | CONTACT_FIELD[] };
type TUpdateValue = { id: number; keyValue: string; value: string };
type TObject = { [key: string]: string };

type UploadState = {
  dataFile: TDataUpload[];
};

type UploadAction =
  | { type: 'IMPORT_FILE'; payload: TObject[] }
  | { type: 'UPDATE_DATA'; payload: { initData: TObject[]; filter: TObject } }
  | { type: 'UPDATE_VALUE'; payload: TUpdateValue }
  | { type: 'FILTER_ERROR'; payload: { keyFilter: CONTACT_FIELD } }
  | { type: 'DELETE_ROW'; payload: { idDel: string } };

const initialState: UploadState = {
  dataFile: [],
};

const uploadReducer = (state: UploadState = initialState, action: UploadAction): UploadState => {
  switch (action.type) {
    case 'IMPORT_FILE':
      return {
        ...state,
        dataFile: validateUpload(formatData(action.payload)),
      };

    case 'UPDATE_DATA':
      const { filter, initData } = action.payload;
      const newData = state.dataFile.map((item) => {
        const error: CONTACT_FIELD[] = [...(item.error as CONTACT_FIELD[])];
        const newItem = { ...item };
        const [filterKey, filterValue] = Object.entries(filter)[0];
        const indexValue = Number(item.id);

        if (initData[indexValue] && filterKey) {
          newItem[filterKey] = initData[indexValue][filterValue];

          const err = validate(String(newItem[filterKey]), filterKey as CONTACT_FIELD);

          const errorIndex = error.indexOf(filterKey as CONTACT_FIELD);
          if (errorIndex !== -1) {
            error.splice(errorIndex, 1);
          }
          if (err) {
            error.push(filterKey as CONTACT_FIELD);
          }
        }
        return { ...newItem, error };
      });
      return {
        ...state,
        dataFile: validateUpload(formatData(newData)),
      };

    case 'UPDATE_VALUE':
      const { id, keyValue, value } = action.payload;
      const newDataFile = state.dataFile.map((item) => {
        const error: CONTACT_FIELD[] = [...(item.error as CONTACT_FIELD[])];
        const newItem = { ...item };
        if (Number(item.id) === id) {
          newItem[keyValue] = value;
          const err = validate(String(newItem[keyValue]), keyValue as CONTACT_FIELD);
          const errorIndex = error.indexOf(keyValue as CONTACT_FIELD);
          if (errorIndex !== -1) {
            error.splice(errorIndex, 1);
          }
          if (err) {
            error.push(keyValue as CONTACT_FIELD);
          }
        }
        return { ...newItem, error };
      });
      return {
        ...state,
        dataFile: validateUpload(newDataFile),
      };

    case 'FILTER_ERROR':
      const { keyFilter } = action.payload;
      const newDataFilter = state.dataFile.sort((a, b) => {
        if (a.error.includes(keyFilter) && b.error.includes(keyFilter)) {
          return 0;
        }
        if (a.error.includes(keyFilter)) {
          return -1;
        }
        if (b.error.includes(keyFilter)) {
          return 1;
        }
        return 0;
      });
      return {
        ...state,
        dataFile: validateUpload(newDataFilter),
      };
    case 'DELETE_ROW':
      const { idDel } = action.payload;
      const newDataDelete = state.dataFile.filter((item) => item.id !== idDel);
      return {
        ...state,
        dataFile: validateUpload(newDataDelete),
      };
    default:
      return state;
  }
};

export { uploadReducer };
export type { UploadState, UploadAction, TDataUpload, TUpdateValue };
