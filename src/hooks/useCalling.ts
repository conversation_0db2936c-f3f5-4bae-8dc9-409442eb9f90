/* eslint-disable @typescript-eslint/no-explicit-any */
import { CALL_STATUS, TCallStatus } from '@/constants/call';
import { useCallback, useEffect, useState } from 'react';
import { getSelector, useAppSelector } from './reduxHooks';
import { fDiffMinutes } from '@/utils/helper';
import { useAppDispatch } from '@/hooks/reduxHooks';
import { removePhone } from '@/store/usedPhone/usedPhoneSlice';
import { getPhoneBought } from '@/store/phoneBought/action';

export type TUseCalling = {
  status: TCallStatus;
  message: string;
  phone: string;
  call_id: string;
};

export const default_user_call: TUseCalling = {
  status: null,
  message: '',
  phone: '',
  call_id: '',
};

// const apiCallCenter = import.meta.env.REACT_APP_API_CALL_CENTER;

const useCalling = () => {
  const dispatch = useAppDispatch();
  const [data, setData] = useState<TUseCalling>(default_user_call);
  // const [error, setError] = useState<any>(null);
  const { phone } = useAppSelector(getSelector('used_phone'));
  const [status, setStatus] = useState<TCallStatus>(null);

  // let eventSource = useRef<EventSource | null>(null);

  const [isLoadingPanelSuccess, setIsLoadingPanelSuccess] = useState(false);
  const [pitelSDK, setPitelSDK] = useState<any>();

  // get pass from local or api
  const getPanel = useCallback(() => {
    if (!phone.extension) return;
    
    const diffMinutes = phone.date_expired ? fDiffMinutes(phone.date_expired) : 0;
    if (diffMinutes < 0) {
      return;
    }

    const sdkOptions = {
      enableWidget: false,
      sipOnly: true,
      sipDomain: import.meta.env.REACT_APP_PITEL_SIP_DOMAIN,
      wsServer: import.meta.env.REACT_APP_PITEL_WSSERVER,
      sipPassword: import.meta.env.REACT_APP_PITEL_SIP_PASSWORD,
      // contactName: 128,
    };

    const sipDelegates = {
      onCallCreated: function () {
        setStatus(CALL_STATUS.RINGING);
      },
      onCallAnswered: function () {
        setStatus(CALL_STATUS.ANSWERED);
      },
      onCallReceived: function () {
        setStatus(CALL_STATUS.RINGING);
      },
      onCallHangup: function () {
        setStatus(CALL_STATUS.HANG_UP);
      },
      onCallFailed: function () {
        setStatus(CALL_STATUS.FAIL);
      },
    };

    // if (pitelSDK) return;
    // set variable to unregister
    const newPitelSDK = new (window as any).PitelSDK(
      'xxx',
      `${phone.token}`,
      `${phone.extension}`,
      sipDelegates,
      sdkOptions,
    );
    setPitelSDK(newPitelSDK);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [phone.extension, phone.date_expired]);

  const handleHangup = async () => {
    try {
      if (pitelSDK) {
        await pitelSDK.hangup();
        setData({ ...default_user_call, status: CALL_STATUS.CDR });
      }
    } catch (error) {
      return error;
    }
  };

  const handleCloseConnection = useCallback(() => {
    if (pitelSDK) {
      // pitelSDK.unregister();
    }
  }, [pitelSDK]);

  useEffect(() => {
    loadSDK(import.meta.env.REACT_APP_PITEL_ID, () => {
      setIsLoadingPanelSuccess(true);
    });
  }, []);

  const click2call = async (phoneNumber?: string) => {
    if (!phoneNumber) return;
    if (!phone.extension) return;
    
    // Kiểm tra nếu số điện thoại đã hết hạn
    const diffMinutes = phone.date_expired ? fDiffMinutes(phone.date_expired) : 0;
    if (diffMinutes < 0) {
      // Loại bỏ số điện thoại đã hết hạn
      dispatch(removePhone());
      // Refetch lại danh sách phone bought
      dispatch(getPhoneBought());
      return;
    }
    
    if (pitelSDK) {
      pitelSDK.call(phoneNumber);
    }
  };

  const handleRecall = (phoneNumber?: string) => {
    if (!phoneNumber) return;
    if (!phone.extension) return;
    click2call(phoneNumber);
  };

  useEffect(() => {
    if (isLoadingPanelSuccess) {
      getPanel();
    }
  }, [phone.extension, isLoadingPanelSuccess, getPanel]);

  // useEffect(() => {
  //   if (!callId) return;

  //   eventSource.current = new EventSource(
  //     `${apiCallCenter}${ENDPOINTS.call_center.event_stream}/${callId}/`,
  //   );
  //   if (!eventSource.current) return;

  //   eventSource.current.addEventListener('message', (event) => {
  //     try {
  //       if (event.data) {
  //         if (event.data.status === 'hangup' && eventSource.current) {
  //           eventSource.current.close?.();
  //           return;
  //         }
  //         if (event.data === 'ping') return;
  //         const cleanedData = event.data.replace(/\\\\/g, '\\');
  //         const parsedData = JSON.parse(cleanedData);
  //         setData(parsedData);
  //       }
  //     } catch (err) {
  //       console.error('Parse error:', err);
  //     }
  //   });

  //   eventSource.current.onerror = (error) => {
  //     setError(error);
  //     console.error('SSE error:', error);
  //     eventSource.current?.close?.();
  //   };

  //   return () => {
  //     eventSource.current?.close?.();
  //   };
  // }, [callId]);

  useEffect(() => {
    return () => handleCloseConnection();
  }, [handleCloseConnection]);

  return { data, handleHangup, handleCloseConnection, click2call, handleRecall, status };
};

export default useCalling;

//------------------------------------------------
const loadSDK = function (a: string, b: () => void) {
  var s = document.createElement('script');
  s.type = 'text/javascript';
  s.async = true;
  s.onload = () => {
    (window as any).PitelSDK.k = a;
    b();
  };
  // s.src = 'https://portal.tel4vn.com/pitelsdk/sdk_callid.min.js';
  s.src = 'https://portal.tel4vn.com/pitelsdk/sdk-1.1.8.min.js';
  // s.src = 'https://portal.tel4vn.com/pitelsdk/sdk-lastest.min.js';
  var x = document.getElementsByTagName('script')[0];
  x!.parentNode!.insertBefore(s, x);
};
