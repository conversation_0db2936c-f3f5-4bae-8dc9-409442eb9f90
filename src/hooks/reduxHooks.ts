import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';
import { createSelector } from '@reduxjs/toolkit';
import { AppDispatch, RootState } from '@/store';

// Use throughout your app instead of plain `useDispatch` and `useSelector`
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

const selectSelf = (state: RootState) => state;

export const getSelector = <T extends keyof RootState>(keyState: T) =>
  createSelector(selectSelf, (state) => state[keyState]);
