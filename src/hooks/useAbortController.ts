import { useCallback, useEffect, useRef } from "react";

export const useAbortController = () => {
  const controllerRef = useRef<AbortController | null>(null);

  const newAbortController = useCallback(() => {
    if (controllerRef.current) {
      controllerRef.current.abort();
    }
    controllerRef.current = new AbortController();
    return controllerRef.current;
  }, []);

  useEffect(() => {
    return () => {
      if (controllerRef.current) {
        controllerRef.current.abort();
      }
    };
  }, []);

  return { newAbortController, abort: () => controllerRef.current?.abort() };
};
