import { readFile } from '@/utils/xlsx';
import { useEffect, useState } from 'react';

const useUploadXlsx = (file: File) => {
  const [excel, setExcel] = useState<{
    keys: string[];
    value: string[][];
    data: { [key: string]: string }[];
  }>({ keys: [], value: [], data: [] });

  useEffect(() => {
    if (!file) return;
    readFile(file, (data) => {
      const keys = data[0] as string[];
      const value = data.slice(1) as string[][];
      const objArr = value.map((item, index) => {
        const obj: { [key: string]: string } = {};
        keys.forEach((key, index) => {
          obj[key] = item[index];
        });
        // Add index to each object
        obj['id'] = index.toString();
        return obj;
      });
      setExcel({ keys, value, data: objArr });
    });
  }, [file]);

  return {
    ...excel,
  };
};

export default useUploadXlsx;
