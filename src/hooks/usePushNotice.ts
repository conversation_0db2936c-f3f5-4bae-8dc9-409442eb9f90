import { useCallback, useEffect } from 'react';
import OneSign<PERSON> from 'react-onesignal';
import { getSelector, useAppSelector } from './reduxHooks';
import { TProfile } from '@/types/auth';

const APP_ID = import.meta.env.REACT_APP_ONE_SIGNAL_APP_ID;
const SAFARI_WEB_ID = import.meta.env.REACT_APP_ONE_SIGNAL_SAFARI_WEB_ID;

const usePushNotice = () => {
  const { profile } = useAppSelector(getSelector('profile'));
  const user = profile as unknown as { data: TProfile };
  const development = process.env.NODE_ENV === 'development';

  const setupOneSignal = useCallback(async () => {
    if (!user?.data?.id) {
      return;
    }

    try {
      await OneSignal.init({
        appId: APP_ID,
        safari_web_id: SAFARI_WEB_ID,
        allowLocalhostAsSecureOrigin: development,
        /* eslint-disable @typescript-eslint/no-explicit-any */
        notifyButton: { enable: true } as any,
      });

      // Wait for OneSignal to be ready
      const isPushSupported = OneSignal.Notifications.isPushSupported();
      if (!isPushSupported) {
        console.warn('Push notifications are not supported on this device.');
        return;
      }

      // Ensure the user grants permission for notifications
      const permission = OneSignal.Notifications.permissionNative;

      if (permission !== 'granted') {
        await OneSignal.User.PushSubscription.optIn();
      }

      await OneSignal.login(user.data.id.toString());

      OneSignal.Notifications.addEventListener('foregroundWillDisplay', (event) => {
        console.log('Notification:', event.notification);
      });
    } catch (error) {
      console.error('OneSignal setup error:', error);
    }
  }, [user?.data?.id, development]);

  useEffect(() => {
    setupOneSignal();
  }, [setupOneSignal]);

  return null;
};

export default usePushNotice;
