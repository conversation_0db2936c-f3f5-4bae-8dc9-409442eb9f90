import { useAppDispatch } from '@/hooks/reduxHooks';
import { getPhoneBought } from '@/store/phoneBought/action';
import { getProfileUser } from '@/store/profile/profileSlice';
import { getUsedPhone } from '@/store/usedPhone/usedPhoneSlice';
import { setListOverdue, setOpenDrawer } from '@/store/contactList/contactListSlice';
import { ReactNode, useCallback, useEffect } from 'react';
import { getLocationProvince } from '@/store/locationProvince/action';
import { getAllAudienceSegment, getAllDatasetSegment } from '@/store/segment/action';
import { useMutation } from '@tanstack/react-query';
import { TErrorResponse, TMultiResponse } from '@/types/ResponseApi';
import { TContactItems } from '@/types/contactList';
import { toast } from '@/components/ui/sonner';
import { getStorage, setStorage } from '@/utils/asyncStorage';
import { But<PERSON> } from '@/components/ui/button';
import { get } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { useTranslation } from 'react-i18next';
import { toast as sonnerToast } from 'sonner';
import { handleReceiveMessage } from '@/utils/helper';
import { useCheckDialog } from '@/hooks/useCheckDialog';
import { handleGetSegmentLog } from '@/store/SegmentLog/slice';

type Props = {
  children: ReactNode;
};

const AppContextProvider = ({ children }: Props) => {
  useCheckDialog();
  const dispatch = useAppDispatch();
  const isDismiss = getStorage('isDismiss') === 'true';
  const { t } = useTranslation();

  const handlePromiseActions = useCallback(() => {
    Promise.all([
      dispatch(getPhoneBought()).unwrap(),
      dispatch(getUsedPhone()).unwrap(),
      dispatch(getLocationProvince()).unwrap(),
      dispatch(getAllAudienceSegment()).unwrap(),
      dispatch(getAllDatasetSegment()).unwrap(),
      dispatch(handleGetSegmentLog()).unwrap()
  ]);
  }, [dispatch]);

  useEffect(() => {
    dispatch(getProfileUser())
      .unwrap()
      .then(() => handlePromiseActions())
      .catch((err: unknown) => err);
  }, [dispatch, handlePromiseActions]);

  useEffect(() => {
    handleReceiveMessage({
      type: 'notification/openNotiDrawer',
      dispatch: (e) => dispatch(setOpenDrawer(e)),
    });
    //   eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleGetList = () => {
    return get<TContactItems>({
      endpoint: ENDPOINTS.contact_list,
      params: {
        page: 1,
        limit: 10,
        order_by: '-date_created,full_name',
        reminder_overdue: true,
      },
    });
  };

  const handleViewOverdue = (data: {
    count: number;
    items: TContactItems[];
    isOverdue: boolean;
  }) => {
    dispatch(setListOverdue(data));
  };

  const dismissNoti = () => {
    setStorage('isDismiss', 'true');
    sonnerToast.dismiss();
  };

  const fetchOverdueMutation = useMutation({
    mutationFn: async () => handleGetList(),
    onSuccess: (res) => {
      const contact: TMultiResponse<TContactItems> = res.data as TMultiResponse<TContactItems>;
      const count = contact.data?.count || 0;
      dispatch(
        setListOverdue({
          count,
          items: contact.data?.items || [],
          isOverdue: false,
        }),
      );
      if (isDismiss || count === 0) {
        return;
      }
      toast({
        status: 'error',
        title: (
          <div className="flex justify-between gap-4">
            <p>
              {t('common.reminder.overDueReminder', {
                count: contact.data?.count || 0,
                symbol: count > 1 ? 's' : '',
              })}
            </p>
            <div className="flex gap-2 items-center">
              <Button
                variant={'ghostError'}
                className="w-fit p-0 h-auto"
                onClick={() =>
                  handleViewOverdue({
                    count: contact.data?.count || 0,
                    items: contact.data?.items || [],
                    isOverdue: true,
                  })
                }
              >
                {t('common.reminder.viewDetail')}
              </Button>
              <Button variant={'ghostError'} className="w-fit p-0 h-auto" onClick={dismissNoti}>
                {t('common.button.dismiss')}
              </Button>
            </div>
          </div>
        ),
      });
    },
    onError: (error: TErrorResponse) => {
      console.log('error', { error });
    },
  });

  useEffect(() => {
    fetchOverdueMutation.mutate();
    //   eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isDismiss]);

  return <>{children}</>;
};
export default AppContextProvider;
