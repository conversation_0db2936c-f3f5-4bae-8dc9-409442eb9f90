export const RESPONSE_MESSAGES = {
  //success
  UPDATE_SUCCESS: 'Update successful',
  CREATE_SUCCESS: 'Created successfully',
  DELETE_SUCCESS: 'Deleted successfully',
  PRINT_SUCCESS: 'Print information updated successfully',
  SCAN_SUCCESS: 'Scan successful',
  UPLOAD_SUCCESS: 'File uploaded successfully',
  //error
  TOO_LARGE_FILE_SIZE: 'Upload failed. The file size is too large',
  ERROR_CONNECTION_SERVER: 'Server connection error. Please try again later!',
  PRINT_FAILED: 'Failed to update print information',
  SCAN_FAILED: 'Scan failed',
  DUPLICATE_PHONE: 'Phone number already exists. Please enter a different number',
  FETCH_ERROR: 'Failed to fetch data',
  ITEM_EXIST: 'Item already exists',
  CREATE_ERROR: 'Creation error',
  UPDATE_ERROR: 'Update error',
  DELETE_ERROR: 'Delete error',
  END_SESSION: 'Session ended',
  CONFIRM_ERROR: 'Confirmation error',
  UPLOAD_ERROR: 'File upload error',
  //
  ERROR_500: '500 error on the server',
  NOT_FOUND: 'Information not found!',
  MULTI_REQUEST: 'The system limits this action to 5 minutes. Please try again later!',
  ERROR: 'An error occurred',
  EXPORT_SUCCESS: 'File export successful',
  EXPORT_ERROR: 'File export failed',
  ERROR_TRY_AGAIN: 'An error occurred. Please try again',
  // push data
  PUSH_DATA_SUCCESS: 'Data pushed successfully',
  PUSH_DATA_ERROR: 'Data push failed',
  IMPORT_SHEET_ERROR: 'Failed to create import sheet',
  // facebook
  SELECT_DEFAULT: 'Successfully set as default account',
  REMOVED_DEFAULT: 'Successfully removed default account',
};
