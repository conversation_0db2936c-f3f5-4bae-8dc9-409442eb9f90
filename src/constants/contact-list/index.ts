export enum CONTACT_STATUS {
  AVAILABLE = 'available',
  USED = 'used',
  SPAM = 'spam',
  EXPIRED = 'expired',
}

export enum KEY_PHONE_BOUGHT {
  select_phone = 'select_phone',
  phone_number = 'phone_number',
  telecom_provider = 'telecom_provider',
  status = 'status',
  date_created = 'date_created',
}
export enum MOBILE_PROVIDER {
  VIETTEL = 'viettel',
  MOBIFONE = 'mobifone',
  VINAPHONE = 'vinaphone',
  VIETNAMOBILE = 'vietnamobile',
  ITELECOM = 'itelecom',
  REDDI = 'reddi',
}
export enum CONTACT_FIELD {
  ID = 'id',
  Name = 'full_name',
  PhoneNumber = 'phone_number',
  Gender = 'gender',
  DOB = 'dob',
  Email = 'email',
  Company = 'company',
  Position = 'position',
  Address = 'person_address',
}

export const LABEL_CONTACT_FIELD = {
  [CONTACT_FIELD.Name]: 'Name',
  [CONTACT_FIELD.PhoneNumber]: 'Phone Number',
  [CONTACT_FIELD.Gender]: 'Gender',
  [CONTACT_FIELD.DOB]: 'Date of Birth',
  [CONTACT_FIELD.Email]: 'Email',
  [CONTACT_FIELD.Company]: 'Company',
  [CONTACT_FIELD.Position]: 'Position',
  [CONTACT_FIELD.Address]: 'Address',
  [CONTACT_FIELD.ID]: 'ID',
};

export enum GENDER {
  NAM = 'nam',
  NU = 'nữ',
  KHAC = 'khác',
  FEMALE = 'female',
  MALE = 'male',
  OTHER = 'other',
}

export const genderOptions = [
  { value: GENDER.MALE, label: 'Male' },
  { value: GENDER.FEMALE, label: 'Female' },
  { value: GENDER.OTHER, label: 'Other' },
];

export enum OVER_DUE_PHONE_BOUGHT {
  ACTIVE = 'active',
  WARNING = 'warning', // Vàng: Cảnh báo gần hết hạn
  EXPIRED = 'expired', // Đỏ: Quá hạn, chỉ cho renew
  STORE = 'store',
}

export const defaultFilter = {
  gender__in: '',
  age__in: '',
  status__in: '',
  dob_month__in: '',
  segment__in: '',
  person_province__in: '',
  search: '',
  frequency: '',
  frequency_min: '',
  frequency_max: '',
};
