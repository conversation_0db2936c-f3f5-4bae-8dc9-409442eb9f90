import { CONTACT_FIELD, CONTACT_STATUS, LABEL_CONTACT_FIELD, MOBILE_PROVIDER } from '.';

export const CONTACT_LABEL = {
  actions: 'Actions',
  report_title: 'Report phone number',
  remove_title: 'Remove phone number',
  report_description:
    'Are you sure you want to report this phone number? This action cannot be undone.',
  remove_description:
    'Are you sure you want to remove this phone number? This action cannot be undone.',
  title: 'Contact List',
  sub_title: 'Easily create and manage campaigns synced with your CRM.',
  description: 'Ultilise CRM360’s build-in calling tools to streamline your outreach.',
  sub_description: 'Get a phone number to start calling.',
  get_phone_number: 'Get phone number',
  get_phone: 'Get phone number',
  switchboard: 'Switchboard',
  search_phone: 'Search phone number',
  phone_number: 'Phone number',
  status: 'Status',
  buy_phone: 'Buy phone number',
  select: 'Select',
};

export const mobileProviderOptions = Object.values(MOBILE_PROVIDER).map((value) => ({
  label: value,
  value,
}));

export const statusOptions = Object.values(CONTACT_STATUS).map((value) => ({
  label: value,
  value,
}));
export const selectCol = Object.values(CONTACT_FIELD).map((value) => ({
  label: LABEL_CONTACT_FIELD[value],
  value,
}));
