import { z } from 'zod';
import { getTypeValue, ZL_CAMPAIGN_KEY } from '.';
import { regex } from '../regex';

const rowSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  content: z.string().min(1, 'Content is required'),
  value: z.string().optional(),
});

const actionButtonSchema = z
  .object({
    type: z.string().min(1, 'Type is required').nonempty(),
    label: z
      .string()
      .min(5, 'Label is required')
      .max(30, "Title's length must be at most 65 characters")
      .nonempty(),
    value: z.string().min(1, 'Value is required').nonempty(),
  })
  .superRefine((data, ctx) => {
    const type = getTypeValue(data.type);

    if (type === 'phone' && !regex.phone.test(data.value)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Invalid phone number',
        path: ['value'],
      });
    } else if (type === 'link' && !regex.url.test(data.value)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Invalid URL',
        path: ['value'],
      });
    }
  });
const logoSchema = z.object({
  type: z.string().min(1, 'Type is required'),
  media_id: z.string().min(1, 'Media id is required'),
  local_url: z.string().min(1, 'Local url is required'),
});

const contentKeysSchema = z.object({
  key: z.string().min(1, 'Key is required').nonempty(),
  value: z.string().min(1, 'Value is required').nonempty(),
  type: z.string().min(1, 'Type is required').nonempty(),
});
export type TContentKeys = z.infer<typeof contentKeysSchema>;

export const zaloTemplateSchema = z.object({
  template_type: z.string().min(1, 'Type is required'),
  template_name: z.string().min(1, 'Template name is required'),
  content: z.string().min(1, 'Content not empty'),
  title: z
    .string()
    .min(9, "Title's length must be at least 9 characters")
    .max(50, "Title's length must be at most 65 characters"),
  actionButton: z.object({
    main: actionButtonSchema,
    sub: actionButtonSchema.optional(),
  }),
  table: z
    .array(rowSchema)
    .min(2, 'Table must have at least 2 rows')
    .max(8, 'Table can have a maximum of 8 rows'),
  logo: z.object({
    dark: logoSchema,
    light: logoSchema,
  }),
  keyError: z.array(z.string()).optional(),
});
export type RowTableType = z.infer<typeof rowSchema>;

export const zaloTempBeforeSchema = zaloTemplateSchema.omit({ table: true }).extend({
  table: z
    .array(
      rowSchema.refine(
        (data) => {
          if (data.content.trim() !== '') {
            return data.value !== null && data.value !== undefined && data.value.trim() !== '';
          }
          return true;
        },
        {
          message: 'Value is required',
          path: ['value'],
        },
      ),
    )
    .min(2, 'Table must have at least 2 rows')
    .max(8, 'Table can have a maximum of 8 rows'),
  note: z
    .string()
    .min(1, 'Note is required')
    .max(400, "Note's length must be at most 65 characters")
    .nonempty(),
  isAcceptTerm: z
    .boolean()
    .default(false)
    .refine((val) => val === true, {
      message: 'You must accept the terms',
    }),
  contentKeys: z.array(contentKeysSchema),
});

export type TZaloTempBeforeSchema = z.infer<typeof zaloTempBeforeSchema>;
export type ZaloTemplate = z.infer<typeof zaloTemplateSchema>;

export const initialTemplate: ZaloTemplate = {
  template_type: 'Preview ZNS',
  table: [],
  template_name: '',
  title: '',
  content: '',
  actionButton: { main: { type: '', label: '', value: '' } },
  logo: {
    dark: { type: '', media_id: '', local_url: '' },
    light: { type: '', media_id: '', local_url: '' },
  },
};

export const initialTemplateBefore: TZaloTempBeforeSchema = {
  ...initialTemplate,
  contentKeys: [],
  note: '',
  isAcceptTerm: false,
};

// zalo campaign

export const zaloCampaignSchema = z
  .object({
    [ZL_CAMPAIGN_KEY.CAMPAIGN_NAME]: z.string().min(1, 'Campaign name is required'),
    [ZL_CAMPAIGN_KEY.TEMPLATE]: z.string().min(1, 'Template name is required'),
    [ZL_CAMPAIGN_KEY.SEGMENT]: z.string().min(1, 'Segment name is required'),
    isSendDate: z.boolean().default(false),
    sendDate: z.string().optional(),
  })
  .refine(
    (data) => {
      if (data.isSendDate && !data.sendDate) {
        return false;
      }
      return true;
    },
    {
      path: ['isSendDate'],
      message: 'Send date is required',
    },
  );

export type TZaloCampaign = z.infer<typeof zaloCampaignSchema>;
