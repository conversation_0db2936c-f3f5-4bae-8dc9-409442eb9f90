import { Option } from '@/types/contactList';
import i18n from '@/config-translation';

export const actionButton = [
  {
    name: i18n.t('common.zaloAds.actionBtn.businessWebsite'),
    price: { main: 0, sub: 100 },
    type: 'link',
    value: '1',
  },
  {
    name: i18n.t('common.zaloAds.actionBtn.call'),
    price: { main: 0, sub: 100 },
    type: 'phone',
    value: '2',
  },
  {
    name: i18n.t('common.zaloAds.actionBtn.oaInfo'),
    price: { main: 0, sub: 100 },
    type: 'link',
    value: '3',
  },
  {
    name: i18n.t('common.zaloAds.actionBtn.zaloMiniApp'),
    price: { main: 0, sub: 100 },
    type: 'link',
    value: '4',
  },
  {
    name: i18n.t('common.zaloAds.actionBtn.productDistribution'),
    price: { main: 400, sub: 500 },
    type: 'link',
    value: '5',
  },
  {
    name: i18n.t('common.zaloAds.actionBtn.otherWeb'),
    price: { main: 300, sub: 400 },
    type: 'link',
    value: '6',
  },
  {
    name: i18n.t('common.zaloAds.actionBtn.applicationPage'),
    price: { main: 400, sub: 500 },
    type: 'link',
    value: '7',
  },
  {
    name: i18n.t('common.zaloAds.actionBtn.otherApplicationPage'),
    price: { main: 600, sub: 700 },
    type: 'link',
    value: '8',
  },
];

export const getTypeValue = (key: string) => {
  return actionButton.find((item) => item.value === key)?.type;
};

export const actionButtonOptions: { main: Option[]; sub: Option[] } = {
  main: actionButton.map((item) => ({
    value: item.value,
    label: `${item.name} (${item.price.main})`,
    type: item.type,
  })),
  sub: actionButton.map((item) => ({
    value: item.value,
    label: `${item.name} (${item.price.sub})`,
    type: item.type,
  })),
};

export const paramFields = [
  { value: 'customer_name', label: "Customer's Name", maxLength: 30, type: 1 },
  { value: 'gender', label: 'Gender', maxLength: 5, type: 8 },
  { value: 'dob', label: 'DOB', maxLength: 30, type: 5 },
  { value: 'email', label: 'Email', maxLength: 50, type: 7 },
  { value: 'company', label: 'Company', maxLength: 200, type: 9 },
  { value: 'position', label: 'Position', maxLength: 30, type: 6 },
  { value: 'address', label: 'Address', maxLength: 200, type: 3 },
  { value: 'province', label: 'Province', maxLength: 30, type: 4 },
];

export const getTypeParam = (value: string) => {
  return paramFields.find((param) => param.value === value)?.type;
};

export const getParamField = (key: string) => {
  return paramFields.find((param) => param.value === key);
};

export const paramFieldsOptions = paramFields.map((item) => ({
  value: item.value,
  label: item.label + `(${item.maxLength})`,
}));

export const templateStatus = [
  { value: 'pending', label: 'Pending' },
  { value: 'processing', label: 'Processing' },
  { value: 'completed', label: 'Completed' },
];

export enum ZL_CAMPAIGN_KEY {
  CAMPAIGN_NAME = 'campaign_name',
  TEMPLATE = 'template',
  SEGMENT = 'segment',
}
export const TERM_URL = 'https://zalo.cloud/terms';
