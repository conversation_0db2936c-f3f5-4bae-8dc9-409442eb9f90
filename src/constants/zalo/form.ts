import { TemplateData } from '@/types/zalo';
import { getTypeParam } from '.';
import { TZaloTempBeforeSchema } from './validate';

export const customForm = (object: TZaloTempBeforeSchema) => {
  const rowsParam = object.table.map((row) => ({
    sample_value: row.value,
    name: row.content,
    type: getTypeParam(row.content)?.toString(),
  }));

  const contentParam = object.contentKeys.map((content) => ({
    name: content.key,
    sample_value: content.value,
    type: getTypeParam(content.type),
  }));

  const actionButton =
    object.actionButton.sub || object.actionButton.sub !== undefined
      ? [
          {
            content: object.actionButton.main.value,
            type: object.actionButton.main.type,
            title: object.actionButton.main.label,
          },
          {
            content: object.actionButton.sub.value,
            type: object.actionButton.sub.type,
            title: object.actionButton.sub.label,
          },
        ]
      : [
          {
            content: object.actionButton.main.value,
            type: object.actionButton.main.type,
            title: object.actionButton.main.label,
          },
        ];

  return {
    template_name: object.template_name,
    template_type: '1',
    tag: '1',
    layout: {
      header: {
        components: [
          {
            LOGO: {
              light: {
                type: 'IMAGE',
                media_id: object.logo?.light?.media_id,
              },
              dark: {
                type: 'IMAGE',
                media_id: object.logo?.dark?.media_id,
              },
            },
          },
        ],
      },
      body: {
        components: [
          { TITLE: { value: object.title } },
          { PARAGRAPH: { value: object.content } },
          {
            TABLE: {
              rows: object.table.map((row) => ({
                value: `${row.content}`,
                title: row.title,
                // row_type: getTypeParam(row.content),
              })),
            },
          },
        ],
      },
      footer: {
        components: [{ BUTTONS: { items: actionButton } }],
      },
    },
    note: object.note,
    params: [...rowsParam, ...contentParam],
    tracking_id: 'abc123',
  };
};

export const fCustomForm = (payload: TemplateData): TZaloTempBeforeSchema => {
  const buttonFooter =
    payload.layout.footer?.components.find((comp) => 'BUTTONS' in comp)?.BUTTONS?.items || [];
  const logoFooter = payload.layout.header?.components.find((comp) => 'LOGO' in comp)?.LOGO;

  return {
    template_name: payload.template_name,
    template_type: '1', // Add template_type
    title: payload.layout.body?.components.find((comp) => 'TITLE' in comp)?.TITLE?.value || '',
    content:
      payload.layout.body?.components.find((comp) => 'PARAGRAPH' in comp)?.PARAGRAPH?.value || '',
    table:
      payload.layout.body?.components
        .find((comp) => 'TABLE' in comp)
        ?.TABLE?.rows.map((row) => ({
          content: row.value.replace(/<|>/g, ''), // Remove angle brackets
          title: row.title,
          value:
            payload.params?.find((param) => param.name === row.value.replace(/<|>/g, ''))
              ?.sample_value || '',
        })) || [],
    contentKeys:
      payload.params?.map((param) => {
        const newItem = {
          key: param.name,
          value: param.sample_value,
          type: param.type.toString(),
        };
        return newItem;
      }) || [],
    actionButton: {
      main: {
        value: buttonFooter[0]?.content || '',
        label: buttonFooter[0]?.title || '',
        type:
          payload.layout.footer?.components
            .find((comp) => 'BUTTONS' in comp)
            ?.BUTTONS?.items[0]?.type.toString() || '',
      },
      sub: buttonFooter[1]
        ? {
            value: buttonFooter[1]?.content || '',
            type:
              payload.layout.footer?.components
                .find((comp) => 'BUTTONS' in comp)
                ?.BUTTONS?.items[1]?.type.toString() || '',
            label: buttonFooter[1]?.title || '',
          }
        : undefined,
    },
    logo: {
      dark: {
        type: logoFooter?.dark?.type || '',
        media_id: logoFooter?.dark?.media_id || '',
        local_url: '',
      },
      light: {
        type: logoFooter?.light?.type || '',
        media_id: logoFooter?.light?.media_id || '',
        local_url: '',
      },
    },
    isAcceptTerm: true,
    note: payload.note,
  };
};
