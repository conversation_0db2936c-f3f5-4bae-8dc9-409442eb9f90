export enum FILTER_KEY {
  STATUS = 'status_in',
  DATE_FROM = 'date_created_from',
  DATE_TO = 'date_created_to',
  SEARCH = 'search',
  PAGE = 'page',
  LIMIT = 'limit',
}

export enum CAMPAIGN_KEY {
  CAMPAIGN_NAME = 'name',
  SENDER = 'sender',
  RECIPIENTS = 'recipients',
  DESIGN = 'design',
  SUBJECT = 'subject',
  ADDITIONAL_SETTING = 'additional_setting',
  PREVIEW_TEXT = 'preview_text',
  REPLY_TO = 'reply_to',
  FOOTER = 'footer',
  HEADER = 'header',
  UTM_CAMPAIGN = 'utm_campaign',
  UPDATE_FORM_ID = 'update_form_id',
  ATTACHMENT_URL = 'attachment_url',
  TAG = 'tag',
  EMAIL_EXPIRATION_DATE = 'email_expiration_date',
  HTML_CONTENT = 'html_content',
  HTML_URL = 'html_url',
  TEMPLATE_ID = 'template_id',
  SCHEDULED_AT = 'scheduled_at',
  MIRROR_ACTIVE = 'mirror_active',
  IGNORE_CUSTOM_SENDER = 'ignore_custom_list_sender',
  GA_ACTIVE = 'ga_active',
  UNSUBSCRIBE = 'unsubscribe',
  PROFILE_UPDATE = 'profile_update_form',
  TO_FIELD = 'to_field',
}

export enum UNSUBSCRIBE_FORM_KEY {
  NAME = 'unsubscribe_form_name',
  ID = 'unsubscribe_form_id',
}

export enum EXPIRATION_DATE_KEY {
  DURATION = 'duration',
  UNIT = 'unit',
}
export enum SENDER_KEY {
  EMAIL_ADDRESS = 'email',
  NAME = 'name',
  ID = 'id',
}
export enum RECIPIENTS_KEY {
  SEND_TO = 'segmentIds',
  DONT_SEND = 'dont_send',
}
export enum SUBJECT_KEY {
  SUBJECT_LINE = 'subject_line',
  PREVIEW_TEXT = 'preview_text',
}
export enum ADDITIONAL_SETTING_KEY {
  PERSONALIZATION = 'personalization',
  SENDING_TRACKING = 'sending_tracking',
  SUBSCRIPTION = 'subscription',
}

export const ItemDesignTypes = {
  TOOLBAR_ITEM: 'toolbarItem',
  CONTENT_BLOCK: 'contentBlock',
};

export enum ExpUnit {
  Days = 'days',
  Weeks = 'weeks',
  Months = 'months',
}

export const borderStyle = [
  { label: 'Dashed', value: 'dashed' },
  { label: 'Dotted', value: 'dotted' },
  { label: 'Double', value: 'double' },
  { label: 'Groove', value: 'groove' },
  { label: 'Hidden', value: 'hidden' },
  { label: 'Inset', value: 'inset' },
  { label: 'None', value: 'none' },
  { label: 'Outset', value: 'outset' },
  { label: 'Ridge', value: 'ridge' },
  { label: 'Solid', value: 'solid' },
];

export const fontFamily = [
  { label: 'Auto', value: 'auto' },
  { label: 'Cursive', value: 'cursive' },
  { label: 'Emoji', value: 'emoji' },
  { label: 'Fangsong', value: 'fangsong' },
  { label: 'Fantasy', value: 'fantasy' },
  { label: 'Math', value: 'math' },
  { label: 'Monospace', value: 'monospace' },
  { label: 'None', value: 'none' },
  { label: 'Sans-serif', value: 'sans-serif' },
  { label: 'Serif', value: 'serif' },
  { label: 'System-ui', value: 'system-ui' },
  { label: 'Ui-monospace', value: 'ui-monospace' },
  { label: 'Ui-rounded', value: 'ui-rounded' },
  { label: 'Ui-sans-serif', value: 'ui-sans-serif' },
  { label: 'Ui-serif', value: 'ui-serif' },
  { label: '-Webkit-body', value: '-webkit-body' },
  { label: 'Inherit', value: 'inherit' },
  { label: 'Initial', value: 'initial' },
  { label: 'Revert', value: 'revert' },
  { label: 'Revert-layer', value: 'revert-layer' },
  { label: 'Unset', value: 'unset' },
];

export const iconSize = [
  { label: 'Tiny', value: '16' },
  { label: 'Small', value: '32' },
  { label: 'Medium', value: '48' },
  { label: 'Large', value: '64' },
];

export const iconTheme = [
  { label: 'Light', value: 'light' },
  { label: 'Dark', value: 'dark' },
  { label: 'Colored', value: 'colored' },
];
export const iconStyle = [
  { label: 'Original', value: 'original' },
  { label: 'Rounded', value: 'rounded' },
  { label: 'Squared', value: 'squared' },
];
