import { z } from 'zod';
import { regex } from '../regex';
import {
  ADDITIONAL_SETTING_KEY,
  CAMPAIGN_KEY,
  SUBJECT_KEY,
  SENDER_KEY,
  RECIPIENTS_KEY,
  EXPIRATION_DATE_KEY,
  ExpUnit,
  UNSUBSCRIBE_FORM_KEY,
} from '.';

export const senderSchema = z.object({
  [SENDER_KEY.EMAIL_ADDRESS]: z
    .string()
    .email('Invalid email address')
    .regex(regex.email, 'Invalid email address'),
  [SENDER_KEY.NAME]: z.string().min(1, 'Name is required'),
  [SENDER_KEY.ID]: z.string().nonempty('Sender ID is required'),
});

const emailExpSchema = z.object({
  isSelect: z.boolean().default(false),
  [EXPIRATION_DATE_KEY.DURATION]: z.number().int().min(1, 'Duration must be greater than 0'),
  [EXPIRATION_DATE_KEY.UNIT]: z
    .enum([ExpUnit.Days, ExpUnit.Weeks, ExpUnit.Months])
    .default(ExpUnit.Days),
});

export const recipientsSchema = z.object({
  [RECIPIENTS_KEY.SEND_TO]: z.string().min(1, 'Send to is required'),
  [RECIPIENTS_KEY.DONT_SEND]: z.boolean().optional(),
});
export const subjectSchema = z.object({
  [SUBJECT_KEY.SUBJECT_LINE]: z.string().min(1, 'Subject line is required'),
  [SUBJECT_KEY.PREVIEW_TEXT]: z.string().min(1, 'Preview text is required').optional(),
});

export const additionalSettingSchema = z.object({
  [ADDITIONAL_SETTING_KEY.PERSONALIZATION]: z.boolean().optional(),
  [ADDITIONAL_SETTING_KEY.SENDING_TRACKING]: z.boolean().optional(),
  [ADDITIONAL_SETTING_KEY.SUBSCRIPTION]: z.boolean().optional(),
});

export const differentReplySchema = z
  .object({
    isSelect: z.boolean().default(false),
    value: z.string().optional(),
  })
  .refine(
    (data) => {
      return !data.isSelect || (data.value !== null && data.value !== undefined);
    },
    { message: 'Reply to is required', path: ['value'] },
  );
export const utmCampaignSchema = z.object({
  isSelect: z.boolean().default(false),
  value: z.string().optional(),
  [CAMPAIGN_KEY.GA_ACTIVE]: z.boolean().optional().default(false),
});

export const attachmentSchema = z.object({
  isSelect: z.boolean().default(false),
  value: z.string().optional(),
});

export const tagSchema = z.object({
  isSelect: z.boolean().default(false),
  value: z.string().optional(),
});
const headerSchema = z.object({
  isSelect: z.boolean().default(false),
  value: z.string().optional(),
});
const footerSchema = z.object({
  isSelect: z.boolean().default(false),
  value: z.string().optional(),
});

const unsubscribeSchema = z.object({
  isSelect: z.boolean().default(false),
  [UNSUBSCRIBE_FORM_KEY.NAME]: z.string().optional(),
  [UNSUBSCRIBE_FORM_KEY.ID]: z.string().optional(),
});

const profileUpdateSchema = z.object({
  isSelect: z.boolean().default(false),
  value: z.string().optional(),
});

const toFieldSchema = z.object({
  isSelect: z.boolean().default(false),
  value: z.string().optional(),
});

export const emailCampaignSchema = z.object({
  [CAMPAIGN_KEY.CAMPAIGN_NAME]: z.string().min(1, 'Campaign name is required'),
  [CAMPAIGN_KEY.SENDER]: senderSchema,
  [CAMPAIGN_KEY.HTML_CONTENT]: z.string().min(1, 'HTML content is required'),
  [CAMPAIGN_KEY.PREVIEW_TEXT]: z.string().min(1, 'Preview text is required').optional(),
  [CAMPAIGN_KEY.EMAIL_EXPIRATION_DATE]: emailExpSchema,
  [CAMPAIGN_KEY.SUBJECT]: z.string().min(1, 'Subject is required'),
  [CAMPAIGN_KEY.FOOTER]: footerSchema,
  [CAMPAIGN_KEY.HEADER]: headerSchema,
  [CAMPAIGN_KEY.RECIPIENTS]: recipientsSchema,
  [CAMPAIGN_KEY.REPLY_TO]: differentReplySchema,
  [CAMPAIGN_KEY.UTM_CAMPAIGN]: utmCampaignSchema,
  [CAMPAIGN_KEY.MIRROR_ACTIVE]: z.boolean().optional().default(false),
  [CAMPAIGN_KEY.ATTACHMENT_URL]: attachmentSchema,
  [CAMPAIGN_KEY.TAG]: tagSchema,
  [CAMPAIGN_KEY.IGNORE_CUSTOM_SENDER]: z.boolean().optional().default(false),
  [CAMPAIGN_KEY.UNSUBSCRIBE]: unsubscribeSchema,
  [CAMPAIGN_KEY.PROFILE_UPDATE]: profileUpdateSchema,
  [CAMPAIGN_KEY.TO_FIELD]: toFieldSchema,
});

export type TEmailCampaign = z.infer<typeof emailCampaignSchema>;
export type TSender = z.infer<typeof senderSchema>;
export type TSubject = z.infer<typeof subjectSchema>;
export type TAdditionalSetting = z.infer<typeof additionalSettingSchema>;
export type TRecipients = z.infer<typeof recipientsSchema>;

export const createSenderSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email address').regex(regex.email, 'Invalid email address'),
});
export type TCreateSender = z.infer<typeof createSenderSchema>;
