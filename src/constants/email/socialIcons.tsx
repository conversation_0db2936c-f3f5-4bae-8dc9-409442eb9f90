import { Checkbox } from '@/components/ui/checkbox';
import {
  RiFacebookFill,
  Ri<PERSON>nstagramFill,
  RiTwitterFill,
  RiLinkedinFill,
  RiYoutubeFill,
  RiPinterestFill,
  RiTiktokFill,
  RiRedditFill,
  RiD<PERSON>rdFill,
  RiTelegramFill,
  R<PERSON><PERSON>hatsappFill,
  RiSnapchatFill,
  RiTwitchFill,
  RiGithubFill,
  R<PERSON><PERSON>ribbbleFill,
  RiBehanceFill,
  RiMediumFill,
  RiSlackFill,
  RiWechatFill,
  RiVimeoFill,
} from '@remixicon/react';
import { ColumnDef } from '@tanstack/react-table';

type IconComponent = typeof RiFacebookFill;

export type TSocialList = { name: string; icon: IconComponent; color: string };

export const socialIcons = (): TSocialList[] => [
  { name: 'Facebook', icon: RiFacebookFill, color: '#1877F2' },
  { name: 'Instagram', icon: RiInstagramFill, color: '#E4405F' },
  { name: 'Twitter', icon: RiTwitterFill, color: '#1DA1F2' },
  { name: 'LinkedIn', icon: RiLinkedinFill, color: '#0A66C2' },
  { name: 'YouTube', icon: RiYoutubeFill, color: '#FF0000' },
  { name: 'Pinterest', icon: RiPinterestFill, color: '#BD081C' },
  { name: 'TikTok', icon: RiTiktokFill, color: '#000000' },
  { name: 'Reddit', icon: RiRedditFill, color: '#FF4500' },
  { name: 'Discord', icon: RiDiscordFill, color: '#5865F2' },
  { name: 'Telegram', icon: RiTelegramFill, color: '#26A5E4' },
  { name: 'WhatsApp', icon: RiWhatsappFill, color: '#25D366' },
  { name: 'Snapchat', icon: RiSnapchatFill, color: '#FFFC00' },
  { name: 'Twitch', icon: RiTwitchFill, color: '#9146FF' },
  { name: 'GitHub', icon: RiGithubFill, color: '#181717' },
  { name: 'Dribbble', icon: RiDribbbleFill, color: '#EA4C89' },
  { name: 'Behance', icon: RiBehanceFill, color: '#1769FF' },
  { name: 'Medium', icon: RiMediumFill, color: '#000000' },
  { name: 'Slack', icon: RiSlackFill, color: '#4A154B' },
  { name: 'WeChat', icon: RiWechatFill, color: '#07C160' },
  { name: 'Vimeo', icon: RiVimeoFill, color: '#1AB7EA' },
];

export const socialIconsCol = (): ColumnDef<TSocialList>[] => {
  return [
    {
      accessorKey: 'icon',
      header: '',
      cell: ({ row }) => (
        <div className="flex items-center gap-3 px-4">
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            className="w-4 h-4"
          />
          <row.original.icon color={row.original.color} /> {row.original.name}
        </div>
      ),
    },
  ];
};
