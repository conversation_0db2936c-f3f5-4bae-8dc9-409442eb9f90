import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import translationEN from '@/locale/en/translation';

const resources = {
  en: { translation: translationEN },
};

i18n.use(initReactI18next).init({
  lng: 'en',
  debug: false,
  resources,
});

export default i18n;

export const localeFormat = {
  en: 'en-US',
  de: 'de-DE',
  ja: 'ja-JP',
  zh: 'zh-CN',
  vi: 'vi-VN',
  ko: 'ko-KR',
};
