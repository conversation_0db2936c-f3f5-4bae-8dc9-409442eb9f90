import { TSegmentData } from '@/types/segment';
import { APIBigConfig, APIConfig } from '.';
import { ENDPOINTS } from './endpoints';
import { get } from '@/apis/apiHelper';
import { TSegmentDetail } from '@/types/contactList';

const getList = async (): Promise<TSegmentData | unknown[] | undefined> => {
  const res = await get({
    endpoint: ENDPOINTS.segments.getList,
    params: {
      page: 1,
      limit: 50,
      order_by: '-date_created',
    },
  });
  return res?.data?.data as TSegmentData | unknown[] | undefined;
};

const getDetail = async (id: string): Promise<TSegmentDetail> => {
  const res = await APIConfig().get(ENDPOINTS.segments.detail + `${id}/`);
  return res.data.data;
};

const createSegment = async (payload: { [key: string]: unknown }) => {
  const res = await APIConfig().post(ENDPOINTS.segments.create, payload);
  return res.data.data;
};

/* eslint-disable @typescript-eslint/no-explicit-any */
const getSegmentLogContact = async <T = any>() => {
  try {
    const response = await APIBigConfig().get<T>(`contact-crm/segments/logs/`);
    return response.data;
  } catch (error) {
    return { data: null, error };
  }
};

export const segmentApi = { getList, createSegment, getDetail, getSegmentLogContact };
