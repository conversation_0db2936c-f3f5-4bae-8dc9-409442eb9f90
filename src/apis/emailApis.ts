import { APIConfig } from '.';
import { ENDPOINTS } from './endpoints';

const createSender = async (name: string, email: string) => {
  const res = await APIConfig().post(ENDPOINTS.email.createSender, { email, name });
  return res.data;
};

const verifySender = async (email_sender_id: string, otp: string) => {
  const res = await APIConfig().post(ENDPOINTS.email.verifySender(email_sender_id), {
    otp: Number(otp),
  });
  return res.data;
};

const getOtpSender = async () => {
  const res = await APIConfig().get(ENDPOINTS.email.getOtpSender);
  return res.data;
};

const getSenderDetail = async (sender_id: string) => {
  const res = await APIConfig().get(ENDPOINTS.email.getSenderDetail(sender_id));
  return res.data;
};

const deleteSender = async (sender_id: string) => {
  const res = await APIConfig().delete(ENDPOINTS.email.deleteSender(sender_id));
  return res.data;
};

const updateSender = async (sender_id: string, payload: { name: string; is_primary?: boolean }) => {
  const res = await APIConfig().patch(ENDPOINTS.email.updateSender(sender_id), payload);
  return res.data;
};

export const emailApis = {
  createSender,
  verifySender,
  getOtpSender,
  deleteSender,
  getSenderDetail,
  updateSender,
};
