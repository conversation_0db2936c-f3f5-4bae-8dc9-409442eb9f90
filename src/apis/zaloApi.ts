import { TOauth } from '@/pages/Zalo/Context/OauthContext';
import { TCampaignPayload, Template, TFilterZalo } from '@/types/zalo';
import { APIConfig } from '.';
import { ENDPOINTS } from './endpoints';

const getListOauth = async (): Promise<{
  items: TOauth[];
  count: number;
}> => {
  const res = await APIConfig().get(ENDPOINTS.zalo['oa']);
  return res.data.data;
};
const getZaloOauthURL = async (redirect_uri: string) => {
  const res = await APIConfig().post(
    ENDPOINTS.zalo[''],
    {},
    {
      params: { redirect_uri },
      withCredentials: true,
    },
  );
  return res.data;
};

const uploadImage = async (oa_id: string, payload: { file: File }) => {
  const formData = new FormData();
  formData.append('file', payload.file);
  const res = await APIConfig().post(ENDPOINTS.zalo.uploadImage(oa_id), formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return res.data.data.data;
};

const createTemplate = async (oa_id: string, payload: { [key: string]: unknown }) => {
  const res = await APIConfig().post(ENDPOINTS.zalo.createTemp(oa_id), payload);
  return res.data;
};
const getListTemp = async (
  oa_id: string,
  payload?: Partial<TFilterZalo>,
): Promise<{ count: number; items: Template[] }> => {
  const res = await APIConfig().get(ENDPOINTS.zalo['getList'](oa_id), {
    params: {
      ...payload,
      order_by: '-date_created',
    },
  });
  return res.data.data;
};

const logoutZalo = async (oa_id: string) => {
  const res = await APIConfig().patch(ENDPOINTS.zalo['logout'](oa_id), {}, { params: { oa_id } });
  return res.data;
};
const createCampaign = async (oa_id: string, payload: TCampaignPayload) => {
  const res = await APIConfig().post(ENDPOINTS.zalo.createCampaign(oa_id), payload);
  return res.data;
};

const getListCampaign = async (oa_id: string, payload: TFilterZalo) => {
  const res = await APIConfig().get(ENDPOINTS.zalo.getListCampaign(oa_id), {
    params: {
      ...payload,
      order_by: '-date_created',
    },
  });
  return res.data.data;
};

const getTemplateDetail = async ({ oa_id, temp_id }: { oa_id: string; temp_id: string }) => {
  const res = await APIConfig().get(ENDPOINTS.zalo['getTempDetail']({ oa_id, temp_id }));
  return res.data.data;
};

const updateTemplateDetail = async ({
  oa_id,
  payload,
}: {
  oa_id: string;
  payload: { [key: string]: unknown };
}) => {
  const res = await APIConfig().post(ENDPOINTS.zalo['updateTempDetail'](oa_id), payload);
  return res.data;
};

const deleteTemplate = async (oa_id: string, template_id: string) => {
  const res = await APIConfig().post(ENDPOINTS.zalo['deleteTemp'](oa_id, template_id));
  return res.data;
};

export const zaloApi = {
  getTemplateDetail,
  getListOauth,
  getZaloOauthURL,
  logoutZalo,
  createTemplate,
  uploadImage,
  getListTemp,
  createCampaign,
  getListCampaign,
  updateTemplateDetail,
  deleteTemplate,
};
