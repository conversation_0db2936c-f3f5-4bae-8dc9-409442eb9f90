/* eslint-disable @typescript-eslint/no-explicit-any */
import { APIConfig } from '@/apis/index';

const BASE_URL = import.meta.env.REACT_APP_API_URL;
const createContact = async <T>({ endpoint = '', params }: { params?: any; endpoint?: string }) => {
  return await APIConfig(BASE_URL).post<T>(endpoint, params);
};
const updateContact = async <T>({ endpoint = '', params }: { params?: any; endpoint?: string }) => {
  return await APIConfig(BASE_URL).patch<T>(endpoint, params);
};

const getContactLimit = async <T>({ endpoint = '' }: { params?: any; endpoint?: string })=>{
  return await APIConfig(BASE_URL).get<T>(endpoint);
}

export const contactApi = { createContact, updateContact, getContactLimit };
