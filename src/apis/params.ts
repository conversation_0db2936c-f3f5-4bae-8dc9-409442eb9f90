/* eslint-disable @typescript-eslint/no-explicit-any */

/* eslint-disable @typescript-eslint/no-unused-expressions */
import axios from 'axios';
import map from 'lodash/map';
import omit from 'lodash/omit';
import pick from 'lodash/pick';
import { isObject } from 'lodash';
import isArray from 'lodash/isArray';
import isBoolean from 'lodash/isBoolean';
import { TSelectOption } from '@/types/select';
import { TParams } from '@/types/table';

const { CancelToken } = axios;

export const convertCancelToken = (params: any) => {
  const cancelToken = params?.cancelToken ? params?.cancelToken : new CancelToken(() => {});
  delete params?.cancelToken;
  delete params?.cancelRequest;

  return { cancelToken, paramsNoneCancelToken: params };
};

const ignoreKeys: string[] = [];

export const formatParamsToURLUtil = (keyFilter: string[], params?: any) => {
  const paramsURL = new URLSearchParams();
  let reqPar = '';
  for (reqPar in params) {
    if (!ignoreKeys?.includes(reqPar)) {
      if (params[reqPar]) {
        if (params[reqPar] === 'all') return;
        if (keyFilter.includes(reqPar)) {
          params[reqPar].length > 0 &&
            isArray(params[reqPar]) &&
            params[reqPar].map((item: string) => {
              if (item === 'all') return item;
              item === 'none' ? paramsURL.append(reqPar, 'null') : paramsURL.append(reqPar, item);
              return item;
            });
        } else {
          paramsURL.append(reqPar, params[reqPar]);
        }
      }
    }
  }
  return paramsURL;
};

export const formatParamsToSkycomServerUtil = (keyFilter: string[], params?: any) => {
  const paramsURL = new URLSearchParams();
  let reqPar = '';
  for (reqPar in params) {
    if (params[reqPar]) {
      if (keyFilter.includes(reqPar)) {
        if (isArray(params[reqPar]) && params[reqPar].length) {
          const transferParams = [...params[reqPar]];
          const noneValueIdx = transferParams?.findIndex(
            (item: string | number) => item === 'none',
          );
          if (noneValueIdx >= 0) {
            transferParams.splice(noneValueIdx, 1);
          }
          paramsURL.append(reqPar, transferParams.join(','));
        }
      } else {
        paramsURL.append(reqPar, params[reqPar]);
      }
    }
  }
  return paramsURL;
};

export const formatParamsUtilMore = (
  keyFilters: string[],
  params?: any,
  keyIgnores: string[] = ['trackingDateValue', 'dateValue'],
) => {
  const paramsURL = new URLSearchParams();
  let reqPar = '';
  for (reqPar in omit(params, keyIgnores)) {
    if (reqPar === 'page') {
      paramsURL.append('page', params.page);
    } else if (reqPar === 'offset') {
      paramsURL.append('offset', params.offset);
    } else if (params[reqPar] || isBoolean(params[reqPar])) {
      if (keyFilters.includes(reqPar)) {
        params[reqPar].length > 0 && isArray(params[reqPar])
          ? params[reqPar].map((item: string) => {
              item === 'none' ? paramsURL.append(reqPar, 'null') : paramsURL.append(reqPar, item);
              return item;
            })
          : paramsURL.append(reqPar, params[reqPar]);
      } else {
        paramsURL.append(reqPar, params[reqPar]);
      }
    }
  }
  return paramsURL;
};

export const revertFromQueryForSelector = (params?: any, defaultValue = 'all') => {
  if (typeof params === 'string' || typeof params === 'number') {
    return params;
  }
  return Array.isArray(params)
    ? map(params, (element) => (element === 'null' ? 'null' : element))
    : defaultValue;
};

/**
 *
 * @param objParams old object params
 * @param arrItemSelect
 * @returns
 */
export const chooseParams = (objParams: any, arrItemSelect: string[] = []) => {
  let newParams: any = {};
  const newArrSelect = ['page', 'limit', 'ordering', ...arrItemSelect];

  for (const keyObj in objParams) {
    if (
      !newArrSelect.includes(keyObj) ||
      ['all', ''].includes(objParams[keyObj]) ||
      (Array.isArray(objParams[keyObj]) && !objParams[keyObj].length)
    ) {
      newParams = { ...newParams };
    } else {
      newParams = { ...newParams, [keyObj]: objParams[keyObj] };
    }
  }

  return newParams || {};
};

export const filterParams = (keys: string[], params?: any) => {
  const result = pick(params, keys);
  return result;
};

export const handleDeleteParam = (
  params: TParams,
  option: { key: string; value: string | number },
  setParams?: (params: TParams) => void,
) => {
  if (params?.[option.key as keyof TParams]) {
    const isArray = Array.isArray(params?.[option.key as keyof TParams]);
    if (isArray) {
      const filterForArray = (
        params?.[option.key as keyof TParams] as unknown as TSelectOption[]
      )?.filter((item) => item.value.toString() !== option.value.toString());

      setParams?.({
        ...params,
        [option.key]: filterForArray.length > 0 ? filterForArray : undefined,
      });
    } else {
      setParams?.({
        ...params,
        [option.key]: undefined,
      });
    }
  }
};

export const clearParamsVar = (keysFilter: string[], params: any) => {
  const clearParams = { ...params };
  map(keysFilter, (item) => {
    if (clearParams?.[item]) {
      clearParams[item] = undefined;
    }
  });
  return clearParams;
};

export const handleParamsHeaderFilter = (params: any, arrTakeValue: string[]) =>
  Object.keys(params).length
    ? Object.keys(params).reduce(
        (prevObj, current) =>
          arrTakeValue.includes(current)
            ? {
                ...prevObj,
                [current]: params[current],
              }
            : prevObj,
        {},
      )
    : {};

export const formatParamsUtil = (
  keyFilters: string[],
  params?: TParams,
  keyIgnores: string[] = [],
) => {
  const paramsURL = new URLSearchParams();
  let reqPar = '';
  if (params) {
    for (reqPar in omit(params, keyIgnores)) {
      const value = params[reqPar as keyof typeof params] as any;
      if (value === 'all') {
        continue;
      } else if (reqPar === 'page') {
        const page = value as string;
        paramsURL.append('page', page);
      } else if (reqPar === 'offset') {
        const offset = value as string;
        paramsURL.append('offset', offset);
      } else if (reqPar === 'dimensions') {
        const dimensions = value as string[];
        const transformParams = transformParamsPivotFields(dimensions);
        paramsURL.append('dimensions', JSON.stringify(transformParams));
      } else if (reqPar === 'metrics') {
        const metrics = value as string;
        paramsURL.append('metrics', JSON.stringify(metrics));
      } else if (value || isBoolean(value)) {
        if (keyFilters.includes(reqPar)) {
          isArray(value) && value.length > 0
            ? value.map((item) => {
                if (item === undefined) {
                  return item;
                }
                if (item === 'all') return item;
                if (item === 'none') paramsURL.append(reqPar, 'null');
                else if (isObject(item)) {
                  if ((item as TSelectOption).value === 'all') {
                    return item;
                  }
                  paramsURL.append(reqPar, (item as TSelectOption).value?.toString());
                  return item;
                }
                paramsURL.append(reqPar, item);
                return item;
              })
            : paramsURL.append(reqPar, value as string);
        } else {
          paramsURL.append(reqPar, value as string);
        }
      }
    }
  }
  return paramsURL;
};

function transformParamsPivotFields(fields: string[]) {
  return fields.map((field) => {
    const match = field.match(/^(.*?)__/);

    return match ? match[1] : field;
  });
}
