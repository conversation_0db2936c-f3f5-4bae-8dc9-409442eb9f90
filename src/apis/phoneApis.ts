import { APIConfig } from '.';
import { TPhoneBought, TPhoneBoughtResponse } from '@/types/contactList';
import { ENDPOINTS } from './endpoints';
import { updatePhoneBought } from '@/store/phoneBought/phoneBoughtSlice';
import { TBaseResponse } from '@/types/ResponseApi';
import { crmStore } from '@/store';
import { CONTACT_STATUS } from '@/constants/contact-list';

const getPhones = async (params?: { [key: string]: unknown }) => {
  const res = await APIConfig().get<TPhoneBoughtResponse>(ENDPOINTS.hotline, {
    params,
  });
  return res.data.data || { count: 0, items: [] };
};

const buyPhones = async (data: TPhoneBought[]) => {
  try {
    const res = await APIConfig().patch<TBaseResponse<TPhoneBought[]>>(ENDPOINTS.add_phones, {
      hotline_ids: data.map((item) => item.id),
    });
    crmStore.dispatch(updatePhoneBought(res.data.data));
    return res.data;
  } catch (error) {
    throw error;
  }
};

const getPhoneBought = async (params?: { [key: string]: unknown }) => {
  try {
    const res = await APIConfig().get<TPhoneBoughtResponse>(ENDPOINTS.hotline_bought, {
      params: params,
    });
    return res.data.data || { count: 0, items: [] };
  } catch (error) {
    throw error;
  }
};

const choosePhone = async (hotline_id: string) => {
  try {
    const res = await APIConfig().patch<TBaseResponse<TPhoneBought>>(
      ENDPOINTS.using_hotline + `${hotline_id}/`,
    );
    return res.data;
  } catch (error) {
    throw error;
  }
};
const reportPhone = async ({ id }: { id: string }) => {
  const res = await APIConfig().patch(ENDPOINTS.hotline + `${id}/`, {
    status: CONTACT_STATUS.SPAM,
  });
  return res.data.data;
};

const cancelRenewal = async (hotline_id: string) => {
  const res = await APIConfig().patch(ENDPOINTS.cancelRenewalHotline(hotline_id));
  return res.data;
};

const updateRenewal = async (hotline_id: string, is_renewal: boolean) => {
  const res = await APIConfig().patch(ENDPOINTS.updateRenewalHotline(hotline_id), {
    is_renewal,
  });
  return res.data;
};

export const phoneApis = {
  updateRenewal,
  getPhones,
  buyPhones,
  getPhoneBought,
  choosePhone,
  reportPhone,
  cancelRenewal,
};
