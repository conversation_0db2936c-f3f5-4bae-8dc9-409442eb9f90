import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

import { RESPONSE_MESSAGES } from '@/constants/messages/response.message';
import {
  TCancelRequest,
  TNetworkError,
  CServerError,
  CValidationError,
  ErrorType,
} from '@/types/ResponseApi';
import {
  ACCESS_TOKEN,
  deleteAllStorages,
  getAccessToken,
  getRefreshToken,
  setStorage,
} from '@/utils/asyncStorage';
import { handleResponseErrorMessage } from '@/utils/handleError';

import { ERROR_STATUS } from '@/constants/httpStatusCode';
import { toast } from '@/hooks/use-toast';
import { VALIDATION_MESSAGE } from '@/constants/messages/validate.message';
import { ENDPOINTS } from './endpoints';

const MAIN_APP_URL = import.meta.env.REACT_APP_MAIN_APP_URL;

let service: AxiosInstance;
let isRefreshing = false;
let failedQueue: {
  resolve: (value: unknown) => void;
  reject: (reason?: ErrorType) => void;
}[] = [];

const processQueue = (error: ErrorType, token = null) => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });

  failedQueue = [];
};

// API Config
export const APIConfigNoToken = (baseUrl?: string) => {
  service = axios.create({
    baseURL: baseUrl ? baseUrl : import.meta.env.REACT_APP_API_URL,
    responseType: 'json',
  });
  service.interceptors.response.use(handleSuccess, handleError);
  return service;
};

export const getAuthorizationHeaderFormData = () => {
  const headers = {
    Authorization: `Bearer ${getAccessToken()}`,
    // Accept: "text/plain, */*",
    'Content-': 'multipart/form-data',
  };
  return headers;
};

export const APIConfig = (baseUrl?: string, config?: AxiosRequestConfig & { signal?: AbortSignal }) => {
  service = axios.create({
    baseURL: baseUrl ? baseUrl : import.meta.env.REACT_APP_API_URL,
    responseType: 'json',
    ...config,
    headers: {
      Authorization: `Bearer ${getAccessToken()}`,
      // Accept: "text/plain, */*",
      ...config?.headers,
      "ngrok-skip-browser-warning": "69420",
    },
  });
  service.interceptors.response.use(handleSuccess, handleError);
  return service;
};

export const APIBigConfig = (baseUrl?: string, config?: AxiosRequestConfig & { signal?: AbortSignal }) => {
  service = axios.create({
    baseURL: baseUrl ? baseUrl : import.meta.env.REACT_APP_MAIN_APP_URL + '/api/v1',
    responseType: 'json',
    ...config,
    headers: {
      Authorization: `Bearer ${getAccessToken()}`,
      // Accept: "text/plain, */*",
      ...config?.headers,
    },
  });
  service.interceptors.response.use(handleSuccess, handleError);
  return service;
};

const refreshToken = (err: AxiosError) => {
  const originalRequest = err.config;
  if (isRefreshing) {
    return new Promise(function (resolve, reject) {
      failedQueue.push({ resolve, reject });
    })
      .then((token) => {
        if (originalRequest && originalRequest.headers) {
          originalRequest.headers.Authorization = 'Bearer ' + token;
        }
        if (originalRequest) {
          return axios(originalRequest);
        }
        return Promise.reject(new Error('Original request is undefined'));
      })
      .catch((err) => {
        return Promise.reject(err);
      });
  }

  return new Promise(function (resolve, reject) {
    const refreshToken = getRefreshToken();
    if (refreshToken) {
      isRefreshing = true;
      const newService = axios.create({
        baseURL: MAIN_APP_URL,
        responseType: 'json',
      });
      return newService
        .get(ENDPOINTS.auth.refresh, {
          headers: {
            'refresh-token': refreshToken,
          },
        })
        .then(async ({ data }) => {
          const {
            data: { access_token },
          } = data;

          setStorage(ACCESS_TOKEN, access_token);

          service.defaults.headers['Authorization'] = `Bearer ${access_token}`;
          if (originalRequest && originalRequest.headers) {
            originalRequest.headers.Authorization = 'Bearer ' + access_token;
          }
          processQueue(null, access_token);
          if (originalRequest) resolve(axios(originalRequest));
        })
        .catch((err) => {
          processQueue(err, null);
          if (document.location.pathname !== '/login') {
            resetStore();
          }
          reject(err);
        })
        .then(() => {
          isRefreshing = false;
        });
    } else {
      if (err.config?.baseURL === '/users/token/') {
        toast({
          status: 'error',
          description: VALIDATION_MESSAGE.CHECK_INFO_PLEASE,
        });
      }
      reject(err);
      return;
    }
  });
};

const resetStore = () => {
  toast({
    status: 'warning',
    description: RESPONSE_MESSAGES.END_SESSION,
  });
  deleteAllStorages();
  redirectTo(document, '/login');
};

const handleError = (error: AxiosError<{ error: string }>) => {
  if (!error) {
    return Promise.reject(new TCancelRequest(error));
  } else if (error.message === 'Network Error') {
    toast({
      status: 'error',
      description: RESPONSE_MESSAGES.ERROR_CONNECTION_SERVER,
    });
    return Promise.reject(new CServerError(RESPONSE_MESSAGES.ERROR_CONNECTION_SERVER));
  } else if (!error?.response) {
    if (axios.isCancel(error)) {
      return Promise.reject(new TCancelRequest(error.toString()));
    }
    return Promise.reject(new TNetworkError(error));
  }

  const { status } = error.response;
  switch (status) {
    case ERROR_STATUS.UNAUTHORIZED: {
      //  1. Logout user if token refresh didn't work or user is disabled
      if (error.config?.baseURL === '/auth/refresh/') {
        return resetStore();
      }
      // 2. Try request again with new token
      return refreshToken(error);
    }
    case ERROR_STATUS.TOO_MANY_REQUESTS: {
      toast({
        status: 'warning',
        description: RESPONSE_MESSAGES.MULTI_REQUEST,
      });
      return Promise.reject(new CValidationError(RESPONSE_MESSAGES.MULTI_REQUEST));
    }
    case ERROR_STATUS.INTERNAL_SERVER_ERROR:
    case ERROR_STATUS.BAD_GATEWAY:
    case ERROR_STATUS.SERVICE_UNAVAILABLE:
    case ERROR_STATUS.GATEWAY_TIMEOUT: {
      toast({
        status: 'error',
        description: RESPONSE_MESSAGES.ERROR_CONNECTION_SERVER,
      });
      return Promise.reject(new CServerError(RESPONSE_MESSAGES.ERROR_CONNECTION_SERVER));
    }
    default: {
      toast({
        status: 'error',
        description:
          handleResponseErrorMessage(error?.response?.data.error) ||
          error.message ||
          RESPONSE_MESSAGES.ERROR,
      });
      return Promise.reject(error);
    }
  }
};

const handleSuccess = (response: AxiosResponse) => {
  const method = response.config.method;
  switch (method) {
    case 'post':
      break;
    case 'put':
    case 'patch':
      break;
    case 'delete':
      break;
    default:
      break;
  }
  return response;
};

const redirectTo = (document: Document, path: string) => {
  document.location.href = path;
};
