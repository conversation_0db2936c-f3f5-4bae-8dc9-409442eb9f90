/* eslint-disable @typescript-eslint/no-explicit-any */
import type { AxiosRequestConfig } from 'axios';
import { convertCancelToken, formatParamsUtil } from './params';
import { APIConfig } from '.';
import { TBaseResponse, TMultiResponse } from '@/types/ResponseApi';

const BASE_URL = import.meta.env.REACT_APP_API_URL;
const BASE_URL_LOCATION = import.meta.env.REACT_APP_MAIN_APP_URL;

const keyFilter: string[] = [];

export const getById = async <T>({
  endpoint = '',
  params,
}: {
  params?: any;
  endpoint?: string;
}) => {
  const { paramsNoneCancelToken } = convertCancelToken(params);
  try {
    const result = await APIConfig(BASE_URL).get<T>(endpoint, {
      params: paramsNoneCancelToken,
    });
    return result;
  } catch (error) {
    console.log('🚀 ~ getById ~ error:', error);
    throw Error(error?.toString());
  }
};

export const get = async <T>({
  endpoint = '',
  params,
  signal,
}: {
  params?: any;
  endpoint?: string;
  signal?: AbortSignal;
}) => {
  const { cancelToken, paramsNoneCancelToken } = convertCancelToken(params);
  const paramsUtil = formatParamsUtil(keyFilter, paramsNoneCancelToken);
  try {
    const result = await APIConfig(BASE_URL).get<TMultiResponse<T>>(endpoint, {
      signal,
      params: paramsUtil,
      cancelToken,
    });
    return result;
  } catch (error) {
    if (error instanceof Error) {
      return { data: null, error };
    }
    console.log('🚀 ~ get ~ error:', error);
    throw Error(error?.toString());
  }
};

export const create = async <T>({ endpoint = '', params }: { params?: any; endpoint?: string }) => {
  try {
    const result = await APIConfig(BASE_URL).post<T>(endpoint, params);
    return result;
  } catch (error) {
    console.log('🚀 ~ create ~ error:', error);
    return { data: null, error };
  }
};

export const update = async <T>({ endpoint = '', params }: { params?: any; endpoint?: string }) => {
  try {
    const result = await APIConfig(BASE_URL).patch<T>(endpoint, params);
    return result;
  } catch (error) {
    console.log('🚀 ~ update ~ error:', error);
    return { data: null, error };
  }
};

export const remove = async <T>({
  endpoint = '',
  config,
}: {
  endpoint?: string;
  config?: AxiosRequestConfig<any>;
}) => {
  try {
    const result = await APIConfig(BASE_URL).delete<TBaseResponse<T>>(endpoint, {
      ...config,
    });

    return { data: result.status };
  } catch (error) {
    return { data: null, error };
  }
};

const removeById = async ({
  endpoint,
  config,
}: {
  endpoint: string;
  config?: AxiosRequestConfig<any>;
}) => {
  try {
    await APIConfig(BASE_URL).delete<TBaseResponse<any>>(endpoint, {
      ...config,
    });
    return { data: 'success', code: 204 };
  } catch (error) {
    return { data: null, error };
  }
};

export const getBig360 = async <T>({ endpoint = '', params }: { params?: any; endpoint?: string }) => {
  const { cancelToken, paramsNoneCancelToken } = convertCancelToken(params);
  const paramsUtil = formatParamsUtil(keyFilter, paramsNoneCancelToken);
  try {
    const result = await APIConfig(BASE_URL_LOCATION).get<TMultiResponse<T>>(endpoint, {
      params: paramsUtil,
      cancelToken,
    });
    return result;
  } catch (error) {
    if (error instanceof Error) {
      return { data: null, error };
    }
    console.log('🚀 ~ get ~ error:', error);
    throw Error(error?.toString());
  }
};

export const apiHelper = {
  update,
  remove,
  get,
  create,
  getById,
  removeById,
};
