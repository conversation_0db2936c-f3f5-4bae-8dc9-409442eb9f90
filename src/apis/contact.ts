import { APIConfig } from '.';
import { ENDPOINTS } from './endpoints';

const uploadContact = async ({
  data,
  segment,
}: {
  data: { [key: string]: unknown }[];
  segment?: string;
}) => {
  const res = await APIConfig().post(ENDPOINTS.upload_contact, data, {
    params: segment && { segment },
  });
  return res;
};

const createNote = async (data: { note: string; contact_id: string }) => {
  const res = await APIConfig().post(ENDPOINTS.note.create(data.contact_id), data, {
    params: { contact_id: data.contact_id },
  });
  return res;
};
const updateNote = async (data: { note: string; contact_id: string; id: string }) => {
  const res = await APIConfig().patch(
    ENDPOINTS.note.update(data.contact_id, data.id),
    {
      note: data.note,
      contact_id: data.contact_id,
    },
    {
      params: { id: data.id, contact_id: data.contact_id },
    },
  );
  return res;
};

export const contactApis = {
  uploadContact,
  createNote,
  updateNote,
};
