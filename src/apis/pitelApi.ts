const baseUrl = 'https://pitel04-api.tel4vn.com';

export const keyFilter: string[] = ['type'];

export const get = async ({
  endpoint = '',
  version = 'v3',
  pitelToken,
  // hotline,
  ext,
  phone,
}: {
  endpoint?: string;
  version?: string;
  pitelToken?: string;
  hotline?: string;
  ext?: number;
  phone: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
}): Promise<any> => {
  // if (!hotline || !ext || !pitelToken || !phone) {
  if (!ext || !pitelToken || !phone) {
    return;
  }
  try {
    let paramsURL = new URLSearchParams();
    paramsURL.append('ext', `${ext}`);
    paramsURL.append('phone', phone);
    paramsURL.append('auto_answer', 'true');
    // paramsURL.append('hotline', hotline);

    const res = await fetch(`${baseUrl}/${version}/${endpoint}?${paramsURL}`, {
      method: 'GET',
      headers: new Headers({ Authorization: `Bearer ${pitelToken}` }),
    });
    return res.json();
  } catch (error: unknown) {
    return {
      data: undefined,
      results: [],
      error,
    };
  }
};

export const pitelApi = {
  get,
};
