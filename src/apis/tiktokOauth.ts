import { APIConfig } from '@/apis/index';
import { ENDPOINTS } from '@/apis/endpoints';

const getTiktokUser = async ({ signal }: { signal: AbortSignal }) => {
  const res = await APIConfig().get(ENDPOINTS.tiktok[''], {
    signal,
  });
  return res.data;
};

const selectTiktokAdsAccount = async (ad_account_id: string) => {
  const res = await APIConfig().post(
    ENDPOINTS.tiktok.ad_account,
    {},
    {
      withCredentials: true,
      params: { ad_account_id },
    },
  );
  return res.data;
};

const getTiktokOauthUrl = async (redirect_path: string) => {
  const res = await APIConfig().get(ENDPOINTS.tiktok.authLink, {
    withCredentials: true,
    params: { redirect_path },
  });
  return res.data;
};

const disconnect = async () => {
  const res = await APIConfig().post(ENDPOINTS.tiktok.logout);
  return res.data;
};

const uploadAudience = async (payload: { audience_name: string; segment_id: string }) => {
  const res = await APIConfig().post(ENDPOINTS.tiktok.largeUpload, payload);
  console.log('largeUpload', { res });
  return res.data;
};

export const tiktokOauthApi = {
  getTiktokOauthUrl,
  selectTiktokAdsAccount,
  getTiktokUser,
  disconnect,
  uploadAudience,
};
