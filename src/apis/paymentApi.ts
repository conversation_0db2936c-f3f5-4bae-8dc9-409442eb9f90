import { APIConfig } from '@/apis/index';
import { ICretePaymentRequest } from '@/types/payment';

const MAIN_APP_URL = import.meta.env.REACT_APP_MAIN_APP_URL;

const createPaymentLink = async <T>(payload: ICretePaymentRequest) => {
  try {
    const response = await APIConfig(`${MAIN_APP_URL}`).post<T>(`/api/v1/payment/payment-links/`, {
      ...payload,
      currency: 'usd',
    });
    return response.data;
  } catch (error) {
    return { data: null, error };
  }
};

export const paymentAPI = {
  createPaymentLink,
};
