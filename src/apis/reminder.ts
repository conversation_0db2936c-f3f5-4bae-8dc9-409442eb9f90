import { TReminder } from '@/types/reminder';
import { APIConfig } from '.';
import { ENDPOINTS } from './endpoints';

const create = async (payload: TReminder) => {
  const res = await APIConfig().post(ENDPOINTS.reminder, payload);
  return res.data.data;
};
const remove = async (id: string) => {
  const res = await APIConfig().delete(ENDPOINTS.reminder + `${id}/`);
  return res.data.data;
};
const update = async (payload: TReminder) => {
  const res = await APIConfig().patch(ENDPOINTS.reminder + `${payload.contact_id}/`, payload);
  return res.data.data;
};

export const reminderApi = {
  create,
  update,
  remove,
};
