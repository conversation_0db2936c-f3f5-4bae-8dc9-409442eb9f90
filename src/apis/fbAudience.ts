import { TCampaign, TCampaignResponse } from '@/types/audience';
import { APIConfig } from '.';
import { ENDPOINTS } from './endpoints';
import { AxiosResponse } from 'axios';
import { TFacebookLogData, TFacebookLogResponse } from '@/types/facebook';

const getAudience = async (
  payload: {
    page?: number;
    limit?: number;
    search?: string;
  },
  signal?: AbortSignal,
): Promise<TFacebookLogData> => {
  const res = await APIConfig().get(ENDPOINTS.fb.log, {
    signal: signal,
    params: {
      ...payload,
      order_by: '-date_created',
    },
  });
  return res.data.data;
};

const getCampaign = async (
  payload: {
    page?: number;
    limit?: number;
    search?: string;
  },
  signal?: AbortSignal,
): Promise<TCampaignResponse> => {
  const res = await APIConfig().get(ENDPOINTS.fb.campaign, {
    signal: signal,
    params: {
      ...payload,
      order_by: '-date_created',
    },
  });
  return res.data;
};

const updateCampaign = async (campaign: Partial<TCampaign> = {}) => {
  const { daily_budget, status, id } = campaign;
  const res = await APIConfig().post(`${ENDPOINTS.fb.campaign}${id}/`, {
    daily_budget,
    status,
  });
  return (res.data as AxiosResponse)?.status === 200;
};

const addAudience = async (payload: {
  audience_name: string;
  segment_id?: string;
  file?: File;
}) => {
  const formData = new FormData();
  formData.append('audience_name', payload.audience_name);

  if (payload.segment_id) {
    formData.append('segment_id', payload.segment_id.toString());
  }

  if (payload.file) {
    formData.append('file', payload.file);
  }

  const res = await APIConfig().post(ENDPOINTS.fb.push_custom_audience, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return res.data;
};

const getLog = async (
  payload: {
    page?: number;
    limit?: number;
    status?: string;
  },
  signal?: AbortSignal,
): Promise<TFacebookLogResponse> => {
  const res = await APIConfig().get(ENDPOINTS.fb.log, {
    signal: signal,
    params: {
      ...payload,
    },
  });
  return res.data.data;
};

export const fbAudienceApi = {
  getAudience,
  addAudience,
  getCampaign,
  updateCampaign,
  getLog,
};
