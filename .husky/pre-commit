#!/bin/sh

# <PERSON><PERSON><PERSON> tra lỗi TypeScript
echo "Running TypeScript checks..."
yarn tsc --noEmit --skipLibCheck
if [ $? -ne 0 ]; then
  echo "TypeScript found errors. Commit aborted."
  exit 1
fi

# <PERSON>ể<PERSON> tra lỗi ESLint
echo "Running ESLint checks..."
# FILES=$(git diff --cached --name-only --diff-filter=ACM | grep '\.js\|\.jsx\|\.ts\|\.tsx$')
FILES=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(js|jsx|ts|tsx)$') | xargs yarn eslint --max-warnings=0
if [ -n "$FILES" ]; then
  echo "Running ESLint checks on the following files:"
  echo "$FILES"

  # Chạy ESLint
  echo "$FILES" | xargs yarn eslint --max-warnings=0
  if [ $? -ne 0 ]; then
    echo "ESLint found errors. Commit aborted."
    exit 1
  fi
fi
echo "All checks passed. Proceeding with commit."
exit 0