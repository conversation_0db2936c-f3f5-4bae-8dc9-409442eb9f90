apiVersion: apps/v1
kind: Deployment
metadata:
  name: crm-fe-dev
  namespace: dev-big360
  labels:
    app: crm-fe-dev
spec:
  replicas: 1
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  selector:
    matchLabels:
      app: crm-fe-dev
  template:
    metadata:
      labels:
        app: crm-fe-dev
    spec:
      containers:
        - name: crm-fe-dev
          image: <NGINX_IMAGE>
          imagePullPolicy: IfNotPresent
          resources:
            requests:
              cpu: 100m
              memory: 150Mi
            limits:
              cpu: 150m
              memory: 225Mi
          livenessProbe:
            tcpSocket:
              port: 80
            initialDelaySeconds: 5
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 3
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 5
            timeoutSeconds: 2
            successThreshold: 1
            failureThreshold: 3
            periodSeconds: 10
          ports:
            - containerPort: 80
      imagePullSecrets:
        - name: registry-skylink
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: crm-fe-dev-service
  namespace: dev-big360
spec:
  selector:
    app: crm-fe-dev
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: ClusterIP
