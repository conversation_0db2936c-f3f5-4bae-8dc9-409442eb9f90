apiVersion: apps/v1
kind: Deployment
metadata:
  name: big360-crm-fe-prod
  namespace: prod-big360
  labels:
    app: big360-crm-fe-prod
spec:
  replicas: 2
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  selector:
    matchLabels:
      app: big360-crm-fe-prod
  template:
    metadata:
      labels:
        app: big360-crm-fe-prod
    spec:
      containers:
        - name: big360-crm-fe-prod
          image: <NGINX_IMAGE>
          imagePullPolicy: IfNotPresent
          resources:
            requests:
              cpu: 100m
              memory: 150Mi
            limits:
              cpu: 150m
              memory: 225Mi
          livenessProbe:
            tcpSocket:
              port: 80
            initialDelaySeconds: 5
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 3
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 5
            timeoutSeconds: 2
            successThreshold: 1
            failureThreshold: 3
            periodSeconds: 10
          ports:
            - containerPort: 80
      imagePullSecrets:
        - name: registry-skylink
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: big360-crm-fe-prod-service
  namespace: prod-big360
spec:
  selector:
    app: big360-crm-fe-prod
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: ClusterIP
